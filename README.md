# RiderService SDK

This SDK provides functionalities related to rider services.

## Modules

- `app`: Contains the main application.
- `riderservice`: Contains the core service logic.

## 连接分析系统

### 概述
SDK集成了统一的连接分析系统，用于监控和分析所有连接相关的活动，包括BLE、WiFi和TCP连接。

### 核心组件
- **ConnectionAnalytics**: 统一的连接分析管理器
- **ConnectionMetrics**: 连接指标数据类
- **AnalyticsListener**: 分析事件监听器接口

### 日志标签标准化
所有连接分析日志统一使用 `CONNECTION_ANALYTICS` 标签，格式为：
```
CONNECTION_ANALYTICS: [连接类型]: [事件描述] ::[时间戳]
```

### 使用示例
```kotlin
// 记录连接开始
val sessionId = ConnectionAnalytics.recordConnectionStart(ConnectionType.BLE, deviceAddress)

// 记录连接成功
ConnectionAnalytics.recordConnectionSuccess(sessionId, connectionInfo)

// 记录连接失败
ConnectionAnalytics.recordConnectionFailure(sessionId, error)

// 使用专用日志函数
logBleConnection("Device connected", deviceAddress)
logWifiConnection("Connected to network", ssid, "AP Mode")
```

## 连接时间监控系统（仅Debug模式）

### 概述
SDK集成了简单的连接时间监控系统，仅在Debug模式下工作，每次连接完成后立即将分析数据存入app的cache目录。

### 核心功能
- **实时记录**: 每次连接完成后立即保存到cache目录
- **Debug模式限制**: 只在BuildConfig.DEBUG为true时工作
- **原始数据存储**: 保存每次连接的原始数据，不计算平均速度
- **Cache目录存储**: 数据自动保存到app的cache目录

### 使用示例
```kotlin
// 初始化连接时间记录器（在Application中调用）
connectionManager.initDebugLogger(this)

// 手动生成连接时间报表
connectionManager.generateConnectionTimeReport()

// 获取当前记录数量
val recordCount = connectionManager.getConnectionTimeRecordCount()

// 清除连接时间记录
connectionManager.clearConnectionTimeRecords()
```

### 数据文件示例

#### connection_records.txt（实时记录文件）
```
2024-01-01 12:00:01|BLE|AA:BB:CC:DD:EE:FF|1250ms|SUCCESS
2024-01-01 12:00:05|WIFI_AP|MyWiFi|3200ms|SUCCESS
2024-01-01 12:00:10|BLE|AA:BB:CC:DD:EE:FF|2100ms|FAILED ERROR:Connection timeout
2024-01-01 12:00:15|WIFI_AP|MyWiFi|2800ms|SUCCESS
...
```

#### connection_times_report.txt（汇总报表）
```
=== 连接记录报表 ===
生成时间: 2024-01-01 12:00:00
总记录数: 25

【BLE 连接】
  总次数: 15
  成功: 13
  失败: 2

【WIFI_AP 连接】
  总次数: 10
  成功: 8
  失败: 2

=== 详细记录 ===
2024-01-01 12:00:01|BLE|AA:BB:CC:DD:EE:FF|1250ms|SUCCESS
2024-01-01 12:00:05|WIFI_AP|MyWiFi|3200ms|SUCCESS
...
```

### 特性说明
- **实时保存**: 每次连接完成立即保存到cache文件
- **原始数据**: 保存每次连接的原始数据，不进行统计计算
- **文件位置**:
  - 实时记录: `{app_cache_dir}/connection_records.txt`
  - 汇总报表: `{app_cache_dir}/connection_times_report.txt`
- **Debug限制**: 只在Debug构建中工作，Release版本不会记录任何数据
- **数据格式**: 使用管道符(|)分隔的简单文本格式，便于解析

## Usage

Further details on usage will be provided here.

## Key Parameters

Details about key parameters for configuring the SDK will be added here.