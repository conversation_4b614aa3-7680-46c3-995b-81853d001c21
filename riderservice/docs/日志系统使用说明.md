# RiderService 统一日志管理系统

## 概述

为了更好地管理项目中的日志输出，我们创建了一个统一的日志管理系统。这个系统可以控制除BLE模块外的所有日志输出，提供了灵活的开启/关闭和级别设置功能。

## 特性

- ✅ 统一管理除BLE模块外的所有日志
- ✅ 支持全局开启/关闭日志
- ✅ 支持设置全局日志级别
- ✅ 支持为特定标签单独设置开关和级别
- ✅ 默认开启所有日志
- ✅ 线程安全
- ✅ 不影响BLE模块的日志输出

## 快速开始

### 1. 基本使用

```kotlin
import com.link.riderservice.utils.*

class MyActivity {
    companion object {
        private const val TAG = "MyActivity"
    }
    
    private fun example() {
        // 方式1：使用全局函数
        logD(TAG, "这是一条调试信息")
        logI(TAG, "这是一条普通信息")
        logW(TAG, "这是一条警告信息")
        logE(TAG, "这是一条错误信息", exception)
        
        // 方式2：使用扩展函数（自动使用类名作为TAG）
        logD("这是一条调试信息")
        logI("这是一条普通信息")
        logW("这是一条警告信息")
        logE("这是一条错误信息", exception)
    }
}
```

### 2. 全局控制

```kotlin
// 禁用所有日志
LogManager.disableLog()

// 启用所有日志
LogManager.enableLog()

// 设置全局日志级别为ERROR（只显示错误日志）
LogManager.setLogLevel(LogLevel.ERROR)

// 设置全局日志级别为DEBUG（显示DEBUG及以上级别）
LogManager.setLogLevel(LogLevel.DEBUG)
```

### 3. 标签特定控制

```kotlin
// 为特定标签禁用日志
LogManager.disableLogForTag("MyActivity")

// 为特定标签启用日志
LogManager.enableLogForTag("MyActivity")

// 为特定标签设置日志级别
LogManager.setLogLevelForTag("MyActivity", LogLevel.WARN)

// 移除特定标签的设置（恢复全局设置）
LogManager.removeTagSettings("MyActivity")

// 清除所有标签设置
LogManager.clearAllTagSettings()
```

### 4. 应用初始化时配置

```kotlin
class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        
        // 根据构建类型配置日志
        if (BuildConfig.DEBUG) {
            // 开发环境：启用所有日志
            LogManager.enableLog()
            LogManager.setLogLevel(LogLevel.VERBOSE)
        } else {
            // 生产环境：只显示警告和错误
            LogManager.enableLog()
            LogManager.setLogLevel(LogLevel.WARN)
        }
    }
}
```

## 代码迁移指南

### 从原始Android Log迁移

**原始代码：**
```kotlin
import android.util.Log

private const val TAG = "MyClass"

Log.d(TAG, "调试信息")
Log.i(TAG, "普通信息")
Log.w(TAG, "警告信息")
Log.e(TAG, "错误信息", throwable)
```

**迁移后：**
```kotlin
import com.link.riderservice.utils.*

private const val TAG = "MyClass"

logD(TAG, "调试信息")
logI(TAG, "普通信息")
logW(TAG, "警告信息")
logE(TAG, "错误信息", throwable)

// 或者使用扩展函数（推荐）
logD("调试信息")
logI("普通信息")
logW("警告信息")
logE("错误信息", throwable)
```

### 批量替换脚本

可以使用以下正则表达式进行批量替换：

1. 替换`import android.util.Log`
   - 查找：`import android.util.Log`
   - 替换：`import com.link.riderservice.utils.*`

2. 替换日志调用
   - 查找：`Log\.([vdiwea])\(`
   - 替换：`log${1.toUpperCase()}(`

## 日志级别说明

| 级别 | 值 | 描述 | 使用场景 |
|------|----|----- |----------|
| VERBOSE | 2 | 详细信息 | 开发调试时的详细信息 |
| DEBUG | 3 | 调试信息 | 开发调试时的一般信息 |
| INFO | 4 | 一般信息 | 正常运行时的重要信息 |
| WARN | 5 | 警告信息 | 潜在的问题或异常情况 |
| ERROR | 6 | 错误信息 | 错误和异常 |
| ASSERT | 7 | 断言 | 严重错误 |

## BLE模块日志

以下标签的日志不受此管理器控制，会正常输出：
- BleManager
- BleManagerHandler
- BleServerManager
- BluetoothLeScannerCompat
- ScanCallback
- ConnectionObserver
- BondingObserver

## API参考

### LogManager主要方法

#### 全局控制
- `enableLog()` - 启用日志
- `disableLog()` - 禁用日志
- `isLogEnabled()` - 检查是否启用
- `setLogLevel(level: LogLevel)` - 设置全局级别
- `getLogLevel()` - 获取当前级别

#### 标签控制
- `enableLogForTag(tag: String)` - 为标签启用日志
- `disableLogForTag(tag: String)` - 为标签禁用日志
- `setLogLevelForTag(tag: String, level: LogLevel)` - 设置标签级别
- `removeTagSettings(tag: String)` - 移除标签设置
- `clearAllTagSettings()` - 清除所有标签设置

### 扩展函数

#### 全局函数
- `logV(tag, message)` / `logV(tag, message, throwable)`
- `logD(tag, message)` / `logD(tag, message, throwable)`
- `logI(tag, message)` / `logI(tag, message, throwable)`
- `logW(tag, message)` / `logW(tag, message, throwable)`
- `logE(tag, message)` / `logE(tag, message, throwable)`

#### Any扩展函数
- `Any.logV(message)` / `Any.logV(message, throwable)`
- `Any.logD(message)` / `Any.logD(message, throwable)`
- `Any.logI(message)` / `Any.logI(message, throwable)`
- `Any.logW(message)` / `Any.logW(message, throwable)`
- `Any.logE(message)` / `Any.logE(message, throwable)`

## 注意事项

1. **性能考虑**：日志检查在输出前进行，避免了不必要的字符串拼接
2. **线程安全**：所有操作都是线程安全的
3. **BLE模块**：BLE相关的日志不受此系统控制，保持原有行为
4. **默认行为**：系统默认启用所有级别的日志
5. **构建优化**：建议在生产环境中设置较高的日志级别以减少日志输出

## 示例场景

### 场景1：开发调试
```kotlin
// 开启所有日志用于调试
LogManager.enableLog()
LogManager.setLogLevel(LogLevel.VERBOSE)
```

### 场景2：生产环境
```kotlin
// 只保留警告和错误日志
LogManager.enableLog()
LogManager.setLogLevel(LogLevel.WARN)
```

### 场景3：特定模块调试
```kotlin
// 全局只显示错误，但ConnectionManager显示所有
LogManager.setLogLevel(LogLevel.ERROR)
LogManager.setLogLevelForTag("ConnectionManager", LogLevel.VERBOSE)
```

### 场景4：关闭特定模块日志
```kotlin
// 关闭某个噪音较大的模块的日志
LogManager.disableLogForTag("NoisyModule")
``` 