# RiderService 统一日志管理系统架构说明

## 系统概述

为了更好地管理 RiderService 项目中的日志输出，我们设计了一个统一的日志管理系统。该系统提供了灵活的日志控制功能，支持全局和细粒度的日志管理，同时保持了与现有 BLE 模块日志系统的兼容性。

## 设计目标

1. **统一管理**：除 BLE 模块外的所有日志都通过统一系统管理
2. **灵活控制**：支持全局和标签级别的开关控制
3. **级别设置**：支持不同的日志级别设置
4. **默认开启**：默认情况下所有日志都是开启的
5. **线程安全**：支持多线程环境下的安全使用
6. **性能优化**：避免不必要的字符串拼接和处理
7. **向后兼容**：不影响现有 BLE 模块的日志输出

## 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                        应用层                                │
├─────────────────────────────────────────────────────────────┤
│  LogApi (对外接口)          LogExtensions (扩展函数)        │
├─────────────────────────────────────────────────────────────┤
│                      LogManager (核心管理器)                │
├─────────────────────────────────────────────────────────────┤
│  Logger (接口)              LogLevel (枚举)                │
├─────────────────────────────────────────────────────────────┤
│                    Android Log (系统日志)                   │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. LogLevel (日志级别枚举)
- **路径**: `com.link.riderservice.utils.LogLevel`
- **作用**: 定义日志级别常量
- **级别**: VERBOSE(2) → DEBUG(3) → INFO(4) → WARN(5) → ERROR(6) → ASSERT(7)

### 2. Logger (日志接口)
- **路径**: `com.link.riderservice.utils.Logger`
- **作用**: 定义标准的日志记录接口
- **方法**: v, d, i, w, e (每个都有带异常的重载版本)

### 3. LogManager (核心管理器)
- **路径**: `com.link.riderservice.utils.LogManager`
- **作用**: 实现日志管理的核心逻辑
- **特性**:
  - 全局日志开关控制
  - 全局日志级别设置
  - 标签特定的开关和级别控制
  - BLE 模块排除机制
  - 线程安全实现

### 4. LogExtensions (扩展函数)
- **路径**: `com.link.riderservice.utils.LogExtensions`
- **作用**: 提供便捷的日志记录方法
- **功能**:
  - 全局日志函数 (logD, logI, logW, logE 等)
  - Any 类扩展函数 (自动使用类名作为 TAG)

### 5. LogApi (对外接口)
- **路径**: `com.link.riderservice.api.LogApi`
- **作用**: 为外部应用提供日志控制接口
- **特性**:
  - Java 兼容性 (@JvmStatic)
  - 便捷方法 (enableVerboseMode, enableProductionMode 等)

## 工作流程

### 1. 系统初始化
```kotlin
// 在 RiderService.init() 中
private fun initLogSystem() {
    if (BuildConfig.DEBUG) {
        LogManager.enableLog()
        LogManager.setLogLevel(LogLevel.VERBOSE)
    } else {
        LogManager.enableLog()
        LogManager.setLogLevel(LogLevel.WARN)
    }
}
```

### 2. 日志记录流程
```
应用代码调用
    ↓
logD("TAG", "message")
    ↓
LogManager.d(tag, message)
    ↓
shouldLog(tag, LogLevel.DEBUG)
    ↓
检查：BLE标签? → 直接输出
    ↓
检查：全局开关? → 是否启用
    ↓
检查：标签开关? → 标签特定设置
    ↓
检查：日志级别? → 级别比较
    ↓
Android Log.d(tag, message)
```

### 3. 控制流程
```
外部应用/内部代码
    ↓
LogApi.setLogLevel(LogLevel.INFO)
    ↓
LogManager.setLogLevel(level)
    ↓
更新 currentLogLevel
    ↓
影响后续日志输出判断
```

## 文件结构

```
riderservice/src/main/java/com/link/riderservice/
├── api/
│   ├── LogApi.kt                    # 对外日志控制API
│   └── RiderService.kt              # 主服务类（已集成日志系统）
├── utils/
│   ├── LogLevel.kt                  # 日志级别枚举
│   ├── Logger.kt                    # 日志接口定义
│   ├── LogManager.kt                # 核心日志管理器
│   ├── LogExtensions.kt             # 扩展函数
│   └── CrashHandler.kt              # 崩溃处理器（已迁移）
└── docs/
    ├── 日志系统使用说明.md           # 使用说明文档
    ├── 日志系统使用示例.kt           # 详细使用示例
    └── 日志系统架构说明.md           # 本文档
```

## 关键特性

### 1. BLE 模块排除机制
```kotlin
private val bleExcludeTags = setOf(
    "BleManager",
    "BleManagerHandler", 
    "BleServerManager",
    "BluetoothLeScannerCompat",
    "ScanCallback",
    "ConnectionObserver",
    "BondingObserver"
)

private fun isBleTag(tag: String): Boolean {
    return bleExcludeTags.any { tag.contains(it, ignoreCase = true) }
}
```

### 2. 线程安全设计
```kotlin
@Volatile
private var isLogEnabled: Boolean = true

@Volatile  
private var currentLogLevel: LogLevel = LogLevel.VERBOSE

private val tagEnabledMap = ConcurrentHashMap<String, Boolean>()
private val tagLevelMap = ConcurrentHashMap<String, LogLevel>()
```

### 3. 性能优化
```kotlin
private fun shouldLog(tag: String, level: LogLevel): Boolean {
    // 快速路径：BLE 标签直接通过
    if (isBleTag(tag)) return true
    
    // 快速路径：全局禁用直接返回
    if (!isLogEnabled) return false
    
    // 其他检查...
}
```

## 使用模式

### 1. 开发环境配置
```kotlin
LogApi.enableVerboseMode()  // 显示所有日志
```

### 2. 生产环境配置
```kotlin
LogApi.enableProductionMode()  // 只显示警告和错误
```

### 3. 调试特定模块
```kotlin
LogApi.setLogLevel(LogLevel.ERROR)  // 全局只显示错误
LogApi.setLogLevelForTag("NetworkManager", LogLevel.DEBUG)  // 特定模块显示调试信息
```

### 4. 临时启用详细日志
```kotlin
LogApi.enableVerboseMode()
// 调试完成后
LogApi.enableProductionMode()
```

## 迁移指南

### 1. 导入替换
```kotlin
// 原来
import android.util.Log

// 现在
import com.link.riderservice.utils.*
```

### 2. 调用替换
```kotlin
// 原来
Log.d(TAG, "message")
Log.e(TAG, "error", exception)

// 现在
logD(TAG, "message")
logE(TAG, "error", exception)

// 或者使用扩展函数
logD("message")
logE("error", exception)
```

## 扩展性

### 1. 添加新的日志级别
在 `LogLevel` 枚举中添加新的级别常量。

### 2. 添加新的输出目标
实现新的 `Logger` 接口，可以输出到文件、网络等。

### 3. 添加日志格式化
在 `LogManager` 中添加格式化逻辑。

### 4. 添加日志过滤
扩展 `shouldLog` 方法的过滤逻辑。

## 最佳实践

1. **使用合适的日志级别**：
   - VERBOSE: 详细的调试信息
   - DEBUG: 一般的调试信息  
   - INFO: 重要的运行信息
   - WARN: 警告信息
   - ERROR: 错误信息

2. **使用扩展函数**：
   ```kotlin
   // 推荐：自动使用类名作为TAG
   logD("用户点击了按钮")
   
   // 而不是
   logD("MainActivity", "用户点击了按钮")
   ```

3. **在应用初始化时配置**：
   ```kotlin
   class MyApplication : Application() {
       override fun onCreate() {
           super.onCreate()
           configureLogging()
       }
   }
   ```

4. **为生产环境优化**：
   ```kotlin
   if (!BuildConfig.DEBUG) {
       LogApi.enableProductionMode()
   }
   ```

## 注意事项

1. **BLE 模块日志**：BLE 相关的日志不受此系统控制，保持原有行为
2. **性能影响**：日志检查在输出前进行，避免了不必要的字符串操作
3. **线程安全**：所有操作都是线程安全的，可以在多线程环境中使用
4. **默认行为**：系统默认启用所有级别的日志
5. **向后兼容**：现有代码可以逐步迁移，不会影响现有功能 