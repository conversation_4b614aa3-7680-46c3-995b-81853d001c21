# RiderService SDK WiFi AP模式重构工作量评估

## 1. 项目概述

### 1.1 重构目标
将RiderService SDK从WiFi P2P（点对点直连）模式改为WiFi AP（接入点）客户端模式，架构模式为：
- **仪表端（车机）**：作为WiFi热点AP + TCP服务器
- **手机端**：作为WiFi客户端 + TCP客户端

### 1.2 技术架构对比

| 项目 | 当前P2P模式 | 目标AP客户端模式 |
|------|-------------|------------------|
| **WiFi角色** | P2P Group Owner/Client | WiFi AP → 客户端连接 |
| **连接发现** | P2P设备发现 | BLE获取热点信息 |
| **网络建立** | P2P直连 | 标准WiFi连接 |
| **TCP角色** | 手机端TCP服务器 | 手机端TCP客户端 |
| **状态管理** | 11个P2P状态 | 7个简化状态 |
| **兼容性** | P2P API限制 | 标准WiFi API |

### 1.3 核心架构变更

```mermaid
graph LR
    subgraph "原架构 (P2P)"
        A1[SoftP2pManager] --> A2[P2P连接]
        A2 --> A3[TCP服务器]
    end
    
    subgraph "新架构 (AP客户端)"
        B1[WiFiClientManager] --> B2[WiFi客户端连接]
        B2 --> B3[TCP客户端连接]
    end
    
    A1 -.替换.-> B1
    A2 -.替换.-> B2
    A3 -.替换.-> B3
    
    style B1 fill:#e3f2fd
    style B2 fill:#f1f8e9
    style B3 fill:#fff3e0
```

## 2. 详细工作量估算

### 2.1 核心组件开发 (8-11人天)

| 子任务 | 工作内容 | 复杂度 | 估算(人天) |
|--------|----------|--------|------------|
| **WiFiClientManager.kt** | 新建WiFi客户端管理器 | 高 | 3-4 |
| - Android版本兼容 | API 23-34版本兼容处理 | 高 | 1-1.5 |
| - 网络扫描连接 | WiFi扫描和连接逻辑 | 中 | 1-1.5 |
| - 状态监控 | 网络状态回调处理 | 中 | 0.5-1 |
| **TcpClientConnection.kt** | 新建TCP客户端连接 | 中 | 1.5-2 |
| **WiFiClientManagerListener.kt** | 事件监听接口 | 低 | 0.5 |
| **NetworkPermissionHelper.kt** | 权限管理助手 | 中 | 1-1.5 |
| **重连机制** | 指数退避重连策略 | 中 | 1-1.5 |
| **错误处理** | 异常捕获和恢复 | 中 | 1-1.5 |

### 2.2 现有组件修改 (4-6人天)

| 子任务 | 修改内容 | 复杂度 | 估算(人天) |
|--------|----------|--------|------------|
| **DisplayNaviManager.kt** | 替换P2P为WiFi客户端逻辑 | 高 | 2-3 |
| **ConnectionManager.kt** | 更新连接管理和协议处理 | 中 | 1-1.5 |
| **RiderService.kt** | 修改WiFi信息处理逻辑 | 中 | 0.5-1 |
| **协议层适配** | WIFI_P2P → WIFI_AP协议更新 | 低 | 0.5-1 |

### 2.3 权限和配置 (1-2人天)

| 子任务 | 工作内容 | 复杂度 | 估算(人天) |
|--------|----------|--------|------------|
| **AndroidManifest.xml** | 添加网络权限配置 | 低 | 0.5 |
| **动态权限处理** | Android 6.0+权限申请 | 中 | 0.5-1 |
| **特殊权限处理** | Android 13+ NEARBY_WIFI_DEVICES | 中 | 0.5-1 |

### 2.4 测试验证 (4-6人天)

| 测试类型 | 测试内容 | 估算(人天) |
|----------|----------|------------|
| **单元测试** | WiFi连接、重连机制、权限检查 | 1.5-2 |
| **集成测试** | 完整连接流程、异常恢复 | 1.5-2 |
| **兼容性测试** | Android 6.0-14版本测试 | 1-2 |

### 2.5 文档和清理 (1-2人天)

| 子任务 | 工作内容 | 估算(人天) |
|--------|----------|------------|
| **API文档更新** | 接口文档和集成指南 | 0.5-1 |
| **代码清理** | 删除P2P相关文件 | 0.5-1 |

## 3. 总体工作量汇总

| 阶段 | 工作内容 | 估算范围(人天) | 推荐(人天) |
|------|----------|----------------|------------|
| **第一阶段** | 核心组件开发 | 8-11 | 10 |
| **第二阶段** | 现有组件修改 | 4-6 | 5 |
| **第三阶段** | 权限和配置 | 1-2 | 1.5 |
| **第四阶段** | 测试验证 | 4-6 | 5 |
| **第五阶段** | 文档和清理 | 1-2 | 1.5 |
| **总计** | **全部工作** | **18-27** | **23** |

## 4. 风险评估与应对

### 4.1 技术风险

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| **Android版本兼容性** | 高 | +2-4人天 | 分版本实现，充分测试验证 |
| **厂商定制问题** | 中 | +1-2人天 | 适配主流厂商，提供降级方案 |
| **网络权限限制** | 中 | +1-2人天 | 完善权限引导，用户教育 |
| **TCP连接稳定性** | 中 | +1-2人天 | 强化重连机制，连接监控 |

### 4.2 项目风险

| 风险项 | 风险等级 | 影响 | 应对措施 |
|--------|----------|------|----------|
| **仪表端配合问题** | 高 | +3-5人天 | 提前沟通协议，联调测试 |
| **需求变更** | 中 | +2-4人天 | 模块化设计，预留扩展性 |
| **测试环境不足** | 中 | +1-3人天 | 准备多种测试设备和环境 |

### 4.3 风险缓解后的工作量调整

考虑风险因素，建议工作量调整为：
- **保守估算**: 28-32人天
- **推荐估算**: 26-30人天
- **乐观估算**: 23-25人天

## 5. 实施建议

### 5.1 开发优先级

1. **高优先级**：WiFiClientManager核心功能
2. **中优先级**：TCP客户端连接、现有组件修改
3. **低优先级**：权限优化、错误处理完善

### 5.2 里程碑设置

| 里程碑 | 时间节点 | 交付物 | 验收标准 |
|--------|----------|--------|----------|
| **M1** | 5天后 | WiFi客户端基础功能 | 能连接指定热点 |
| **M2** | 10天后 | 完整连接流程 | 端到端数据传输 |
| **M3** | 15天后 | 异常处理和重连 | 稳定性测试通过 |
| **M4** | 20天后 | 兼容性验证 | 多版本设备测试 |

### 5.3 质量保证

- **代码Review**：关键组件双人Review
- **单元测试覆盖率**：核心逻辑>80%
- **集成测试**：模拟真实使用场景
- **性能测试**：连接速度和稳定性指标

## 6. 资源需求

### 6.1 人力配置

- **Android高级开发工程师** × 1人（负责核心架构和复杂组件）
- **Android开发工程师** × 1人（负责组件修改和集成）
- **测试工程师** × 0.5人（负责测试用例设计和执行）

### 6.2 设备需求

- **测试设备**：Android 6.0-14各版本手机2-3台
- **仪表设备**：支持WiFi热点的车机设备1-2台
- **网络环境**：WiFi测试环境，模拟各种网络条件

## 7. 成功标准

### 7.1 功能标准
- ✅ 手机能成功连接仪表WiFi热点
- ✅ TCP客户端能建立稳定数据连接
- ✅ 网络异常时能自动重连
- ✅ 支持Android 6.0-14版本

### 7.2 性能标准
- 📊 WiFi连接成功率 > 95%
- 📊 TCP连接建立时间 < 10秒
- 📊 网络重连时间 < 30秒
- 📊 内存使用增量 < 10MB

### 7.3 稳定性标准
- 🔄 连续运行24小时无崩溃
- 🔄 网络切换场景正常恢复
- 🔄 异常场景覆盖率 > 90%

## 8. 总结

基于详细的技术分析，WiFi AP客户端模式重构是一个中等复杂度的项目：

- **核心工作量**：23人天（推荐估算）
- **含风险缓冲**：26-30人天
- **预计工期**：4-5周（2人团队）
- **技术可行性**：高，基于标准Android API
- **业务价值**：显著提升连接稳定性和兼容性

建议采用迭代开发方式，优先实现核心功能，逐步完善异常处理和用户体验。 