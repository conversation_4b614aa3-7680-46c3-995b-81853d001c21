# SharedPreferences 优化方案

## 问题描述

原代码中多次重复调用 `getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)`，这种做法存在以下问题：

1. **性能问题**：每次都要重新获取 SharedPreferences 实例
2. **代码重复**：相同的代码在多个地方重复出现
3. **维护困难**：如果需要修改 SharedPreferences 的名称或配置，需要改动多个地方
4. **容易出错**：硬编码的字符串容易出现拼写错误

## 优化方案

### 方案1：创建 ConfigPreferences 管理类（推荐）

创建了一个专门的 `ConfigPreferences` 单例类来管理所有配置相关的 SharedPreferences 操作。

#### 优势：
- **单例模式**：保证全局只有一个 SharedPreferences 实例
- **类型安全**：提供了类型安全的 getter/setter 方法
- **统一管理**：所有配置操作都集中在一个类中
- **易于维护**：修改配置键名只需在一个地方改动

#### 使用方法：

```kotlin
// 在 RiderService 初始化时会自动创建 ConfigPreferences 实例
val configPrefs = RiderService.instance.getConfigPreferences()

// 保存配置
configPrefs.setBleName("device_name")
configPrefs.setBleAddress("device_address")
configPrefs.setWifiAddress("***********")
configPrefs.setWifiPort(30512)

// 读取配置
val bleName = configPrefs.getBleName()
val bleAddress = configPrefs.getBleAddress()
val wifiAddress = configPrefs.getWifiAddress()
val wifiPort = configPrefs.getWifiPort()

// 检查配置是否存在
if (configPrefs.containsBleAddress()) {
    // 执行相关逻辑
}

// 清除所有配置
configPrefs.clearAllConfig()
```

### 方案2：在 RiderService 中添加 SharedPreferences 属性

在 `RiderService` 类中添加 `ConfigPreferences` 实例，统一管理配置。

#### 修改内容：

1. **RiderService.kt**：
   - 添加 `mConfigPreferences` 属性
   - 在 `init()` 方法中初始化
   - 提供 `getConfigPreferences()` 方法
   - 简化 `delectConfig()` 方法

2. **ConnectionManager.kt**：
   - 使用 `RiderService.instance.getConfigPreferences()` 替换直接的 SharedPreferences 调用

3. **RiderBleManager.kt**：
   - 使用新的 ConfigPreferences 方法

4. **DisplayNaviManager.kt**：
   - 使用新的 ConfigPreferences 方法保存配置

## 性能对比

### 优化前：
```kotlin
// 每次都要重新获取 SharedPreferences 实例
val sharedPref = RiderService.instance.getApplication()
    .getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
val address = sharedPref.getString("wifi_address", "")
val port = sharedPref.getInt("wifi_port", 0)
```

### 优化后：
```kotlin
// 使用单例，只获取一次实例
val configPrefs = RiderService.instance.getConfigPreferences()
val address = configPrefs.getWifiAddress()
val port = configPrefs.getWifiPort()
```

## 受影响的文件

已优化的文件：
- `RiderService.kt`
- `ConnectionManager.kt` 
- `RiderBleManager.kt`
- `DisplayNaviManager.kt`

新增的文件：
- `ConfigPreferences.kt`

## 注意事项

1. **向后兼容性**：新的 ConfigPreferences 类仍然使用相同的 SharedPreferences 文件名和键名，保证数据的向后兼容
2. **线程安全**：ConfigPreferences 使用了双重检锁的单例模式，保证线程安全
3. **初始化顺序**：确保在使用 ConfigPreferences 之前已经调用了 `RiderService.init()`

## 扩展性

如果需要添加新的配置项，只需要在 `ConfigPreferences` 类中添加相应的常量和方法即可：

```kotlin
companion object {
    // 添加新的配置键
    private const val KEY_NEW_CONFIG = "new_config"
}

// 添加 getter/setter 方法
fun setNewConfig(value: String) {
    sharedPreferences.edit {
        putString(KEY_NEW_CONFIG, value)
    }
}

fun getNewConfig(): String? {
    return sharedPreferences.getString(KEY_NEW_CONFIG, null)
}
``` 