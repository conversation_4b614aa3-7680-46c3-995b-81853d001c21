// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_IBLUETOOTH_CALLBACKS_H
#define AUTOLINK_PROTOCOL_IBLUETOOTH_CALLBACKS_H

#include "util/common.h"

/**
 * This class defines the interfaces that are necessary to enable automatic
 * bluetooth pairing via the GAL protocol. You must subclass this class and
 * implement the methods to integrate with your platform. More details are
 * available in the documentation for class BluetoothEndpoint.
 */
class IBluetoothCallbacks {
public:
    virtual ~IBluetoothCallbacks() {}

    virtual int onChannelOpened() = 0;

    virtual bool discoverBluetoothService(string carAddress, uint32_t methodsBitmap) = 0;

    virtual void onPairingResponse(int32_t status, bool alreadyPaired) = 0;

    virtual void onAuthenticationData(string authData) = 0;

    virtual void onPhoneBluetoothStatusInquire() = 0;
};

#endif // AUTOLINK_PROTOCOL_IBLUETOOTH_CALLBACKS_H
