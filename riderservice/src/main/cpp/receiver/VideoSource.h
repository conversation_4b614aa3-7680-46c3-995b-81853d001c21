// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_VIDEO_SOURCE_H
#define AUTOLINK_PROTOCOL_VIDEO_SOURCE_H

#include "util/common.h"
#include "MediaSourceBase.h"
#include "IVideoSourceCallbacks.h"
#include "rtp/NetworkSession.h"
#include "rtp/RTPSender.h"

class VideoSource : public MediaSourceBase {
public:
    VideoSource(uint8_t id, MessageRouter *router, uint8_t priority, string &remoteHost)
            : MediaSourceBase(id, router),
              mLocalRTPPort(-1),
              mVideoTransportMode(Config_VideoTransportMode_VIDEO_TRANSPORT_MODE_NONE),
              mRemoteHost(remoteHost) {

    }

    VideoSource(uint8_t id, MessageRouter *router, uint8_t priority)
            : MediaSourceBase(id, router),
              mLocalRTPPort(-1),
              mVideoTransportMode(Config_VideoTransportMode_VIDEO_TRANSPORT_MODE_NONE) {

    }

    int routeMessage(uint8_t channelId, uint16_t type, const shared_ptr<IoBuffer> &msg);

    bool discoverService(const Service &srv);

    void registerCallbacks(const shared_ptr<IVideoSourceCallbacks> &callbacks) {
        mCallbacks = callbacks;
    }

    void sendVideoFocusRequest(int32_t channelId, uint32_t mode, uint32_t reason);

    void sendVideoOrientation(bool isLandscape);

    void sendDisplayAreaChangeResponse();

    int createRtpSession(string &remoteHost);

    virtual void sendData(uint64_t timeStamp, void *data, size_t len, int32_t flags);

protected:

    virtual void onChannelOpened(uint8_t channelId, uint32_t extraMessage);

    virtual int handleConfig(const Config &config);

    virtual int handleAck(const Ack &ack);

    virtual int handleVideoFocusNotif(const VideoFocusNotification &vfn);

    virtual int handleDisplayChange(const DisplayConfigurationChangeRequest &displayCR);

    virtual int handleStartResponse(StartResponse &response);

    virtual int handleStopResponse(StopResponse &response);


private:
    enum {
        kUDPPort = 1030,
        BUFFER_FLAG_CODEC_CONFIG = 10,
    };

    shared_ptr<IVideoSourceCallbacks> mCallbacks;
    shared_ptr<NetworkSession> mNetSession;
    shared_ptr<RTPSender> mRtpSender;
    int32_t mLocalRTPPort;
    shared_ptr<IoBuffer> mCSD0;
    Config_VideoTransportMode mVideoTransportMode;
    string mRemoteHost;
};

#endif //AUTOLINK_PROTOCOL_VIDEO_SOURCE_H
