// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_MEDIA_SOURCE_BASE_H
#define AUTOLINK_PROTOCOL_MEDIA_SOURCE_BASE_H

#include "util/common.h"
#include "ProtocolEndpointBase.h"

#define INVALID_SESSION_ID  (-1)

/**
 * Base class for all media sources. This is the code that handles most of
 * the state machine required for video and audio. The video and audio
 * source inherit from this and provide callbacks which get called when
 * messages arrive that need specific handling.
 */
class MediaSourceBase : public ProtocolEndpointBase {
public:
    MediaSourceBase(uint8_t id, MessageRouter *router) : ProtocolEndpointBase(id, router, false) {}

    virtual int routeMessage(uint8_t channelId, uint16_t type, const shared_ptr<IoBuffer> &msg);

    //virtual bool discoverService(const Service& sdr);
    void sendSetup(MediaCodecType type);

    void sendStart(uint32_t config_index, int32_t session_id, int32_t width, int32_t height);

    void sendStop();

protected:
    virtual void sendCodecConfig(void *config, size_t len);

    virtual void sendData(uint64_t timeStamp, void *data, size_t len, bool isIDR = false);

    virtual int handleConfig(const Config &config) = 0;

    virtual int handleAck(const Ack &ack) = 0;

    virtual int handleVideoFocusNotif(const VideoFocusNotification &vfn) = 0;

    virtual int handleStartResponse(StartResponse &response) = 0;

    virtual int handleStopResponse(StopResponse &response) = 0;

};

#endif // AUTOLINK_PROTOCOL_MEDIA_SOURCE_BASE_H
