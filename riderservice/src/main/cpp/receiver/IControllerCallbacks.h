// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_ICONTROLLER_CALLBACKS_H
#define AUTOLINK_PROTOCOL_ICONTROLLER_CALLBACKS_H

#include "util/common.h"

/**
 * This class represents a general set of callbacks that must be set up for the GAL receiver
 * library to be able to function properly.
 */
class IControllerCallbacks {
public:
    virtual ~IControllerCallbacks() {}

    /**
     * Called when an unrecoverable error has been encountered. The recommended course
     * of action is to reset the usb link at this point. A re-establishment of the
     * GAL connection may be attempted after that.
     *
     * The current set of error codes is:
     *  - STATUS_AUTHENTICATION_FAILURE if the SSL handshake fails.
     *  - STATUS_FRAMING_ERROR if an error occurs during communication with the
     *    MD. Examples of errors include (but are not limited to) IO errors and
     *    SSL decryption errors.
     */
    virtual void unrecoverableErrorCallback(MessageStatus err) = 0;

    /**
    *   if auth complete, this callback will be called.
    */
    virtual void authCompleteCallback() = 0;

    /**
     * Called when the other end pings us.
     * @param timestamp The (remote) timestamp of the request.
     * @param bugReport Should a bug report be saved away? The implementer can choose
     *        to ignore this if it is sent with high frequency. It is in the end, the
     *        implementers responsibility to ensure that the system cannot be DoS'd
     *        if too many requests for bug reports are received.
     */
    virtual void pingRequestCallback(int64_t timestamp, bool bugReport) = 0;

    /**
     * Called when the other end responds to our ping.
     * @param timestamp The timestamp we wrote on the original ping request.
     */
    virtual void pingResponseCallback(int64_t timestamp) = 0;

    /**
     * Called when ByeByeRequest is received from HU. After taking necessary steps,
     * phone side should send ByeByeResponse.
     * @param reason The reason for the disconnection request.
     */
    virtual void byeByeRequestCallback(ByeByeReason reason) = 0;

    /**
     * Called when ByeByeResponse is received from HU. Normally this is a reply for
     * ByeByeRequest message sent from HU.
     */
    virtual void byeByeResponseCallback() = 0;

    virtual void exitRequestCallback() = 0;

    /**
     * Called when a navigation focus notification is received from the HU.
     * @param type The type requested (can be NAV_FOCUS_NATIVE or NAV_FOCUS_PROJECTED).
     */
    //virtual void navFocusNotificationCallback(NavFocusType type) = 0;

    /**
     * Called when the audio focus notification is received from HU.
     * @param status Can be one of AUDIO_FOCUS_STATE_GAIN, AUDIO_FOCUS_STATE_GAIN_TRANSIENT,
     *        AUDIO_FOCUS_STATE_LOSS, AUDIO_FOCUS_STATE_LOSS_TRANSIENT_CAN_DUCK,
     *        AUDIO_FOCUS_STATE_LOSS_TRANSIENT,AUDIO_FOCUS_STATE_GAIN_MEDIA_ONLY,
     *        AUDIO_FOCUS_STATE_GAIN_TRANSIENT_GUIDANCE_ONLY
     * @param unsolicited Can be true or false. False when responding to an audio focus request.
     */
    //virtual void audioFocusNotificationCallback(AudioFocusStateType status, bool unsolicited) = 0;

    virtual void forceLandscapeRequestCallback(bool force) = 0;

    virtual void screenOrientationInquire() = 0;

    virtual void screenResolutionInquire() = 0;

    virtual void runningStateInquire() = 0;

    virtual void timeDateInquire() = 0;

    virtual void versionResponseCallback(uint16_t major, uint16_t minor) = 0;

    virtual void serviceDiscoveryResponseCallback(string &id, string &make, string &model,
                                                  string &year, string &huIc, string &huMake,
                                                  string &huModel, string &huSwBuild,
                                                  string &huSwVersion,
                                                  string &huSeries, string &huMuVersion,
                                                  int32_t checkSum) = 0;

    virtual void autoRotationRequest(bool autoed) = 0;

};


#endif // AUTOLINK_PROTOCOL_ICONTROLLER_CALLBACKS_H
