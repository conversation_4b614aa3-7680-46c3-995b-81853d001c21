// Copyright 2014 Google Inc. All Rights Reserved.

#ifndef AUTOLINK_PROTOCOL_GAL_RECEIVER_H
#define AUTOLINK_PROTOCOL_GAL_RECEIVER_H

#include "util/common.h"
#include "ChannelManager.h"
#include "Controller.h"
#include "MessageRouter.h"

/**
 * This class is the fundamental building block of the Google Automotive Link protocol.
 * <br>
 * Initializing the receiver library consists of the following steps.
 * <pre>
 *      GalReceiver galReceiver;
 *      // Create an instance of type IControllerCallbacks.
 *      galReceiver.init(controllerCallbacks);
 *      galReceiver.setIdentityInfo(...);
 *      galReceiver.setClientCreds(...);
 *      galReceiver.setDriverPosition(...);
 *      // Repeat this for every endpoint you want.
 *      // Create and start service. Look at the documentation for the following for details.
 *      // AudioSink
 *      // AudioSource
 *      // BluetoothEndpoint
 *      // InputSource
 *      // InstrumentClusterEndpoint
 *      // SensorSource
 *      // RadioEndpoint
 *      // VideoSink
 *      galReceiver.registerService()
 *      galReceiver.start();
 * </pre>
 * <br>
 * Under normal operation, you would normally have two threads one that reads the underlying
 * transport and submits data to the GAL receiver and another that reads data from the GAL
 * receiver and submits data to the underlying transport.
 * <br>
 * A typical transport to GAL loop may look like the pseudocode below. Note that this example
 * assumes that you are reading from a buffered transport, it might be necessary to add your
 * own buffering layer if the underlying transport does not support arbitrary sized reads.
 * <pre>
 *      uint8_t header[FRAME_HEADER_MIN_LENGTH];
 *      while (!stop) {
 *          readFromTransport(header, sizeof(header));
 *          int len = galReceiver.getAdditionalBytesToRead(header);
 *          memcpy(buf, header, sizeof(header);
 *          readFromTransport(buf + sizeof(header), len);
 *          mGalReceiver.queueIncoming(buf, len);
 *      }
 * </pre>
 * <br>
 * A typical GAL to transport loop may look like this:
 * <pre>
 *      IoBuffer buf;
 *      while (!stop) {
 *          galReceiver.getEncodedFrame(&buf);
 *          writeToTransport(buf.raw(), buf.size());
 *      }
 * </pre>
 * <br>
 * The shutdown sequence would typically look like this. The most common reason to tear down
 * would be a disconnection event either caused by an unplug or by a callback signalling an
 * unrecoverable error.
 * <pre>
 *      galReceiver.prepareShutdown(); // This will unblock any blocked threads.
 *      // Stop transport threads.
 *      galReceiver.shutdown();
 * </pre>
 */
class GalReceiver {
public:
    GalReceiver() : mController(&mMessageRouter) {}

    /**
     * Call this method to initialize the receiver library. This must be called before any other
     * methods on the library.
     */
    bool init(const shared_ptr<IControllerCallbacks> &controllerCallbacks);

    /**
     * Call this when you want to send out the VERSION_REQUEST message to the phone. All services
     * must be registered before this.
     */
    void start();

    /**
     * Call this method to unblock any threads that may be waiting on getEncodedFrame().
     */
    void prepareShutdown();

    /**
     * Call this method to shutdown the receiver. It is okay to call init again once this method
     * has returned.
     */
    void shutdown();

    /**
     * This method should be called when new data is available for processing. The data is copied
     * so it may be freed once this method returns.
     * @param raw The raw data that was received.
     * @param len The size of data in bytes.
     * @return STATUS_SUCCESS if the data was processed successfully, an approriate error code
     *         otherwise.
     */
    int queueIncoming(void *raw, size_t len);

    /**
     * Acquire a buffer that can be passed on to queueIncoming.
     * @return A shared_ptr to an IoBuffer of the requested size.
     */
    static shared_ptr<IoBuffer> allocateBuffer(size_t size);

    /**
     * This method should be called when there is new data available for processing. With this
     * method, you transfer ownership of the buffer to the receiver library so you should not
     * use it after calling this method.
     * @param buf The buffer containing the data.
     * @return STATUS_SUCCESS if the data was processed successfully, an approriate error code
     *         otherwise.
     */
    int queueIncoming(const shared_ptr<IoBuffer> &buf);

    /**
     * When data is available on the underlying transport, call this method with the first 4 bytes
     * to determine how many more bytes should be read to reach the end of the frame. The data
     * read along with these 4 bytes should be passed into queueIncoming subsequently.
     * @param buf A buffer with the data that belongs to the header.
     * @return How many more bytes should be read to get the complete message.
     */
    static int getAdditionalBytesToRead(unsigned char buf[FRAME_HEADER_MIN_LENGTH]);

    /**
     * Call this method to obtain a frame for transmission. Note that this method blocks until a
     * frame is actually available. The IoBuffer that is passed in will be populated with the
     * frame data.
     * @param buf The io buffer that should be populated with the frame data.
     * @return true on success, false otherwise.
     */
    bool getEncodedFrame(IoBuffer *buf);

    /**
     * Call this function to register a new 'Service' with the receiver. This allows the receiver
     * to advertise it in the ServiceDiscoveryResponse and for the phone to open channels to it.
     * @param endpoint The service to register.
     * @return true on success, false otherwise.
     */
    bool registerService(ProtocolEndpointBase *endpoint);

    /**
    *   set the phone's manfacturer
    */
    void setManufacturer(string &manufacturer) {
        mController.setManufacturer(manufacturer);
    }

    /**
    *   set the phone's model
    */
    void setModel(string &model) {
        mController.setModel(model);
    }

    /**
    *   set the phone's android version
    */
    void setVersion(string &version) {
        mController.setVersion(version);
    }

    /**
    *   set the phone name
    */
    void setPhoneName(string &name) {
        mController.setPhoneName(name);
    }

    /**
    *   set the phone's screen resolution
    */
    void setScreenResolution(uint32_t width, uint32_t height) {
        mController.setScreenResolution(width, height);
    }

    /**
     * Ping the other side to see if it is alive. This is mostly for diagnosing connectivity issues.
     * @param timestamp The local timestamp. This is repeated by the other side in the response
     *        so it can be used to compute round trip delays.
     * @param bugReport Request the other end to generate a bug report locally. The other end may
     *        choose to ignore this request.
     */
    void sendPingRequest(int64_t timestamp, bool bugReport) {
        mController.sendPingRequest(timestamp, bugReport);
    }

    MessageRouter *messageRouter() { return &mMessageRouter; }

    /**
     * Send ByeByeRequest to phone.
     */
    void sendByeByeRequest(int32_t code) {
        mController.sendByeByeRequest(code);
    }

    /**
     * Send ByeByeResponse as a response to ByeByeRequest.
     */
    void sendByeByeResponse() {
        mController.sendByeByeResponse();
    }


    void sendExitResponse() {
        mController.sendExitResponse();
    }

    /**
     * request navigation focus request.
     * @param type Can be NAV_FOCUS_NATIVE or NAV_FOCUS_PROJECTED.
     */
    void sendNavFocusRequest(int32_t type) {
        mController.sendNavFocusRequest(type);
    }

    /**
    *  as reponse for ForceLandscapeRequest
    **/
    void sendForceLandscapeResponse(bool force) {
        mController.sendForceLandscapeResponse(force);
    }

    /**
    *  send the screen orientation status, may be used in two cases:
    *  1. the screen orientation has changed.
    *  2. as the response for ScreenOrientationInquire.
    **/
    void sendScreenOrientationNotifi(int orientation, int rotation) {
        mController.sendScreenOrientationNotifi(orientation, rotation);
    }


    void sendUpdateVehicleIdNotifi(const string &id) {
        mController.sendUpdateVehicleIdNotifi(id);
    }

    void sendRunningStateNotifi(int state) {
        mController.sendRunningStateNotifi(state);
    }

    void sendAutoRotationNotifi(bool isAutoed) {
        mController.sendAutoRotationNotifi(isAutoed);
    }

    void sendScreenResolutionNotification(int width, int height, bool isRequired) {
        mController.sendScreenResolutionNotification(width, height, isRequired);
    }


    void sendTimeDateNotification(int year, int month, int day, int hour, int minute, int second,
                                  int nanosecond, int week, int dayOfWeek) {
        mController.sendTimeDateNotification(year, month, day, hour, minute, second, nanosecond,
                                             week, dayOfWeek);
    }

private:
    MessageRouter mMessageRouter;
    ChannelManager mChannelManager;
    Controller mController;
};

#endif // AUTOLINK_PROTOCOL_GAL_RECEIVER_H
