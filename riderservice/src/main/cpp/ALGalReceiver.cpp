#include <jni.h>
#include "GalReceiver.h"
#include "ControllerCallbacks.h"

static jclass galReceiverClass;
static jfieldID fidNativeGalReceiver;

GalReceiver *getGalReceiver(JNIEnv *env, jobject jthis) {
    jlong nativeGalReceiver = env->GetLongField(jthis, fidNativeGalReceiver);
    return (GalReceiver *) nativeGalReceiver;
}

void setGalReceiver(JNIEnv *env, jobject jthis, GalReceiver *receiver) {
    env->SetLongField(jthis, fidNativeGalReceiver, (jlong) receiver);
}


extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeInit
        (JNIEnv *env, jobject jthis) {
    galReceiverClass = env->GetObjectClass(jthis);
    fidNativeGalReceiver = env->GetFieldID(galReceiverClass, "nativeInstance", "J");
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    if (galReceiver != nullptr) {
        jclass eclass = env->FindClass("java/lang/IllegalStateException");
        env->ThrowNew(eclass, "Already initialized.");
        return;
    }
    galReceiver = new GalReceiver();
    shared_ptr<IControllerCallbacks> controllerCallbacks(new ControllerCallbacks(env, jthis));
    galReceiver->init(controllerCallbacks);
    setGalReceiver(env, jthis, galReceiver);
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeStart
        (JNIEnv *env, jobject jthis) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    galReceiver->start();
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativePrepareShutdown
        (JNIEnv *env, jobject jthis) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    galReceiver->prepareShutdown();
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeShutdown
        (JNIEnv *env, jobject jthis) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    galReceiver->shutdown();
    delete galReceiver;
    setGalReceiver(env, jthis, nullptr);
}

extern "C" JNIEXPORT jint JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeQueueIncoming
        (JNIEnv *env, jobject jthis, jbyteArray buf, jint len) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    jbyte *cbuf = env->GetByteArrayElements(buf, nullptr);
    jsize jlen = env->GetArrayLength(buf);
    // TODO: Throw an error if jlen != len
    int ret = galReceiver->queueIncoming((unsigned char *) cbuf, (size_t) len) == STATUS_SUCCESS;
    env->ReleaseByteArrayElements(buf, cbuf, JNI_ABORT);
    return ret;
}

extern "C" JNIEXPORT jint JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeGetAdditionalBytesToRead
        (JNIEnv *env, jobject jthis, jbyteArray buf) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    if (galReceiver == nullptr) {
        return 0;
    }
    jbyte *cbuf = env->GetByteArrayElements(buf, nullptr);
    jsize len = env->GetArrayLength(buf);
    // TODO: Check the length and bail if length is < 4.
    int ret = GalReceiver::getAdditionalBytesToRead((unsigned char *) cbuf);
    env->ReleaseByteArrayElements(buf, cbuf, JNI_ABORT); // Abort means don't copy back.
    return ret;
}

extern "C" JNIEXPORT jint JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeGetEncodedFrame
        (JNIEnv *env, jobject jthis, jbyteArray buf) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    IoBuffer iobuffer;
    bool ret = galReceiver->getEncodedFrame(&iobuffer);
    if (ret) {
        env->SetByteArrayRegion(buf, 0, (jsize) iobuffer.size(), (const jbyte *) iobuffer.raw());
        return (jint) iobuffer.size();
    }
    return 0;
}

extern "C" JNIEXPORT jboolean JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeRegister
        (JNIEnv *env, jobject jthis, jlong protocolEndpoint) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    return (jboolean) galReceiver->registerService((ProtocolEndpointBase *) protocolEndpoint);
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeSetPhoneInfo
        (JNIEnv *env, jobject jthis, jobject phoneInfo) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    jclass phoneInfoClass = env->GetObjectClass(phoneInfo);

    jfieldID modelId = env->GetFieldID(phoneInfoClass, "model", "Ljava/lang/String;");
    auto model = (jstring) env->GetObjectField(phoneInfo, modelId);
    const char *modelutf = env->GetStringUTFChars(model, nullptr);
    string modelstr(modelutf);
    galReceiver->setModel(modelstr);

    jfieldID factureId = env->GetFieldID(phoneInfoClass, "manufacturer", "Ljava/lang/String;");
    auto facture = (jstring) env->GetObjectField(phoneInfo, factureId);
    const char *factureutf = env->GetStringUTFChars(facture, nullptr);
    string facturestr(factureutf);
    galReceiver->setManufacturer(facturestr);

    jfieldID versionId = env->GetFieldID(phoneInfoClass, "version", "Ljava/lang/String;");
    auto version = (jstring) env->GetObjectField(phoneInfo, versionId);
    const char *versionutf = env->GetStringUTFChars(version, nullptr);
    string versionstr(versionutf);
    galReceiver->setVersion(versionstr);

    jfieldID nameId = env->GetFieldID(phoneInfoClass, "name", "Ljava/lang/String;");
    auto name = (jstring) env->GetObjectField(phoneInfo, nameId);
    const char *nameutf = env->GetStringUTFChars(name, nullptr);
    string namestr(nameutf);
    galReceiver->setPhoneName(namestr);

    jfieldID widthId = env->GetFieldID(phoneInfoClass, "width", "I");
    jint width = env->GetIntField(phoneInfo, widthId);

    jfieldID heightId = env->GetFieldID(phoneInfoClass, "height", "I");
    jint height = env->GetIntField(phoneInfo, heightId);

    galReceiver->setScreenResolution((uint32_t) width, (uint32_t) height);

    env->ReleaseStringUTFChars(model, modelutf);
    env->ReleaseStringUTFChars(facture, factureutf);
    env->ReleaseStringUTFChars(version, versionutf);
    env->ReleaseStringUTFChars(name, nameutf);
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativePing
        (JNIEnv *env, jobject jthis, jlong timestamp, jboolean bugReport) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    galReceiver->sendPingRequest((int64_t) timestamp, bugReport);
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeSetNavFocus
        (JNIEnv *env, jobject jthis, jint type) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    galReceiver->sendNavFocusRequest(type);
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeSendByeByeRequest
        (JNIEnv *env, jobject jthis, jint reason) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    galReceiver->sendByeByeRequest(reason);
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeSendByeByeResponse
        (JNIEnv *env, jobject jthis) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    galReceiver->sendByeByeResponse();
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeSendExitResponse
        (JNIEnv *env, jobject jthis) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    galReceiver->sendExitResponse();
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeSendScreenOrientationNotifi
        (JNIEnv *env, jobject jthis, jint orientation, jint rotation) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    galReceiver->sendScreenOrientationNotifi(orientation, rotation);
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeSendUpdateVehicleIdNotifi
        (JNIEnv *env, jobject jthis, jstring id) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    const char *carId = env->GetStringUTFChars(id, nullptr);
    galReceiver->sendUpdateVehicleIdNotifi(carId);
    env->ReleaseStringUTFChars(id, carId);
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeSendRunningStateNotifi
        (JNIEnv *env, jobject jthis, jint state) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    galReceiver->sendRunningStateNotifi(state);
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeSendAutoRotationNotifi
        (JNIEnv *env, jobject jthis, jboolean isAutoed) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    galReceiver->sendAutoRotationNotifi(isAutoed);
}


extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_nativeSendScreenResolutionNotification
        (JNIEnv *env, jobject jthis, jint width, jint height, jboolean isRequired) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    galReceiver->sendScreenResolutionNotification(width, height, isRequired);
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_GalReceiver_sendTimeDateNotification
        (JNIEnv *env, jobject jthis, jint year, jint month, jint day, jint hour, jint minute,
         jint second,
         jint nanosecond, jint week, jint dayOfWeek) {
    GalReceiver *galReceiver = getGalReceiver(env, jthis);
    galReceiver->sendTimeDateNotification(year, month, day, hour, minute, second, nanosecond, week,
                                          dayOfWeek);

}


