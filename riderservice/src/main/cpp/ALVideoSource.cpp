// Copyright 2014 Google Inc. All Rights Reserved.

#include <jni.h>
#include "GalReceiver.h"
#include "VideoSource.h"
#include "util/shared_ptr.h"
#include "VideoSourceCallbacks.h"

static jclass videoSourceClass;
static jfieldID fidNativeVideoSource;
static jfieldID callback;
static jobject videosourcecallbacks;

VideoSource *getVideoSource(JNIEnv *env, jobject jthis) {
    jlong mNativeVideoSource = env->GetLongField(jthis, fidNativeVideoSource);
    return (VideoSource *) mNativeVideoSource;
}

void setVideoSource(JNIEnv *env, jobject jthis, VideoSource *receiver) {
    env->SetLongField(jthis, fidNativeVideoSource, (jlong) receiver);
}

extern "C" JNIEXPORT jint JNICALL
Java_com_link_riderservice_connection_display_protocol_project_VideoSource_nativeInit(
        JNIEnv *env,
        jobject jthis,
        jint id,
        jlong nativeGalReceiver,
        jboolean autoStart,
        jstring remoteHost) {
    videoSourceClass = env->GetObjectClass(jthis);
    fidNativeVideoSource = env->GetFieldID(videoSourceClass, "nativeInstance", "J");
    callback = env->GetFieldID(videoSourceClass, "mVideoListener",
                               "Lcom/link/riderservice/connection/display/protocol/project/VideoSourceCallbacks;");
    videosourcecallbacks = env->GetObjectField(jthis, callback);
    VideoSource *videoSource = getVideoSource(env, jthis);
    if (videoSource != nullptr) {
        jclass eclass = env->FindClass("java/lang/IllegalStateException");
        env->ThrowNew(eclass, "Already initialized.");
        return -1; // This return value is ignored.
    }

    auto *galReceiver = (GalReceiver *) nativeGalReceiver;
    if (remoteHost != nullptr) {
        const char *mRemoteHost = env->GetStringUTFChars(remoteHost, nullptr);
        string mRemoteHostStr(mRemoteHost);
        videoSource = new VideoSource(static_cast<uint8_t>(id), galReceiver->messageRouter(),
                                      (bool) autoStart, mRemoteHostStr);
        env->ReleaseStringUTFChars(remoteHost, mRemoteHost);
    } else {
        videoSource = new VideoSource(static_cast<uint8_t>(id), galReceiver->messageRouter(),
                                      (bool) autoStart);
    }
    setVideoSource(env, jthis, videoSource);

    shared_ptr<IVideoSourceCallbacks> vcb(new VideoSourceCallbacks(env, videosourcecallbacks));
    videoSource->registerCallbacks(vcb);
    return STATUS_SUCCESS;
}


extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_VideoSource_nativeShutdown
        (JNIEnv *env, jobject jthis) {
    VideoSource *videoSource = getVideoSource(env, jthis);
    if (videoSource != nullptr) {
        videoSource->forceCloseChannel();
        delete videoSource;
        setVideoSource(env, jthis, nullptr);
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_VideoSource_sendSetup
        (JNIEnv *env, jobject jthis, jint type) {
    VideoSource *videoSource = getVideoSource(env, jthis);
    if (videoSource != nullptr) {
        videoSource->sendSetup((MediaCodecType) type);
    }
}


extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_VideoSource_sendStart
        (JNIEnv *env, jobject jthis, jint index, jint sessionid, jint width, jint height) {
    VideoSource *videoSource = getVideoSource(env, jthis);
    if (videoSource != nullptr) {
        videoSource->sendStart(static_cast<uint32_t>(index), sessionid, width, height);
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_VideoSource_sendStop
        (JNIEnv *env, jobject jthis) {
    VideoSource *videoSource = getVideoSource(env, jthis);
    if (videoSource != nullptr) {
        videoSource->sendStop();
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_VideoSource_sendData
        (JNIEnv *env, jobject jthis, jlong timeStamp, jbyteArray data, jint len, jint flags) {
    VideoSource *videoSource = getVideoSource(env, jthis);
    jbyte *cbuf = env->GetByteArrayElements(data, nullptr);
    jsize jlen = env->GetArrayLength(data);
    if (jlen != len) {
        env->ReleaseByteArrayElements(data, cbuf, JNI_ABORT);
        return;
    }
    if (videoSource != nullptr) {
        videoSource->sendData(static_cast<uint64_t>(timeStamp), (void *) cbuf,
                              static_cast<size_t>(len), flags);
    }
    env->ReleaseByteArrayElements(data, cbuf, JNI_ABORT);
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_VideoSource_sendVideoFocusRequestNotifi
        (JNIEnv *env, jobject jthis, jint channelId, jint mode, jint reason) {
    VideoSource *videoSource = getVideoSource(env, jthis);
    if (videoSource != nullptr) {
        videoSource->sendVideoFocusRequest(channelId, static_cast<uint32_t>(mode),
                                           static_cast<uint32_t>(reason));
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_VideoSource_sendDisplayAreaChangeResponse
        (JNIEnv *env, jobject jthis) {
    VideoSource *videoSource = getVideoSource(env, jthis);
    if (videoSource != nullptr) {
        videoSource->sendDisplayAreaChangeResponse();
    }
}

extern "C" JNIEXPORT void JNICALL
Java_com_link_riderservice_connection_display_protocol_project_VideoSource_sendVideoOrientation
        (JNIEnv *env, jobject jthis, jboolean isLandscape) {
    VideoSource *videoSource = getVideoSource(env, jthis);
    if (videoSource != nullptr) {
        videoSource->sendVideoOrientation(isLandscape);
    }
}

extern "C" JNIEXPORT jboolean JNICALL
Java_com_link_riderservice_connection_display_protocol_project_VideoSource_isStartResponseMessageExist
        (JNIEnv *env, jobject jthis) {
    VideoSource *videoSource = getVideoSource(env, jthis);
    if (videoSource != nullptr) {
        return static_cast<jboolean>(videoSource->isExtraMessageExist(
                MEDIA_MESSAGE_START_RESPONSE_EXISTANCE));
    }
    return static_cast<jboolean>(false);
}