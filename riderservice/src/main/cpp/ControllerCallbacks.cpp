// Copyright 2014 Google Inc. All Rights Reserved.

#include "ControllerCallbacks.h"

void ControllerCallbacks::unrecoverableErrorCallback(MessageStatus err) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mUnrecoverableErrorCallbackId, (jint) err);
}

void ControllerCallbacks::authCompleteCallback() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mAuthCompelteCallbackId);
}

void ControllerCallbacks::pingRequestCallback(int64_t timestamp, bool bugReport) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mPingRequestCallbackId, timestamp, bugReport);
}

void ControllerCallbacks::pingResponseCallback(int64_t timestamp) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mPingResponseCallbackId, timestamp);
}

void ControllerCallbacks::byeByeRequestCallback(ByeByeReason reason) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mByeByeRequestCallbackId, reason);
}

void ControllerCallbacks::byeByeResponseCallback() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mByeByeResponseCallbackId);
}

void ControllerCallbacks::exitRequestCallback() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mExitRequestCallbackId);
}

/*void ControllerCallbacks::navFocusNotificationCallback(NavFocusType focusType) {
    JNIEnv* env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mNavFocusNotifCallbackId, focusType);
}

void ControllerCallbacks::audioFocusNotificationCallback(AudioFocusStateType request,bool unsolicited) {
    JNIEnv* env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mAudioFoucNotifCallbackId, request);
}*/

void ControllerCallbacks::forceLandscapeRequestCallback(bool force) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mForceLandscapeCallbackId, force);
}

void ControllerCallbacks::screenOrientationInquire() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mScreenOrientationInquireCallbackId);
}

void ControllerCallbacks::screenResolutionInquire() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mScreenResolutionInquireCallbackId);
}

void ControllerCallbacks::runningStateInquire() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    //env->CallVoidMethod(mJthis, mRunningStateInquireCallbackId);
}

void ControllerCallbacks::versionResponseCallback(uint16_t major, uint16_t minor) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mVersionResponseCallbackId, major, minor);
}

void ControllerCallbacks::serviceDiscoveryResponseCallback(string &id, string &make, string &model,
                                                           string &year, string &huIc,
                                                           string &huMake,
                                                           string &huModel, string &huSwBuild,
                                                           string &huSwVersion,
                                                           string &huSeries, string &huMuVersion,
                                                           int32_t checkSum) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    jstring jId = nullptr;
    jstring jMake = nullptr;
    jstring jModel = nullptr;
    jstring jYear = nullptr;
    jstring jHuIc = nullptr;
    jstring jHuMake = nullptr;
    jstring jHuModel = nullptr;
    jstring jHuSwBuild = nullptr;
    jstring jHuSwVersion = nullptr;
    jstring jHuSeries = nullptr;
    jstring jHuMuVersion = nullptr;
    if (id.length() > 0) {
        jId = env->NewStringUTF(id.c_str());
    }
    if (make.length() > 0) {
        jMake = env->NewStringUTF(make.c_str());
    }
    if (model.length() > 0) {
        jModel = env->NewStringUTF(model.c_str());
    }
    if (year.length() > 0) {
        jYear = env->NewStringUTF(year.c_str());
    }
    if (huIc.length() > 0) {
        jHuIc = env->NewStringUTF(huIc.c_str());
    }
    if (huMake.length() > 0) {
        jHuMake = env->NewStringUTF(huMake.c_str());
    }


    if (huModel.length() > 0) {
        jHuModel = env->NewStringUTF(huModel.c_str());
    }
    if (huSwBuild.length() > 0) {
        jHuSwBuild = env->NewStringUTF(huSwBuild.c_str());
    }
    if (huSwVersion.length() > 0) {
        jHuSwVersion = env->NewStringUTF(huSwVersion.c_str());
    }

    if (huSeries.length() > 0) {
        jHuSeries = env->NewStringUTF(huSeries.c_str());
    }
    if (huMuVersion.length() > 0) {
        jHuMuVersion = env->NewStringUTF(huMuVersion.c_str());
    }
    env->CallVoidMethod(mJthis, mserviceDiscoveryReponseCallbackId, jId, jMake, jModel, jYear,
                        jHuIc, jHuMake, jHuModel,
                        jHuSwBuild, jHuSwVersion, jHuSeries, jHuMuVersion, checkSum);
}

void ControllerCallbacks::autoRotationRequest(bool autoed) {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mAutoRotationRequestId, autoed);
}

void ControllerCallbacks::timeDateInquire() {
    JNIEnv *env = JniUtil::getEnv(mVm);
    env->CallVoidMethod(mJthis, mTimeDateInquireCallbackId);
}
