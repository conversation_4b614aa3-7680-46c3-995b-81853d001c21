/*
 * Copyright 2014 Google Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.link.riderservice.mirror.glutils;

import java.nio.FloatBuffer;

/**
 * Base class for stuff we like to draw.
 */
public class Drawable2d {
    /**
     * Simple triangle (roughly equilateral, 1.0 per side).
     */
    private static final float[] TRIANGLE_COORDS = {
            0.0f, 0.622008459f, // top
            -0.5f, -0.311004243f, // bottom left
            0.5f, -0.311004243f // bottom right
    };
    private static final FloatBuffer TRIANGLE_BUF = GlUtil.createFloatBuffer(TRIANGLE_COORDS);

    /**
     * Simple square, specified as a triangle strip. The square is centered on
     * (0,0) and has a size of 1x1.
     * Triangles are 0-1-2 and 2-1-3 (counter-clockwise winding).
     */
    private static final float[] RECTANGLE_COORDS = {
            -0.5f, -0.5f, // 0 bottom
            // left
            0.5f, -0.5f, // 1 bottom right
            -0.5f, 0.5f, // 2 top left
            0.5f, 0.5f, // 3 top right
    };
    private static final FloatBuffer RECTANGLE_BUF = GlUtil.createFloatBuffer(RECTANGLE_COORDS);

    /**
     * A "full" square, extending from -1 to +1 in both dimensions. When the
     * model/view/projection matrix is identity, this will exactly cover the
     * viewport.
     *
     * <p> This has texture coordinates as well.</p>
     */
    private static final float[] FULL_RECTANGLE_COORDS = {
            -1.0f, -1.0f, // 0
            // bottom
            // left
            1.0f, -1.0f, // 1 bottom right
            -1.0f, 1.0f, // 2 top left
            1.0f, 1.0f, // 3 top right
    };
    private static final FloatBuffer FULL_RECTANGLE_BUF =
            GlUtil.createFloatBuffer(FULL_RECTANGLE_COORDS);

    private static final int SIZEOF_FLOAT = 4;

    private FloatBuffer mVertexArray;
    private int mVertexCount;
    private int mCoordsPerVertex;
    private int mVertexStride;
    private final Prefab mPrefab;

    /**
     * Enum values for constructor.
     */
    public enum Prefab {
        TRIANGLE, RECTANGLE, FULL_RECTANGLE
    }

    /**
     * Prepares a drawable from a "pre-fabricated" shape definition.
     *
     * <p>
     * Does no EGL/GL operations, so this can be done at any time.
     * </p>
     */
    public Drawable2d(final Prefab shape) {
        switch (shape) {
            case TRIANGLE:
                mVertexArray = TRIANGLE_BUF;
                mCoordsPerVertex = 2;
                mVertexStride = mCoordsPerVertex * SIZEOF_FLOAT;
                mVertexCount = TRIANGLE_COORDS.length / mCoordsPerVertex;
                break;
            case RECTANGLE:
                mVertexArray = RECTANGLE_BUF;
                mCoordsPerVertex = 2;
                mVertexStride = mCoordsPerVertex * SIZEOF_FLOAT;
                mVertexCount = RECTANGLE_COORDS.length / mCoordsPerVertex;
                break;
            case FULL_RECTANGLE:
                mVertexArray = FULL_RECTANGLE_BUF;
                mCoordsPerVertex = 2;
                mVertexStride = mCoordsPerVertex * SIZEOF_FLOAT;
                mVertexCount = FULL_RECTANGLE_COORDS.length / mCoordsPerVertex;
                break;
            default:
                throw new RuntimeException("Unknown shape " + shape);
        }
        mPrefab = shape;
    }

    /**
     * Returns the array of vertices.
     *
     * <p>
     * To avoid allocations, this returns internal state. The caller must not
     * modify it.
     * </p>
     */
    public FloatBuffer getVertexArray() {
        return mVertexArray;
    }

    /**
     * Returns the number of vertices stored in the vertex array.
     */
    public int getVertexCount() {
        return mVertexCount;
    }

    /**
     * Returns the width, in bytes, of the data for each vertex.
     */
    public int getVertexStride() {
        return mVertexStride;
    }

    /**
     * Returns the number of position coordinates per vertex. This will be 2 or
     * 3.
     */
    public int getCoordsPerVertex() {
        return mCoordsPerVertex;
    }

    @Override
    public String toString() {
        if (mPrefab != null) {
            return "[Drawable2d: " + mPrefab + "]";
        } else {
            return "[Drawable2d: ...]";
        }
    }
}