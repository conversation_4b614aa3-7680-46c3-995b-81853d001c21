package com.link.riderservice.analytics

import com.link.riderservice.utils.logD
import com.link.riderservice.utils.logE
import com.link.riderservice.utils.logI
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 连接会话管理器
 * 管理完整的连接会话，跟踪从BLE到TCP的整个连接流程
 * <AUTHOR>
 * @date 2024/01/01
 */
object ConnectionSessionManager {
    
    private const val TAG = "ConnectionSessionManager"
    
    /** 当前活跃的连接会话 */
    private val activeSessions = ConcurrentHashMap<String, ConnectionSession>()
    
    /** 已完成的会话历史 */
    private val sessionHistory = CopyOnWriteArrayList<ConnectionSession>()
    
    /** 会话监听器 */
    private val sessionListeners = CopyOnWriteArrayList<ConnectionSessionListener>()
    
    /** 最大历史记录数量 */
    private const val MAX_HISTORY_SIZE = 500
    
    // ==================== 会话生命周期管理 ====================
    
    /**
     * 开始新的连接会话
     * @param deviceId 设备ID
     * @return 会话ID
     */
    fun startSession(deviceId: String): String {
        val sessionId = generateSessionId()
        val session = ConnectionSession(
            sessionId = sessionId,
            deviceId = deviceId,
            bleStartTime = System.currentTimeMillis(),
            currentStage = ConnectionStage.BLE_CONNECTING
        )
        
        activeSessions[sessionId] = session
        logI(TAG, "Started new connection session: $sessionId for device: $deviceId")
        
        notifyListeners { it.onSessionStarted(session) }
        return sessionId
    }
    
    /**
     * 记录BLE连接成功
     */
    fun recordBleConnected(sessionId: String) {
        updateSession(sessionId) { session ->
            session.copy(
                bleConnectedTime = System.currentTimeMillis(),
                currentStage = ConnectionStage.BLE_CONNECTED
            )
        }
        logD(TAG, "BLE connected for session: $sessionId")
    }
    
    /**
     * 记录WiFi连接开始
     */
    fun recordWifiConnecting(sessionId: String, wifiMode: String) {
        updateSession(sessionId) { session ->
            session.copy(
                wifiStartTime = System.currentTimeMillis(),
                wifiMode = wifiMode,
                currentStage = ConnectionStage.WIFI_CONNECTING
            )
        }
        logD(TAG, "WiFi connecting for session: $sessionId, mode: $wifiMode")
    }
    
    /**
     * 记录WiFi连接成功
     */
    fun recordWifiConnected(sessionId: String) {
        updateSession(sessionId) { session ->
            session.copy(
                wifiConnectedTime = System.currentTimeMillis(),
                currentStage = ConnectionStage.WIFI_CONNECTED
            )
        }
        logD(TAG, "WiFi connected for session: $sessionId")
    }
    
    /**
     * 记录TCP连接开始
     */
    fun recordTcpConnecting(sessionId: String) {
        updateSession(sessionId) { session ->
            session.copy(
                tcpStartTime = System.currentTimeMillis(),
                currentStage = ConnectionStage.TCP_CONNECTING
            )
        }
        logD(TAG, "TCP connecting for session: $sessionId")
    }
    
    /**
     * 记录TCP连接成功（会话完成）
     */
    fun recordTcpConnected(sessionId: String) {
        updateSession(sessionId) { session ->
            val completedSession = session.copy(
                tcpConnectedTime = System.currentTimeMillis(),
                currentStage = ConnectionStage.TCP_CONNECTED,
                status = SessionStatus.SUCCESS
            )
            
            // 会话完成，记录统计信息
            logSessionCompletion(completedSession)

            // 记录会话统计到分析日志
            completedSession.bleConnectionTime?.let { bleTime ->
                completedSession.wifiConnectionTime?.let { wifiTime ->
                    completedSession.tcpConnectionTime?.let { tcpTime ->
                        completedSession.totalConnectionTime?.let { totalTime ->
                            logConnectionSession(sessionId, bleTime, wifiTime, tcpTime, totalTime)
                        }
                    }
                }
            }

            completeSession(sessionId, completedSession)
            
            completedSession
        }
        logI(TAG, "TCP connected - session completed: $sessionId")
    }
    
    /**
     * 记录连接失败
     */
    fun recordConnectionFailed(sessionId: String, stage: ConnectionStage, error: ConnectionError) {
        updateSession(sessionId) { session ->
            val failedSession = session.copy(
                currentStage = ConnectionStage.FAILED,
                status = SessionStatus.FAILED,
                error = error
            )
            
            logE(TAG, "Connection failed at stage $stage for session: $sessionId, error: ${error.message}")
            completeSession(sessionId, failedSession)
            
            failedSession
        }
    }
    
    /**
     * 取消会话
     */
    fun cancelSession(sessionId: String) {
        updateSession(sessionId) { session ->
            val cancelledSession = session.copy(
                status = SessionStatus.CANCELLED
            )
            
            logI(TAG, "Session cancelled: $sessionId")
            completeSession(sessionId, cancelledSession)
            
            cancelledSession
        }
    }
    
    // ==================== 数据查询 ====================
    
    /**
     * 获取当前活跃会话
     */
    fun getCurrentSession(): ConnectionSession? {
        return activeSessions.values.firstOrNull()
    }
    
    /**
     * 获取指定会话
     */
    fun getSession(sessionId: String): ConnectionSession? {
        return activeSessions[sessionId] ?: sessionHistory.find { it.sessionId == sessionId }
    }
    
    /**
     * 获取会话历史
     */
    fun getSessionHistory(limit: Int = 50): List<ConnectionSession> {
        return sessionHistory.takeLast(limit)
    }
    
    /**
     * 获取连接时间统计
     */
    fun getConnectionTimeStats(): ConnectionTimeStats {
        val completedSessions = sessionHistory.filter { it.isSuccessful }
        
        if (completedSessions.isEmpty()) {
            return ConnectionTimeStats()
        }
        
        val bleTimes = completedSessions.mapNotNull { it.bleConnectionTime }
        val wifiTimes = completedSessions.mapNotNull { it.wifiConnectionTime }
        val tcpTimes = completedSessions.mapNotNull { it.tcpConnectionTime }
        val totalTimes = completedSessions.mapNotNull { it.totalConnectionTime }
        
        return ConnectionTimeStats(
            totalSessions = completedSessions.size,
            averageBleTime = bleTimes.average().toLong(),
            averageWifiTime = wifiTimes.average().toLong(),
            averageTcpTime = tcpTimes.average().toLong(),
            averageTotalTime = totalTimes.average().toLong(),
            minTotalTime = totalTimes.minOrNull() ?: 0,
            maxTotalTime = totalTimes.maxOrNull() ?: 0
        )
    }
    
    // ==================== 监听器管理 ====================
    
    /**
     * 添加会话监听器
     */
    fun addSessionListener(listener: ConnectionSessionListener) {
        sessionListeners.add(listener)
    }
    
    /**
     * 移除会话监听器
     */
    fun removeSessionListener(listener: ConnectionSessionListener) {
        sessionListeners.remove(listener)
    }
    
    // ==================== 内部方法 ====================
    
    /**
     * 更新会话
     */
    private fun updateSession(sessionId: String, updater: (ConnectionSession) -> ConnectionSession) {
        activeSessions[sessionId]?.let { session ->
            val updatedSession = updater(session)
            activeSessions[sessionId] = updatedSession

            // 记录阶段变化日志
            if (session.currentStage != updatedSession.currentStage) {
                logConnectionSessionStage(
                    sessionId,
                    updatedSession.currentStage.name,
                    updatedSession.progressPercentage
                )
            }

            notifyListeners { it.onSessionUpdated(updatedSession) }
        } ?: run {
            logE(TAG, "Cannot update unknown session: $sessionId")
        }
    }
    
    /**
     * 完成会话
     */
    private fun completeSession(sessionId: String, session: ConnectionSession) {
        activeSessions.remove(sessionId)
        addToHistory(session)
        notifyListeners { it.onSessionCompleted(session) }
    }
    
    /**
     * 添加到历史记录
     */
    private fun addToHistory(session: ConnectionSession) {
        sessionHistory.add(session)
        
        // 限制历史记录大小
        while (sessionHistory.size > MAX_HISTORY_SIZE) {
            sessionHistory.removeAt(0)
        }
    }
    
    /**
     * 记录会话完成的统计信息
     */
    private fun logSessionCompletion(session: ConnectionSession) {
        logI(TAG, "=== 连接会话完成 ===")
        logI(TAG, "会话ID: ${session.sessionId}")
        logI(TAG, "设备ID: ${session.deviceId}")
        logI(TAG, "蓝牙连接时间: ${session.bleConnectionTime ?: "N/A"}ms")
        logI(TAG, "WiFi连接时间: ${session.wifiConnectionTime ?: "N/A"}ms (${session.wifiMode ?: "未知模式"})")
        logI(TAG, "TCP连接时间: ${session.tcpConnectionTime ?: "N/A"}ms")
        logI(TAG, "总连接时间: ${session.totalConnectionTime ?: "N/A"}ms")
    }
    
    /**
     * 生成会话ID
     */
    private fun generateSessionId(): String {
        return "CS_${UUID.randomUUID().toString().substring(0, 8)}"
    }
    
    /**
     * 通知监听器
     */
    private fun notifyListeners(action: (ConnectionSessionListener) -> Unit) {
        sessionListeners.forEach { listener ->
            try {
                action(listener)
            } catch (e: Exception) {
                logE(TAG, "Error notifying session listener", e)
            }
        }
    }
}

/**
 * 连接时间统计数据类
 */
data class ConnectionTimeStats(
    val totalSessions: Int = 0,
    val averageBleTime: Long = 0,
    val averageWifiTime: Long = 0,
    val averageTcpTime: Long = 0,
    val averageTotalTime: Long = 0,
    val minTotalTime: Long = 0,
    val maxTotalTime: Long = 0
)

/**
 * 连接会话监听器接口
 */
interface ConnectionSessionListener {
    fun onSessionStarted(session: ConnectionSession) {}
    fun onSessionUpdated(session: ConnectionSession) {}
    fun onSessionCompleted(session: ConnectionSession) {}
}
