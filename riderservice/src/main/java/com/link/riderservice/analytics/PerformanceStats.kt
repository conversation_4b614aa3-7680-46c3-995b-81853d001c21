package com.link.riderservice.analytics

import java.util.concurrent.atomic.AtomicLong

/**
 * 性能统计数据类
 * 用于存储和计算连接性能指标
 * <AUTHOR>
 * @date 2024/01/01
 */
data class PerformanceStats(
    var totalAttempts: Int = 0,
    var successfulConnections: Int = 0,
    var failedConnections: Int = 0,
    var totalConnectionTime: Long = 0L,
    var maxConnectionTime: Long = 0L,
    var minConnectionTime: Long = Long.MAX_VALUE
) {
    /**
     * 成功率
     */
    val successRate: Float
        get() = if (totalAttempts > 0) successfulConnections.toFloat() / totalAttempts else 0f
    
    /**
     * 失败率
     */
    val failureRate: Float
        get() = if (totalAttempts > 0) failedConnections.toFloat() / totalAttempts else 0f
    
    /**
     * 失败率
     */
    val failureRate: Float
        get() = if (totalAttempts > 0) failedConnections.toFloat() / totalAttempts else 0f

    /**
     * 平均连接时间
     */
    val averageConnectionTime: Long
        get() = if (successfulConnections > 0) totalConnectionTime / successfulConnections else 0L
    
    /**
     * 更新统计数据
     */
    fun update(metrics: ConnectionMetrics) {
        totalAttempts++
        
        when (metrics.status) {
            ConnectionStatus.SUCCESS -> {
                successfulConnections++
                totalConnectionTime += metrics.duration
                maxConnectionTime = maxOf(maxConnectionTime, metrics.duration)
                minConnectionTime = minOf(minConnectionTime, metrics.duration)
            }
            ConnectionStatus.FAILED, ConnectionStatus.TIMEOUT, ConnectionStatus.CANCELLED -> {
                failedConnections++
            }
            else -> { /* 忽略进行中的连接 */ }
        }
    }
    
    /**
     * 重置统计数据
     */
    fun reset() {
        totalAttempts = 0
        successfulConnections = 0
        failedConnections = 0
        totalConnectionTime = 0L
        maxConnectionTime = 0L
        minConnectionTime = Long.MAX_VALUE
    }
    
    /**
     * 获取统计摘要
     */
    fun getSummary(): String {
        return buildString {
            append("总尝试: $totalAttempts, ")
            append("成功: $successfulConnections, ")
            append("失败: $failedConnections, ")
            append("成功率: ${(successRate * 100).toInt()}%, ")
            append("平均时间: ${averageConnectionTime}ms")
            if (successfulConnections > 0) {
                append(", 最快: ${minConnectionTime}ms, 最慢: ${maxConnectionTime}ms")
            }
        }
    }
}

/**
 * 实时指标数据类
 * 用于跟踪当前正在进行的连接活动
 */
data class RealtimeMetrics(
    val currentAttempts: AtomicLong = AtomicLong(0),
    val recentSuccesses: AtomicLong = AtomicLong(0),
    val recentFailures: AtomicLong = AtomicLong(0),
    val totalRetries: AtomicLong = AtomicLong(0)
) {
    /**
     * 获取当前活跃连接数
     */
    fun getCurrentActiveConnections(): Long = currentAttempts.get()
    
    /**
     * 获取近期成功率
     */
    fun getRecentSuccessRate(): Float {
        val total = recentSuccesses.get() + recentFailures.get()
        return if (total > 0) recentSuccesses.get().toFloat() / total else 0f
    }
    
    /**
     * 重置近期统计
     */
    fun resetRecentStats() {
        recentSuccesses.set(0)
        recentFailures.set(0)
    }
    
    /**
     * 获取实时摘要
     */
    fun getSummary(): String {
        return buildString {
            append("活跃连接: ${currentAttempts.get()}, ")
            append("近期成功: ${recentSuccesses.get()}, ")
            append("近期失败: ${recentFailures.get()}, ")
            append("总重试: ${totalRetries.get()}")
        }
    }
}
