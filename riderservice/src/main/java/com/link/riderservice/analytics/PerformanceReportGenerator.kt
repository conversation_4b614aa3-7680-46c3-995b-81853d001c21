package com.link.riderservice.analytics

import com.link.riderservice.utils.TimeUtils
import com.link.riderservice.utils.logI

/**
 * 性能报告生成器
 * 负责生成详细的连接性能分析报告
 * <AUTHOR>
 * @date 2024/01/01
 */
object PerformanceReportGenerator {
    
    private const val TAG = "PerformanceReportGenerator"
    
    /**
     * 生成完整的性能报告
     */
    fun generateFullReport(): String {
        logI(TAG, "Generating full performance report")
        
        return buildString {
            appendLine("=" .repeat(60))
            appendLine("连接性能分析报告")
            appendLine("=" .repeat(60))
            appendLine("生成时间: ${TimeUtils.getCurrentTimeStr()}")
            appendLine()
            
            // 总体概览
            appendOverview()
            appendLine()
            
            // 各连接类型详细报告
            appendDetailedReports()
            appendLine()
            
            // 实时指标
            appendRealtimeMetrics()
            appendLine()
            
            // 性能建议
            appendPerformanceRecommendations()
            appendLine()
            
            // 历史趋势
            appendHistoryTrend()
            
            appendLine("=" .repeat(60))
            appendLine("报告结束")
            appendLine("=" .repeat(60))
        }
    }
    
    /**
     * 生成简化报告
     */
    fun generateSummaryReport(): String {
        return buildString {
            appendLine("=== 连接性能摘要 ===")
            appendLine("时间: ${TimeUtils.getCurrentTimeStr()}")
            appendLine()
            
            val performanceReport = ConnectionAnalytics.getPerformanceReport()
            
            if (performanceReport.isEmpty()) {
                appendLine("暂无连接性能数据")
                return@buildString
            }
            
            performanceReport.forEach { (type, stats) ->
                appendLine("【$type】")
                appendLine("  ${stats.getSummary()}")
                
                // 添加状态指示
                val status = when {
                    stats.successRate >= 0.9f -> "优秀"
                    stats.successRate >= 0.8f -> "良好"
                    stats.successRate >= 0.7f -> "一般"
                    else -> "需要改进"
                }
                appendLine("  状态: $status")
                appendLine()
            }
        }
    }
    
    /**
     * 生成特定类型的报告
     */
    fun generateTypeReport(type: ConnectionType): String {
        return buildString {
            appendLine("=== ${type}连接性能报告 ===")
            appendLine("时间: ${TimeUtils.getCurrentTimeStr()}")
            appendLine()
            
            val stats = ConnectionAnalytics.getPerformanceReport()[type]
            if (stats == null) {
                appendLine("暂无${type}连接性能数据")
                return@buildString
            }
            
            // 基本统计
            appendLine("【基本统计】")
            appendLine("总连接尝试: ${stats.totalAttempts}")
            appendLine("成功连接: ${stats.successfulConnections}")
            appendLine("失败连接: ${stats.failedConnections}")
            appendLine("成功率: ${(stats.successRate * 100).toInt()}%")
            appendLine("失败率: ${(stats.failureRate * 100).toInt()}%")
            appendLine()
            
            // 时间统计
            if (stats.successfulConnections > 0) {
                appendLine("【时间统计】")
                appendLine("平均连接时间: ${stats.averageConnectionTime}ms")
                appendLine("最快连接时间: ${stats.minConnectionTime}ms")
                appendLine("最慢连接时间: ${stats.maxConnectionTime}ms")
                appendLine()
            }
            
            // 实时指标
            val realtimeMetrics = ConnectionAnalytics.getRealtimeMetrics(type)
            if (realtimeMetrics != null) {
                appendLine("【实时指标】")
                appendLine(realtimeMetrics.getSummary())
                appendLine()
            }
            
            // 性能建议
            val recommendations = ConnectionAnalytics.getPerformanceRecommendations(type)
            if (recommendations.isNotEmpty()) {
                appendLine("【性能建议】")
                recommendations.forEach { recommendation ->
                    appendLine("• $recommendation")
                }
                appendLine()
            }
        }
    }
    
    /**
     * 生成JSON格式的报告
     */
    fun generateJsonReport(): String {
        return buildString {
            appendLine("{")
            appendLine("  \"timestamp\": \"${TimeUtils.getCurrentTimeStr()}\",")
            appendLine("  \"performance_data\": {")
            
            val performanceReport = ConnectionAnalytics.getPerformanceReport()
            val entries = performanceReport.entries.toList()
            
            entries.forEachIndexed { index, (type, stats) ->
                appendLine("    \"$type\": {")
                appendLine("      \"total_attempts\": ${stats.totalAttempts},")
                appendLine("      \"successful_connections\": ${stats.successfulConnections},")
                appendLine("      \"failed_connections\": ${stats.failedConnections},")
                appendLine("      \"success_rate\": ${stats.successRate},")
                appendLine("      \"average_connection_time\": ${stats.averageConnectionTime},")
                appendLine("      \"max_connection_time\": ${stats.maxConnectionTime},")
                appendLine("      \"min_connection_time\": ${if (stats.minConnectionTime == Long.MAX_VALUE) 0 else stats.minConnectionTime}")
                append("    }")
                if (index < entries.size - 1) appendLine(",")
                else appendLine()
            }
            
            appendLine("  },")
            appendLine("  \"realtime_metrics\": {")
            
            val realtimeMetrics = ConnectionAnalytics.getAllRealtimeMetrics()
            val realtimeEntries = realtimeMetrics.entries.toList()
            
            realtimeEntries.forEachIndexed { index, (type, metrics) ->
                appendLine("    \"$type\": {")
                appendLine("      \"current_attempts\": ${metrics.currentAttempts.get()},")
                appendLine("      \"recent_successes\": ${metrics.recentSuccesses.get()},")
                appendLine("      \"recent_failures\": ${metrics.recentFailures.get()},")
                appendLine("      \"total_retries\": ${metrics.totalRetries.get()}")
                append("    }")
                if (index < realtimeEntries.size - 1) appendLine(",")
                else appendLine()
            }
            
            appendLine("  }")
            appendLine("}")
        }
    }
    
    // ==================== 私有方法 ====================
    
    private fun StringBuilder.appendOverview() {
        appendLine("【总体概览】")
        
        val performanceReport = ConnectionAnalytics.getPerformanceReport()
        val activeConnections = ConnectionAnalytics.getActiveConnectionCount()
        val historyCount = ConnectionAnalytics.getConnectionHistory().size
        
        appendLine("活跃连接数: $activeConnections")
        appendLine("历史记录数: $historyCount")
        appendLine("监控的连接类型: ${performanceReport.keys.joinToString(", ")}")
        
        // 总体统计
        var totalAttempts = 0
        var totalSuccesses = 0
        var totalFailures = 0
        
        performanceReport.values.forEach { stats ->
            totalAttempts += stats.totalAttempts
            totalSuccesses += stats.successfulConnections
            totalFailures += stats.failedConnections
        }
        
        if (totalAttempts > 0) {
            val overallSuccessRate = totalSuccesses.toFloat() / totalAttempts
            appendLine("总体成功率: ${(overallSuccessRate * 100).toInt()}% ($totalSuccesses/$totalAttempts)")
        }
    }
    
    private fun StringBuilder.appendDetailedReports() {
        appendLine("【详细报告】")
        
        val performanceReport = ConnectionAnalytics.getPerformanceReport()
        
        performanceReport.forEach { (type, stats) ->
            appendLine()
            appendLine("▶ $type 连接")
            appendLine("  ${stats.getSummary()}")
            
            // 性能等级
            val grade = when {
                stats.successRate >= 0.95f && stats.averageConnectionTime <= 2000L -> "A+"
                stats.successRate >= 0.9f && stats.averageConnectionTime <= 3000L -> "A"
                stats.successRate >= 0.8f && stats.averageConnectionTime <= 5000L -> "B"
                stats.successRate >= 0.7f -> "C"
                else -> "D"
            }
            appendLine("  性能等级: $grade")
        }
    }
    
    private fun StringBuilder.appendRealtimeMetrics() {
        appendLine("【实时指标】")
        
        val realtimeMetrics = ConnectionAnalytics.getAllRealtimeMetrics()
        
        if (realtimeMetrics.isEmpty()) {
            appendLine("暂无实时指标数据")
            return
        }
        
        realtimeMetrics.forEach { (type, metrics) ->
            appendLine("$type: ${metrics.getSummary()}")
        }
    }
    
    private fun StringBuilder.appendPerformanceRecommendations() {
        appendLine("【性能建议】")
        
        val performanceReport = ConnectionAnalytics.getPerformanceReport()
        var hasRecommendations = false
        
        performanceReport.forEach { (type, _) ->
            val recommendations = ConnectionAnalytics.getPerformanceRecommendations(type)
            if (recommendations.isNotEmpty()) {
                hasRecommendations = true
                appendLine()
                appendLine("$type:")
                recommendations.forEach { recommendation ->
                    appendLine("  • $recommendation")
                }
            }
        }
        
        if (!hasRecommendations) {
            appendLine("当前性能表现良好，暂无特别建议")
        }
    }
    
    private fun StringBuilder.appendHistoryTrend() {
        appendLine("【历史趋势】")
        
        val history = ConnectionAnalytics.getConnectionHistory(50)
        if (history.isEmpty()) {
            appendLine("暂无历史数据")
            return
        }
        
        // 按类型分组统计最近的连接
        val recentByType = history.groupBy { it.connectionType }
        
        recentByType.forEach { (type, connections) ->
            val recentSuccesses = connections.count { it.isSuccessful }
            val recentTotal = connections.size
            val recentSuccessRate = if (recentTotal > 0) recentSuccesses.toFloat() / recentTotal else 0f
            
            appendLine("$type: 最近${recentTotal}次连接，成功率${(recentSuccessRate * 100).toInt()}%")
        }
    }
}
