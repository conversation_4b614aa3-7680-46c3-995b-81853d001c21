package com.link.riderservice.analytics

import android.content.Context
import com.link.riderservice.BuildConfig
import com.link.riderservice.utils.logD
import com.link.riderservice.utils.logI
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 简单的连接时间跟踪器
 * 只记录4种关键时间：蓝牙、WiFi、TCP、总时间
 * <AUTHOR>
 * @date 2024/01/01
 */
object ConnectionTimeTracker {
    
    private const val TAG = "ConnectionTimeTracker"
    
    // 当前连接的时间记录
    private var bleStartTime: Long = 0
    private var bleConnectedTime: Long = 0
    private var wifiStartTime: Long = 0
    private var wifiConnectedTime: Long = 0
    private var tcpStartTime: Long = 0
    private var tcpConnectedTime: Long = 0
    
    private var currentDeviceId: String = ""
    private var currentWifiMode: String = ""
    
    private var appContext: Context? = null
    
    /**
     * 初始化
     */
    fun init(context: Context) {
        if (BuildConfig.DEBUG) {
            appContext = context.applicationContext
            logD(TAG, "ConnectionTimeTracker initialized in DEBUG mode")
        }
    }
    
    /**
     * 开始新的连接跟踪
     */
    fun startConnection(deviceId: String) {
        reset()
        currentDeviceId = deviceId
        bleStartTime = System.currentTimeMillis()
        logD(TAG, "Started tracking connection for device: $deviceId")
    }
    
    /**
     * 记录蓝牙连接成功
     */
    fun recordBleConnected() {
        bleConnectedTime = System.currentTimeMillis()
        val bleTime = getBleConnectionTime()
        logD(TAG, "BLE connected in ${bleTime}ms")
    }
    
    /**
     * 记录WiFi连接开始
     */
    fun recordWifiConnecting(wifiMode: String) {
        currentWifiMode = wifiMode
        wifiStartTime = System.currentTimeMillis()
        logD(TAG, "WiFi connecting ($wifiMode)")
    }
    
    /**
     * 记录WiFi连接成功
     */
    fun recordWifiConnected() {
        wifiConnectedTime = System.currentTimeMillis()
        val wifiTime = getWifiConnectionTime()
        logD(TAG, "WiFi connected ($currentWifiMode) in ${wifiTime}ms")
    }
    
    /**
     * 记录TCP连接开始
     */
    fun recordTcpConnecting() {
        tcpStartTime = System.currentTimeMillis()
        logD(TAG, "TCP connecting")
    }
    
    /**
     * 记录TCP连接成功（完成整个连接流程）
     */
    fun recordTcpConnected() {
        tcpConnectedTime = System.currentTimeMillis()
        val tcpTime = getTcpConnectionTime()
        val totalTime = getTotalConnectionTime()
        
        logI(TAG, "=== 连接完成 ===")
        logI(TAG, "设备: $currentDeviceId")
        logI(TAG, "蓝牙连接时间: ${getBleConnectionTime()}ms")
        logI(TAG, "WiFi连接时间: ${getWifiConnectionTime()}ms ($currentWifiMode)")
        logI(TAG, "TCP连接时间: ${tcpTime}ms")
        logI(TAG, "总连接时间: ${totalTime}ms")
        
        // 保存到文件（仅Debug模式）
        saveToFile()
    }
    
    /**
     * 获取蓝牙连接时间
     */
    fun getBleConnectionTime(): Long {
        return if (bleStartTime > 0 && bleConnectedTime > 0) {
            bleConnectedTime - bleStartTime
        } else 0
    }
    
    /**
     * 获取WiFi连接时间
     */
    fun getWifiConnectionTime(): Long {
        return if (wifiStartTime > 0 && wifiConnectedTime > 0) {
            wifiConnectedTime - wifiStartTime
        } else 0
    }
    
    /**
     * 获取TCP连接时间
     */
    fun getTcpConnectionTime(): Long {
        return if (tcpStartTime > 0 && tcpConnectedTime > 0) {
            tcpConnectedTime - tcpStartTime
        } else 0
    }
    
    /**
     * 获取总连接时间
     */
    fun getTotalConnectionTime(): Long {
        return if (bleStartTime > 0 && tcpConnectedTime > 0) {
            tcpConnectedTime - bleStartTime
        } else 0
    }
    
    /**
     * 获取当前连接状态摘要
     */
    fun getCurrentConnectionInfo(): String {
        return buildString {
            appendLine("=== 当前连接状态 ===")
            appendLine("设备ID: $currentDeviceId")
            appendLine("蓝牙连接时间: ${getBleConnectionTime()}ms")
            appendLine("WiFi连接时间: ${getWifiConnectionTime()}ms ($currentWifiMode)")
            appendLine("TCP连接时间: ${getTcpConnectionTime()}ms")
            appendLine("总连接时间: ${getTotalConnectionTime()}ms")
        }
    }
    
    /**
     * 重置所有时间记录
     */
    private fun reset() {
        bleStartTime = 0
        bleConnectedTime = 0
        wifiStartTime = 0
        wifiConnectedTime = 0
        tcpStartTime = 0
        tcpConnectedTime = 0
        currentDeviceId = ""
        currentWifiMode = ""
    }
    
    /**
     * 保存连接时间到文件（仅Debug模式）
     */
    private fun saveToFile() {
        if (!BuildConfig.DEBUG || appContext == null) {
            return
        }
        
        try {
            val cacheDir = appContext!!.cacheDir
            val file = File(cacheDir, "connection_times.txt")
            
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            val timeStr = dateFormat.format(Date())
            
            val record = "$timeStr|$currentDeviceId|BLE:${getBleConnectionTime()}ms|" +
                    "WiFi:${getWifiConnectionTime()}ms($currentWifiMode)|" +
                    "TCP:${getTcpConnectionTime()}ms|TOTAL:${getTotalConnectionTime()}ms"
            
            FileWriter(file, true).use { writer ->
                writer.write(record + "\n")
            }
            
            logD(TAG, "Connection times saved to: ${file.absolutePath}")
        } catch (e: Exception) {
            logD(TAG, "Failed to save connection times: ${e.message}")
        }
    }
    
    /**
     * 清除记录文件
     */
    fun clearRecords() {
        if (!BuildConfig.DEBUG || appContext == null) {
            return
        }
        
        try {
            val cacheDir = appContext!!.cacheDir
            val file = File(cacheDir, "connection_times.txt")
            if (file.exists()) {
                file.delete()
                logD(TAG, "Connection times file cleared")
            }
        } catch (e: Exception) {
            logD(TAG, "Failed to clear connection times file: ${e.message}")
        }
    }
}
