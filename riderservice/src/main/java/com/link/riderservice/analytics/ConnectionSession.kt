package com.link.riderservice.analytics

import com.link.riderservice.utils.TimeUtils

/**
 * 连接会话数据类
 * 跟踪从蓝牙连接开始到TCP连接成功的完整连接流程
 * <AUTHOR>
 * @date 2024/01/01
 */
data class ConnectionSession(
    /** 会话ID */
    val sessionId: String,
    /** 设备ID */
    val deviceId: String,
    /** 会话开始时间（BLE连接开始） */
    val sessionStartTime: Long = System.currentTimeMillis(),
    
    // ==================== BLE连接时间 ====================
    /** BLE连接开始时间 */
    val bleStartTime: Long? = null,
    /** BLE连接成功时间 */
    val bleConnectedTime: Long? = null,
    
    // ==================== WiFi连接时间 ====================
    /** WiFi连接开始时间 */
    val wifiStartTime: Long? = null,
    /** WiFi连接成功时间 */
    val wifiConnectedTime: Long? = null,
    /** WiFi连接模式 */
    val wifiMode: String? = null,
    
    // ==================== TCP连接时间 ====================
    /** TCP连接开始时间 */
    val tcpStartTime: Long? = null,
    /** TCP连接成功时间 */
    val tcpConnectedTime: Long? = null,
    
    // ==================== 会话状态 ====================
    /** 当前连接阶段 */
    val currentStage: ConnectionStage = ConnectionStage.BLE_CONNECTING,
    /** 会话状态 */
    val status: SessionStatus = SessionStatus.IN_PROGRESS,
    /** 错误信息 */
    val error: ConnectionError? = null
) {
    
    /**
     * 蓝牙连接时间（毫秒）
     */
    val bleConnectionTime: Long?
        get() = if (bleStartTime != null && bleConnectedTime != null) {
            bleConnectedTime - bleStartTime
        } else null
    
    /**
     * WiFi连接时间（毫秒）
     */
    val wifiConnectionTime: Long?
        get() = if (wifiStartTime != null && wifiConnectedTime != null) {
            wifiConnectedTime - wifiStartTime
        } else null
    
    /**
     * TCP连接时间（毫秒）
     */
    val tcpConnectionTime: Long?
        get() = if (tcpStartTime != null && tcpConnectedTime != null) {
            tcpConnectedTime - tcpStartTime
        } else null
    
    /**
     * 总连接时间（从BLE开始到TCP成功）
     */
    val totalConnectionTime: Long?
        get() = if (bleStartTime != null && tcpConnectedTime != null) {
            tcpConnectedTime - bleStartTime
        } else null
    
    /**
     * 会话是否已完成
     */
    val isCompleted: Boolean
        get() = status != SessionStatus.IN_PROGRESS
    
    /**
     * 会话是否成功
     */
    val isSuccessful: Boolean
        get() = status == SessionStatus.SUCCESS && tcpConnectedTime != null
    
    /**
     * 获取当前阶段的进度百分比
     */
    val progressPercentage: Int
        get() = when (currentStage) {
            ConnectionStage.BLE_CONNECTING -> 10
            ConnectionStage.BLE_CONNECTED -> 25
            ConnectionStage.WIFI_CONNECTING -> 50
            ConnectionStage.WIFI_CONNECTED -> 75
            ConnectionStage.TCP_CONNECTING -> 90
            ConnectionStage.TCP_CONNECTED -> 100
            ConnectionStage.FAILED -> 0
        }
    
    /**
     * 生成会话摘要字符串
     */
    fun getSummary(): String {
        return buildString {
            appendLine("=== 连接会话摘要 ===")
            appendLine("会话ID: $sessionId")
            appendLine("设备ID: $deviceId")
            appendLine("状态: $status")
            appendLine("当前阶段: $currentStage")
            appendLine()
            
            appendLine("=== 连接时间统计 ===")
            bleConnectionTime?.let { 
                appendLine("蓝牙连接时间: ${it}ms") 
            } ?: appendLine("蓝牙连接时间: 未完成")
            
            wifiConnectionTime?.let { 
                appendLine("WiFi连接时间: ${it}ms (模式: ${wifiMode ?: "未知"})") 
            } ?: appendLine("WiFi连接时间: 未完成")
            
            tcpConnectionTime?.let { 
                appendLine("TCP连接时间: ${it}ms") 
            } ?: appendLine("TCP连接时间: 未完成")
            
            totalConnectionTime?.let { 
                appendLine("总连接时间: ${it}ms") 
            } ?: appendLine("总连接时间: 未完成")
            
            if (error != null) {
                appendLine()
                appendLine("错误信息: ${error.message}")
            }
        }
    }
    
    /**
     * 生成简化的日志字符串
     */
    fun getLogString(): String {
        val timeStr = TimeUtils.getCurrentTimeStr()
        return "Session[$sessionId] $currentStage - BLE:${bleConnectionTime ?: "N/A"}ms, " +
                "WiFi:${wifiConnectionTime ?: "N/A"}ms, TCP:${tcpConnectionTime ?: "N/A"}ms, " +
                "Total:${totalConnectionTime ?: "N/A"}ms [$timeStr]"
    }
}

/**
 * 连接阶段枚举
 */
enum class ConnectionStage {
    /** BLE连接中 */
    BLE_CONNECTING,
    /** BLE已连接 */
    BLE_CONNECTED,
    /** WiFi连接中 */
    WIFI_CONNECTING,
    /** WiFi已连接 */
    WIFI_CONNECTED,
    /** TCP连接中 */
    TCP_CONNECTING,
    /** TCP已连接（完成） */
    TCP_CONNECTED,
    /** 连接失败 */
    FAILED
}

/**
 * 会话状态枚举
 */
enum class SessionStatus {
    /** 进行中 */
    IN_PROGRESS,
    /** 成功完成 */
    SUCCESS,
    /** 失败 */
    FAILED,
    /** 超时 */
    TIMEOUT,
    /** 被取消 */
    CANCELLED
}
