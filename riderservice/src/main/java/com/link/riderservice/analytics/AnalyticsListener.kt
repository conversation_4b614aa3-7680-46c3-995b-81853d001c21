package com.link.riderservice.analytics

/**
 * 连接分析监听器接口
 * 用于监听连接分析事件
 * <AUTHOR>
 * @date 2024/01/01
 */
interface AnalyticsListener {
    
    /**
     * 连接开始时调用
     * @param metrics 连接指标数据
     */
    fun onConnectionStarted(metrics: ConnectionMetrics) {}
    
    /**
     * 连接完成时调用（成功或失败）
     * @param metrics 连接指标数据
     */
    fun onConnectionCompleted(metrics: ConnectionMetrics) {}
    
    /**
     * 连接重试时调用
     * @param metrics 连接指标数据
     * @param retryCount 重试次数
     */
    fun onConnectionRetry(metrics: ConnectionMetrics, retryCount: Int) {}
    
    /**
     * 连接状态变化时调用
     * @param metrics 连接指标数据
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     */
    fun onConnectionStatusChanged(
        metrics: ConnectionMetrics,
        oldStatus: ConnectionStatus,
        newStatus: ConnectionStatus
    ) {}
}

/**
 * 简单的分析监听器实现
 * 提供默认的日志记录功能
 */
open class SimpleAnalyticsListener : AnalyticsListener {
    
    override fun onConnectionStarted(metrics: ConnectionMetrics) {
        logConnectionEvent("Connection started", metrics)
    }
    
    override fun onConnectionCompleted(metrics: ConnectionMetrics) {
        val result = if (metrics.isSuccessful) "succeeded" else "failed"
        logConnectionEvent("Connection $result", metrics)
    }
    
    override fun onConnectionRetry(metrics: ConnectionMetrics, retryCount: Int) {
        logConnectionEvent("Connection retry #$retryCount", metrics)
    }
    
    override fun onConnectionStatusChanged(
        metrics: ConnectionMetrics,
        oldStatus: ConnectionStatus,
        newStatus: ConnectionStatus
    ) {
        logConnectionEvent("Status changed: $oldStatus -> $newStatus", metrics)
    }
    
    /**
     * 记录连接事件日志
     */
    protected open fun logConnectionEvent(event: String, metrics: ConnectionMetrics) {
        val message = buildString {
            append("$event: ")
            append("Type=${metrics.connectionType}, ")
            append("Device=${metrics.deviceId}, ")
            append("Session=${metrics.sessionId}")
            if (metrics.isCompleted) {
                append(", Duration=${metrics.duration}ms")
            }
            metrics.error?.let { error ->
                append(", Error=${error.message}")
            }
        }
        
        // 这里使用统一的连接分析标签
        com.link.riderservice.utils.logD("CONNECTION_ANALYTICS", message)
    }
}
