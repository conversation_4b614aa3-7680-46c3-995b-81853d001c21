package com.link.riderservice.analytics

import com.link.riderservice.utils.logD
import com.link.riderservice.utils.logI
import com.link.riderservice.utils.logW
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * 连接性能监控器
 * 负责收集、分析和报告连接性能指标
 * <AUTHOR>
 * @date 2024/01/01
 */
class ConnectionPerformanceMonitor : AnalyticsListener {

    private val performanceData = ConcurrentHashMap<ConnectionType, PerformanceStats>()
    private val realtimeMetrics = ConcurrentHashMap<ConnectionType, RealtimeMetrics>()

    companion object {
        private const val TAG = "ConnectionPerformanceMonitor"

        // 性能阈值常量
        private const val SLOW_CONNECTION_THRESHOLD_MS = 5000L
        private const val FAST_CONNECTION_THRESHOLD_MS = 1000L
        private const val LOW_SUCCESS_RATE_THRESHOLD = 0.7f
        private const val HIGH_RETRY_THRESHOLD = 3
    }

    init {
        logI(TAG, "ConnectionPerformanceMonitor initialized")
    }
    
    // ==================== AnalyticsListener 实现 ====================

    override fun onConnectionStarted(metrics: ConnectionMetrics) {
        updateRealtimeMetrics(metrics.connectionType) { stats ->
            stats.currentAttempts.incrementAndGet()
        }
        logD(TAG, "Connection started: ${metrics.connectionType} - ${metrics.deviceId}")
    }

    override fun onConnectionCompleted(metrics: ConnectionMetrics) {
        recordConnectionMetrics(metrics)
        updateRealtimeMetrics(metrics.connectionType) { stats ->
            stats.currentAttempts.decrementAndGet()
            if (metrics.isSuccessful) {
                stats.recentSuccesses.incrementAndGet()
            } else {
                stats.recentFailures.incrementAndGet()
            }
        }

        // 检查性能警告
        checkPerformanceWarnings(metrics)

        logD(TAG, "Connection completed: ${metrics.connectionType} - ${metrics.deviceId} " +
                "(${metrics.status}, ${metrics.duration}ms)")
    }

    override fun onConnectionRetry(metrics: ConnectionMetrics, retryCount: Int) {
        updateRealtimeMetrics(metrics.connectionType) { stats ->
            stats.totalRetries.incrementAndGet()
        }

        if (retryCount >= HIGH_RETRY_THRESHOLD) {
            logConnectionWarning(
                metrics.connectionType,
                "High retry count detected: $retryCount for device ${metrics.deviceId}"
            )
        }

        logD(TAG, "Connection retry: ${metrics.connectionType} - ${metrics.deviceId} " +
                "(attempt #$retryCount)")
    }
    
    // ==================== 性能数据记录 ====================

    /**
     * 记录连接指标
     */
    fun recordConnectionMetrics(metrics: ConnectionMetrics) {
        if (!metrics.isCompleted) return

        val stats = performanceData.getOrPut(metrics.connectionType) {
            PerformanceStats()
        }

        synchronized(stats) {
            stats.update(metrics)
        }

        logD(TAG, "Recorded metrics for ${metrics.connectionType}: " +
                "duration=${metrics.duration}ms, status=${metrics.status}")
    }

    // ==================== 性能查询API ====================

    /**
     * 获取性能报告
     */
    fun getPerformanceReport(): Map<ConnectionType, PerformanceStats> {
        return performanceData.toMap()
    }

    /**
     * 获取连接成功率
     */
    fun getConnectionSuccessRate(type: ConnectionType): Float {
        return performanceData[type]?.successRate ?: 0f
    }

    /**
     * 获取平均连接时间
     */
    fun getAverageConnectionTime(type: ConnectionType): Long {
        return performanceData[type]?.averageConnectionTime ?: 0L
    }

    /**
     * 获取最大连接时间
     */
    fun getMaxConnectionTime(type: ConnectionType): Long {
        return performanceData[type]?.maxConnectionTime ?: 0L
    }

    /**
     * 获取最小连接时间
     */
    fun getMinConnectionTime(type: ConnectionType): Long {
        return performanceData[type]?.minConnectionTime ?: 0L
    }

    /**
     * 获取实时指标
     */
    fun getRealtimeMetrics(type: ConnectionType): RealtimeMetrics? {
        return realtimeMetrics[type]
    }

    /**
     * 获取所有类型的实时指标
     */
    fun getAllRealtimeMetrics(): Map<ConnectionType, RealtimeMetrics> {
        return realtimeMetrics.toMap()
    }
    
    // ==================== 性能分析 ====================

    /**
     * 检查性能警告
     */
    private fun checkPerformanceWarnings(metrics: ConnectionMetrics) {
        val type = metrics.connectionType
        val stats = performanceData[type] ?: return

        // 检查连接时间
        if (metrics.duration > SLOW_CONNECTION_THRESHOLD_MS) {
            logConnectionWarning(
                type,
                "Slow connection detected: ${metrics.duration}ms for device ${metrics.deviceId}"
            )
        }

        // 检查成功率
        if (stats.totalAttempts >= 10 && stats.successRate < LOW_SUCCESS_RATE_THRESHOLD) {
            logConnectionWarning(
                type,
                "Low success rate detected: ${(stats.successRate * 100).toInt()}% " +
                        "(${stats.successfulConnections}/${stats.totalAttempts})"
            )
        }
    }

    /**
     * 获取性能建议
     */
    fun getPerformanceRecommendations(type: ConnectionType): List<String> {
        val stats = performanceData[type] ?: return emptyList()
        val recommendations = mutableListOf<String>()

        // 基于统计数据提供建议
        when {
            stats.successRate < 0.5f -> {
                recommendations.add("连接成功率过低(${(stats.successRate * 100).toInt()}%)，建议检查网络环境或设备状态")
            }
            stats.successRate < 0.8f -> {
                recommendations.add("连接成功率偏低(${(stats.successRate * 100).toInt()}%)，可考虑优化连接策略")
            }
        }

        when {
            stats.averageConnectionTime > 8000L -> {
                recommendations.add("平均连接时间过长(${stats.averageConnectionTime}ms)，建议优化连接流程")
            }
            stats.averageConnectionTime > 5000L -> {
                recommendations.add("平均连接时间偏长(${stats.averageConnectionTime}ms)，可考虑连接预热")
            }
        }

        if (stats.totalAttempts > 0) {
            val retryRate = stats.failedConnections.toFloat() / stats.totalAttempts
            if (retryRate > 0.3f) {
                recommendations.add("重试率较高(${(retryRate * 100).toInt()}%)，建议检查连接稳定性")
            }
        }

        return recommendations
    }

    // ==================== 内部方法 ====================

    /**
     * 更新实时指标
     */
    private fun updateRealtimeMetrics(
        type: ConnectionType,
        updater: (RealtimeMetrics) -> Unit
    ) {
        val metrics = realtimeMetrics.getOrPut(type) { RealtimeMetrics() }
        updater(metrics)
    }

    /**
     * 记录连接警告
     */
    private fun logConnectionWarning(type: ConnectionType, message: String) {
        logW(ConnectionAnalytics.TAG, "Performance Warning [$type]: $message")
    }

    /**
     * 清除性能数据
     */
    fun clearPerformanceData() {
        performanceData.clear()
        realtimeMetrics.clear()
        logI(TAG, "Performance data cleared")
    }

    /**
     * 重置特定类型的性能数据
     */
    fun resetPerformanceData(type: ConnectionType) {
        performanceData.remove(type)
        realtimeMetrics.remove(type)
        logI(TAG, "Performance data reset for $type")
    }
}
