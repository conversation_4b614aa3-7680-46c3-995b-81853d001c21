package com.link.riderservice.message

import com.link.riderservice.ext.writeInt16BE
import com.link.riderservice.ext.writeInt32BE


/**
 * <AUTHOR>
 * @date 2022/8/9
 */
data class NaviMessage(
    val bitField: Int = 0,
    val frameLength: Int = 0,
    val messageLength: Int = 0,
    val payload: ByteArray? = null,
    val original: ByteArray? = null
) {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as NaviMessage

        if (bitField != other.bitField) return false
        if (frameLength != other.frameLength) return false
        if (messageLength != other.messageLength) return false
        if (payload != null) {
            if (other.payload == null) return false
            if (!payload.contentEquals(other.payload)) return false
        } else if (other.payload != null) return false
        if (original != null) {
            if (other.original == null) return false
            if (!original.contentEquals(other.original)) return false
        } else if (other.original != null) return false

        return true
    }

    override fun hashCode(): Int {
        var result = bitField
        result = 31 * result + frameLength
        result = 31 * result + messageLength
        result = 31 * result + (payload?.contentHashCode() ?: 0)
        result = 31 * result + (original?.contentHashCode() ?: 0)
        return result
    }
}

/**
 * bitField:1byte
 * frameLength:2byte
 * messageLength:0byte or 4 byte
 * +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 * +  bitField  +  frameLength  +  messageLength  +  payload   +
 * +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
 */
fun NaviMessage.toByteArray(): ByteArray {
    var offset = 0

    /** 初始化数据包长度, bitField + frameLength */
    var len = 3
    if (messageLength != 0) {
        /** 加上 messageLength */
        len += 4
    }
    len += frameLength
    val bytes = ByteArray(len)
    bytes[offset] = (bitField and 0xff).toByte()
    offset += 1
    bytes.writeInt16BE(frameLength, offset)
    offset += 2
    if (messageLength != 0) {
        bytes.writeInt32BE(messageLength.toLong(), offset)
        offset += 4
    }
    payload?.let {
        System.arraycopy(it, 0, bytes, offset, frameLength)
    }
    return bytes
}