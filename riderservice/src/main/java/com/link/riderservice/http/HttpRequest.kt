package com.link.riderservice.http

import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL
import java.net.URLEncoder

/**
 * <AUTHOR>
 * @date 2023/2/8
 * @description
 */
class HttpRequest(
    val url: String,
    val method: Method = Method.GET,
    val parameters: Map<String, Any> = mapOf(),
    val headers: Map<String, String> = mapOf(),
    val config: ((HttpURLConnection) -> Unit)? = null
) {
    fun response(): HttpResponse {
        try {
            val url = if (method == Method.GET && parameters.isNotEmpty()) {
                URL("$url?${parameters.query}")
            } else {
                URL(url)
            }
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = method.value
            headers.forEach { (key, value) ->
                connection.setRequestProperty(key, value)
            }
            config?.let { it(connection) }
            if (method == Method.POST && parameters.isNotEmpty()) {
                connection.doOutput = true
                connection.outputStream.use {
                    it.write(parameters.query.toByteArray())
                }
            }
            val response = HttpResponse()
            response.connection = connection
            response.body = connection.inputStream.use {
                BufferedReader(InputStreamReader(it)).use { reader ->
                    reader.readText()
                }
            }
            connection.disconnect()
            return response
        } catch (e: Exception) {
            val response = HttpResponse()
            response.exception = e
            return response
        }
    }
}

enum class Method(val value: String) {
    GET("GET"), HEAD("HEAD"), POST("POST"), PUT("PUT"),
    DELETE("DELETE"), OPTIONS("OPTIONS"), TRACE("TRACE"), PATCH("PATCH")
}

class HttpResponse {
    var connection: HttpURLConnection? = null
    var body: String? = null
    var exception: Exception? = null
    val success: Boolean
        get() {
            connection?.let { return it.responseCode in 200..299 }
            return false
        }
}

val Map<String, Any>.query: String
    get() {
        return this.map { (key, value) -> "$key=${value}" }.joinToString("&")
    }

val Any.urlEncoded: String get() = URLEncoder.encode(toString(), "UTF-8")