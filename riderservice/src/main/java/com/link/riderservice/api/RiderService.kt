package com.link.riderservice.api

import android.app.Application
import android.content.Context
import android.media.projection.MediaProjection
import android.view.Display
import android.view.WindowManager
import com.google.protobuf.ByteString
import com.google.protobuf.MessageLite
import com.link.riderservice.BuildConfig
import com.link.riderservice.R
import com.link.riderservice.api.dto.ArriveDestination
import com.link.riderservice.api.dto.AutoLinkConnect
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.api.dto.GpsSignal
import com.link.riderservice.api.dto.LaneInfo
import com.link.riderservice.api.dto.NaviCross
import com.link.riderservice.api.dto.NaviInfo
import com.link.riderservice.api.dto.NaviInitSuccess
import com.link.riderservice.api.dto.NaviMode
import com.link.riderservice.api.dto.NaviRoute
import com.link.riderservice.api.dto.NaviStart
import com.link.riderservice.api.dto.NaviStop
import com.link.riderservice.api.dto.NaviText
import com.link.riderservice.api.dto.NaviVersionRequest
import com.link.riderservice.api.dto.NotificationInfo
import com.link.riderservice.api.dto.RiderMessage
import com.link.riderservice.api.dto.RiderServiceConfig
import com.link.riderservice.api.dto.WeatherInfo
import com.link.riderservice.connection.ble.BleDevice
import com.link.riderservice.inject.ModuleInject
import com.link.riderservice.message.MessageCallback
import com.link.riderservice.mirror.LockScreenPresentation
import com.link.riderservice.protobuf.RiderProtocol
import com.link.riderservice.protobuf.RiderProtocol.NaviDayOrNight
import com.link.riderservice.protobuf.RiderProtocol.NaviModeChangeResult
import com.link.riderservice.utils.ConfigPreferences
import com.link.riderservice.utils.CrashHandler
import com.link.riderservice.utils.LogLevel
import com.link.riderservice.utils.LogManager
import com.link.riderservice.utils.Preconditions
import com.link.riderservice.utils.TimeUtils
import com.link.riderservice.utils.logD
import com.link.riderservice.utils.logE
import com.link.riderservice.utils.logI
import com.link.riderservice.utils.logW
import java.nio.ByteBuffer

/**
 * RiderService is a singleton class, which is used to communicate with the rider device.
 *
 */
class RiderService private constructor() {
    private val connectionManager by lazy {
        ModuleInject.connectionManager
    }
    private var lockScreenPresentation: LockScreenPresentation? = null
    private val messageManager by lazy { ModuleInject.messageManager }
    private var application: Application? = null
    private var configPreferences: ConfigPreferences? = null
    private var naviMode: NaviMode = NaviMode.SimpleNavi
    private var isBleItemClicked = false
    private var shouldChangeDisplay = true
    private var productKey = "请在连接平台后查看"
    private var uuid = "请在连接平台后查看"
    private var autolinkVersion = "请在连接平台后查看"
    private val messageCallback = object : MessageCallback {
        override fun onMessage(type: Int, message: ByteArray?) {
            when (type) {
                RiderProtocol.NaviMessageId.MSG_WIFI_INFO_NOTIFICATION_VALUE -> {
                    handleWifiInfo(message)
                }

                RiderProtocol.NaviMessageId.MSG_AUTOLINK_STATE_NOTIFICATION_VALUE -> {
                    handleAutolinkState(message)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_RESPONSE_VALUE -> {
                    handleNaviResponse(message)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_MODE_CHANGE_VALUE -> {
                    val naviModeChange = RiderProtocol.NaviModeChange.parseFrom(message)
                    connectionManager.sendNaviModeChange(convertProtocolNaviMode(naviModeChange.naviMode))
                }

                RiderProtocol.NaviMessageId.MSG_WEATHER_INFO_REQUEST_VALUE -> {
                    connectionManager.updateWeatherInfo()
                }

                RiderProtocol.NaviMessageId.MSG_CONFIG_NOTIFICATION_VALUE -> {
                    handleConfig(message)
                }

                RiderProtocol.NaviMessageId.MSG_PROTOCOL_VERSION_NOTIFICATION_VALUE -> {
                    handleProtocolVersion(message)
                }

                RiderProtocol.NaviMessageId.MSG_COMPLICANCE_REQUEST_VALUE -> {
                    handleCompliance(message)
                }

                RiderProtocol.NaviMessageId.MSG_ACTIVE_REQUEST_VALUE -> {
                    handleActiveRequest(message)
                }

                RiderProtocol.NaviMessageId.MSG_AUTHORIZATION_NOTIFICATION_VALUE -> {
                    handleAuthorization(message)
                }

                RiderProtocol.NaviMessageId.MSG_TIME_REQUEST_VALUE -> {
                    sendTimeInfo()
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_MODE_START_RESPONSE_VALUE -> {
                    handleNaviModeStart(message)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_MODE_STOP_RESPONSE_VALUE -> {
                    handleNaviModeStop(message)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_CLOSE_CONNECTION_VALUE -> {
                    closeConnect()
                }


                RiderProtocol.NaviMessageId.MSG_NAVI_DAY_OR_NIGHT_VALUE -> {
                    handleNaviDayOrNight(message)
                }

                RiderProtocol.NaviMessageId.MSG_NAVI_VERSION_RESPONSE_VALUE -> {
                    handleNaviVersion(message)
                }

                else -> {
                    logW(TAG, "unknown message type: $type")
                }
            }
        }
    }

    private fun handleNaviVersion(message: ByteArray?) {
        val version = RiderProtocol.NaviVersionResponse.parseFrom(message)
        connectionManager.naviVersionResponse(version.naviVersion)
    }

    private fun handleNaviDayOrNight(message: ByteArray?) {
        val mapType = RiderProtocol.NaviModeDayOrNight.parseFrom(message)
        logD(TAG, "ChangeMap:${mapType.naviDayOrNight}")
        var type = 0
        type = when (mapType.naviDayOrNight) {
            NaviDayOrNight.NAVI_DAYTIME -> 0
            NaviDayOrNight.NAVI_NIGHT -> 1
            NaviDayOrNight.NAVI_AUTO -> 2
        }
        changeMap(type)
    }

    private fun handleNaviModeStop(message: ByteArray?) {
        val response = RiderProtocol.NaviModeStopResponse.parseFrom(message)
        connectionManager.sendNaviModeStopResponse(response.naviMode)
    }

    private fun handleNaviModeStart(message: ByteArray?) {
        val response = RiderProtocol.NaviModeStartResponse.parseFrom(message)
        connectionManager.sendNaviModeStartResponse(response.naviMode)
    }

    private fun handleAuthorization(message: ByteArray?) {
        val authorizationResult = RiderProtocol.AuthorizationResult.parseFrom(message)
        if (!authorizationResult.result) {
            connectionManager.showDialog("提示", "授权失败，请联系厂商")
            disconnect()
        }
    }

    private fun handleActiveRequest(message: ByteArray?) {
        val activeRequest = RiderProtocol.ActivateRequest.parseFrom(message)
        requestActive(activeRequest)
    }

    private fun handleCompliance(message: ByteArray?) {
        val complianceRequest = RiderProtocol.ComplianceRequest.parseFrom(message)
        connectionManager.requestVerify(
            complianceRequest.productkey,
            complianceRequest.macAddr,
            complianceRequest.uuid,
            complianceRequest.time,
            complianceRequest.licenseSign,
            complianceRequest.sign
        )
        productKey = complianceRequest.productkey
        uuid = complianceRequest.uuid
    }

    private fun handleProtocolVersion(message: ByteArray?) {
        val version = RiderProtocol.ProtocolVerNotification.parseFrom(message)
        connectionManager.checkVersion(version)
    }

    private fun handleConfig(message: ByteArray?) {
        val config = RiderProtocol.ConfigNotification.parseFrom(message)
        val riderServiceConfig = RiderServiceConfig(
            isSupportDvr = config.isSupportDvr,
            isSupportNavi = config.isSupportNavi,
            isSupportScreenNavi = config.isSupportScreenNavi,
            isSupportWeather = config.isSupportWeaNoti,
            isSupportNotification = config.isSupportAndroidNoti,
            isSupportCircularScreen = config.isSupportCircularScreen,
            isSupportCruise = config.isSupportCruiseNavi,
            isSupportMirror = config.isSupportMirrorScreen
        )
        connectionManager.sendRiderServiceConfigChange(riderServiceConfig)
    }

    private fun handleNaviResponse(message: ByteArray?) {
        val response = RiderProtocol.NaviResponse.parseFrom(message)
        connectionManager.sendNaviModeChangeResponse(
            response.naviMode,
            response.isReady
        )
    }

    private fun handleAutolinkState(message: ByteArray?) {
        val stateNotification = RiderProtocol.AutoLinkStateNotification.parseFrom(message)
        logD(TAG, "autolink state:${stateNotification.state}")
        if (stateNotification.state == 0) {
            connectionManager.startAutolinkServiceSuccess()
        } else {
            connectionManager.disconnectWifi()
        }
    }

    private fun handleWifiInfo(message: ByteArray?) {
        logD(
            "connect analysis:",
            "wifi info response::${TimeUtils.getCurrentTimeStr()}"
        )
        val wifiInfo = RiderProtocol.WifiInfoNotification.parseFrom(message)
        connectionManager.startSearchWifiAndConnect(
            getApplication().applicationContext,
            wifiInfo.address,
            wifiInfo.port
        )
    }

    private fun sendTimeInfo() {
        val timeNotification =
            RiderProtocol.TimeNotification.newBuilder().setTime(TimeUtils.getCurrentZeroTimeStr())
                .build()
        if (connectionManager.isBleConnected()) {
            messageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_TIME_NOTIFICATION_VALUE, timeNotification
            )
        }
    }

    private fun requestActive(activeRequest: RiderProtocol.ActivateRequest) =
        connectionManager.requestActivate(
            activeRequest.productKey,
            activeRequest.macAddr,
            activeRequest.time,
            activeRequest.sign
        )


    private fun initConnectManager(context: Context?) {
        connectionManager.init(context)
    }

    private fun sendMessageToRiderService(id: Int, protocolMessage: MessageLite) {
        if (connectionManager.isBleConnected()) {
            messageManager.queueOutgoing(id, protocolMessage)
        }
    }

    fun getOnBleItemClick(): Boolean {
        return isBleItemClicked
    }

    /**
     * 发送信息给仪表盘
     * @param[message] 发送给仪表盘的信息
     * @see RiderMessage
     */
    fun sendMessageToRiderService(message: RiderMessage) {
        when (message) {
            is NotificationInfo -> {
                handleNotificationInfo(message)
            }

            is WeatherInfo -> {
                handleWeatherInfo(message)
            }

            is NaviInfo -> {
                handleNaviInfo(message)
            }

            is ArriveDestination -> {
                handleArriveDestination()
            }

            is NaviStop -> {
                val id = RiderProtocol.NaviMessageId.MSG_NAVI_STOP_VALUE
                val protocolMessage = RiderProtocol.NaviStop.newBuilder().build()
                sendMessageToRiderService(id, protocolMessage)
            }

            is NaviStart -> {
                val id = RiderProtocol.NaviMessageId.MSG_NAVI_START_VALUE
                val protocolMessage = RiderProtocol.NaviStart.newBuilder().build()
                sendMessageToRiderService(id, protocolMessage)
            }

            is GpsSignal -> {
                val id = RiderProtocol.NaviMessageId.MSG_GPS_SIGNAL_WEAK_VALUE
                val protocolMessage = RiderProtocol.GpsSignalWeak.newBuilder().setIsWeak(message.isWeek).build()
                sendMessageToRiderService(id, protocolMessage)
            }

            is NaviInitSuccess -> {
                val id = RiderProtocol.NaviMessageId.MSG_NAVI_INIT_SUCCESS_VALUE
                val protocolMessage = RiderProtocol.NaviInitSuccess.newBuilder().build()
                sendMessageToRiderService(id, protocolMessage)
            }

            is NaviCross -> {
                handleNaviCross(message)
            }

            is NaviText -> {
                handleNaviText(message)
            }

            is LaneInfo -> {
                handleLaneInfo(message)
            }

            is NaviRoute -> {
                handleNaviRoute(message)

            }

            is AutoLinkConnect -> {
                handleAutolinkConnect(message)
            }

            is NaviVersionRequest -> {
                handleNaviVersionRequest()
            }

            else -> {}
        }
    }

    private fun handleNaviVersionRequest() {
        val id = RiderProtocol.NaviMessageId.MSG_NAVI_VERSION_REQUEST_VALUE
        val protocolMessage = RiderProtocol.NaviVersionRequest.newBuilder()
            .build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun handleAutolinkConnect(message: AutoLinkConnect) {
        val id = RiderProtocol.NaviMessageId.MSG_AUTOLINK_CONNECT_VALUE
        logD(TAG, "IP: " + message.ip)
        val protocolMessage = RiderProtocol.AutoLinkConnect.newBuilder()
            .setIp(message.ip)
            .build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun handleNaviRoute(message: NaviRoute) {
        val id = RiderProtocol.NaviMessageId.MSG_NAVI_ROUTE_NOTIFY_VALUE
        val protocolMessage = RiderProtocol.NaviRouteNotify.newBuilder()
            .setDistance(message.distance)
            .setLatitude(message.latitude)
            .setLongitude(message.longitude)
            .setReason(message.reason)
            .setRoadName(message.roadName)
            .setSubTitle(message.subTitle)
            .setIsSuccess(message.isSuccess)
            .setNotifyType(message.notifyType)
            .build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun handleLaneInfo(message: LaneInfo) {
        val id = RiderProtocol.NaviMessageId.MSG_LANE_INFO_VALUE
        val laneInfo = RiderProtocol.LaneInfo.newBuilder()
        for (laneValue in message.backgroundLane) {
            laneInfo.addBackgroundLane(laneValue)
        }
        for (laneValue in message.frontLane) {
            laneInfo.addFrontLane(laneValue)
        }
        laneInfo.laneCount = message.laneCount
        val protocolMessage = laneInfo.build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun handleNaviText(message: NaviText) {
        val id = RiderProtocol.NaviMessageId.MSG_NAVI_TEXT_VALUE
        val protocolMessage =
            RiderProtocol.NaviText.newBuilder().setText(message.text).setType(message.type).build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun handleNaviCross(message: NaviCross) {
        val id = RiderProtocol.NaviMessageId.MSG_NAVI_CROSS_VALUE
        val bytes = message.bitmap.byteCount
        val buffer = ByteBuffer.allocate(bytes)
        message.bitmap.copyPixelsToBuffer(buffer)
        val protocolMessage = RiderProtocol.NaviCross.newBuilder()
            .setWidth(message.width)
            .setHeight(message.height)
            .setBitmap(ByteString.copyFrom(buffer.array()))
            .build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun handleArriveDestination() {
        val id = RiderProtocol.NaviMessageId.MSG_ARRIVE_DEST_VALUE
        val protocolMessage = RiderProtocol.ArriveDestination.newBuilder().build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun handleNaviInfo(message: NaviInfo) {
        val id = RiderProtocol.NaviMessageId.MSG_NAVI_INFO_VALUE
        val protocolMessage = RiderProtocol.NaviInfoNotification.newBuilder()
            .setCurLink(message.curLink)
            .setCurPoint(message.curPoint)
            .setCurStep(message.curStep)
            .setCurStepRetainDistance(message.curStepRetainDistance)
            .setCurStepRetainTime(message.curStepRetainTime)
            .setNaviType(message.naviType)
            .setPathRetainTime(message.pathRetainTime)
            .setPathRetainDistance(message.pathRetainDistance)
            .setRouteRemainLightCount(message.routeRemainLightCount)
            .setPathId(message.pathId)
            .setNextRoadName(message.nextRoadName)
            .setCurrentRoadName(message.currentRoadName)
            .setIconType(message.iconType)
            .setMapType(message.mapType)
            .setTurnIconName(message.turnIconName)
            .build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun handleWeatherInfo(message: WeatherInfo) {
        val id = RiderProtocol.NaviMessageId.MSG_WEATHER_INFO_NOTIFICATION_VALUE
        val protocolMessage = RiderProtocol.WeatherInfoNotification.newBuilder()
            .setWea(message.wea)
            .setTem(message.tem)
            .setHumidity(message.humidity)
            .setPressure(message.pressure)
            .setAltitude(message.altitude)
            .build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun handleNotificationInfo(message: NotificationInfo) {
        val id = RiderProtocol.NaviMessageId.MSG_ANDROID_NOTIFICATION_VALUE
        val protocolMessage = RiderProtocol.AndroidNotification.newBuilder()
            .setAppName(message.appName)
            .setTitle(message.title)
            .setContent(message.content)
            .build()
        sendMessageToRiderService(id, protocolMessage)
    }


    /**
     * 获取配置 SharedPreferences 管理器
     */
    fun getConfigPreferences(): ConfigPreferences {
        return configPreferences ?: throw IllegalStateException("Please call the RiderService#init() first")
    }

    /**
     * 清除自动互联配对
     */
    fun deleteConfig() {
        getConfigPreferences().clearAllConfig()
    }

    /**
     * 获取mProductKey
     */
    fun getProductKey(): String {
        return productKey
    }

    /**
     * 获取uuid
     */
    fun getUuid(): String {
        return uuid
    }

    /**
     * 获取mAutolinkVersion
     */
    fun getAutolinkVersion(): String {
        return autolinkVersion
    }

    /**
     * 赋值mAutolinkVersion
     */
    fun setAutolinkVersion(version: String) {
        autolinkVersion = version
    }

    /**
     * 改变地图昼夜模式
     */
    fun changeMap(type: Int) {
        connectionManager.changeMap(type)
    }

    /**
     * 获取连接状态
     * @return status
     * @see Connection
     */
    fun getConnectStatus(): Connection {
        return connectionManager.getConnectStatus()
    }

    /**
     * 获取当前连接设备
     * @return device
     */
    fun getCurrentConnectDevice(): BleDevice? {
        return connectionManager.getCurrentConnectDevice()
    }

    /**
     * 初始化 RiderLink
     * @param application
     */
    internal fun init(application: Application) {
        <EMAIL> = application
        configPreferences = ConfigPreferences.getInstance(application)
        
        // 初始化统一日志管理系统
        initLogSystem()
        
        CrashHandler.init()
        messageManager.registerCallback(messageCallback)
        initConnectManager(application.applicationContext)
    }

    /**
     * 初始化日志系统
     */
    private fun initLogSystem() {
        // 根据构建类型配置日志
        if (BuildConfig.DEBUG) {
            // 开发环境：启用所有日志
            LogManager.enableLog()
            LogManager.setLogLevel(LogLevel.VERBOSE)
            logI(TAG, "日志系统已初始化 - 开发模式")
        } else {
            // 生产环境：只显示警告和错误
            LogManager.enableLog()
            LogManager.setLogLevel(LogLevel.WARN)
            logE(TAG, "日志系统已初始化 - 生产模式")
        }
    }

    /**
     * 获取 Application
     * @return 返回 Application
     */
    fun getApplication(): Application {
        Preconditions.checkNotNull(application, "Please call the RiderService#init() first")
        return application!!
    }

    /**
     * 开启连接
     * @param device
     */
    fun connect(device: BleDevice) {
        isBleItemClicked = true
        Preconditions.checkNotNull(application)
        connectionManager.connectBle(device, application!!.applicationContext)
    }

    /**
     * 断开连接
     */
    fun disconnect(isManual: Boolean = true) {
        isBleItemClicked = false
        connectionManager.disconnect(isManual)
        productKey = "请在连接平台后查看"
        uuid = "请在连接平台后查看"
    }

    private fun closeConnect() {
        logD(TAG, "closeConnect")
        connectionManager.closeConnect()
    }

    /**
     * 开始扫描 BLE 设备
     */
    fun startBleScan(shouldAutoConnect: Boolean = true) {
        connectionManager.startScan(shouldAutoConnect)
    }

    /**
     * 停止扫描 BLE 设备
     */
    fun stopBleScan() {
        connectionManager.stopScan()
    }

    /**
     * 注册回调
     * @param callback
     * @see RiderServiceCallback
     */
    fun addCallback(callback: RiderServiceCallback) {
        connectionManager.addCallback(callback)
    }


    /**
     * 移除回调
     * @param callback
     * @see RiderServiceCallback
     */
    fun removeCallback(callback: RiderServiceCallback) {
        connectionManager.removeCallback(callback)
    }


    /**
     * 销毁 RiderService
     */
    fun destroy() {
        connectionManager.release()
        connectionManager.destroy(getApplication())
        messageManager.unregisterCallback()
        messageManager.clearMessage()
    }


    /**
     * 开始投屏导航
     */
    internal fun startScreenNavi() {
        logD(TAG, "startScreenNavi")
        if (!connectionManager.startScreenProjection()) {
            logE(TAG, "socket not connected")
            return
        }
    }

    /**
     * 停止投屏导航
     */
    internal fun stopScreenNavi() {
        logD(TAG, "stopScreenNavi")
        if (!connectionManager.stopScreenProjection()) {
            logE(TAG, "socket not connected")
            return
        }
    }

    fun setNaviMode(naviMode: NaviMode) {
        connectionManager.setNaviMode(naviMode)
    }

    fun setMediaProjection(mediaProjection: MediaProjection) {
        connectionManager.setMediaProjection(mediaProjection)
    }


    /**
     * 更换导航模式
     * @param naviMode
     * @see NaviMode
     */
    fun sendNaviModeChange(naviMode: NaviMode) {
        if (!connectionManager.isBleConnected()) {
            return
        }
        logD(TAG, "sendNaviModeChange: ${<EMAIL>} -> $naviMode")
        <EMAIL> = naviMode
        val naviRequest = RiderProtocol.NaviRequest.newBuilder().apply {
            this.naviMode = convertNaviMode(naviMode)
        }.build()
        ModuleInject.messageManager.queueOutgoing(
            RiderProtocol.NaviMessageId.MSG_NAVI_REQUEST_VALUE, naviRequest
        )
    }

    fun sendNaviModeChangeResponse(result: NaviModeChangeResult) {
        if (!connectionManager.isBleConnected()) {
            return
        }
        val naviModeResponse = RiderProtocol.NaviModeChangeResponse.newBuilder().apply {
            naviModeChangeResult = result
        }.build()
        ModuleInject.messageManager.queueOutgoing(
            RiderProtocol.NaviMessageId.MSG_NAVI_MODE_CHANGE_RESPONSE_VALUE, naviModeResponse
        )
    }

    fun needChangeDisplay(): Boolean {
        return shouldChangeDisplay
    }

    fun sendNaviModeStart(naviMode: NaviMode) {
        if (!connectionManager.isBleConnected()) {
            return
        }
        logD(TAG, "sendNaviModeStart: ${<EMAIL>} -> $naviMode")
        shouldChangeDisplay = if (<EMAIL> == NaviMode.Default && naviMode == NaviMode.CruiseNAVI) {
            false
        } else if (<EMAIL> == NaviMode.CruiseNAVI && naviMode == NaviMode.Default) {
            false
        } else if (<EMAIL> == NaviMode.MirrorNAVI && naviMode == NaviMode.MirrorNAVI) {
            false
        } else {
            true
        }
        <EMAIL> = naviMode
        val naviModeStart = RiderProtocol.NaviModeStart.newBuilder().apply {
            this.naviMode = convertNaviMode(naviMode)
        }.build()
        ModuleInject.messageManager.queueOutgoing(
            RiderProtocol.NaviMessageId.MSG_NAVI_MODE_START_VALUE, naviModeStart
        )
    }

    fun sendNaviModeStop(naviMode: NaviMode) {
        if (!connectionManager.isBleConnected()) {
            return
        }
        logD(TAG, "sendNaviModeStop: ${<EMAIL>} -> $naviMode")
        <EMAIL> = naviMode
        val naviModeStop = RiderProtocol.NaviModeStop.newBuilder().apply {
            this.naviMode = convertNaviMode(naviMode)
        }.build()
        ModuleInject.messageManager.queueOutgoing(
            RiderProtocol.NaviMessageId.MSG_NAVI_MODE_STOP_VALUE, naviModeStop
        )
    }

    fun requestLockScreenDisplay() {
        connectionManager.requestLockScreenDisplay()
    }

    @Synchronized
    fun initLockScreenProjection(display: Display?, isSupportCircularScreen: Boolean = false) {
        try {
            if (!(lockScreenPresentation == null || lockScreenPresentation?.display === display)) {
                logI(
                    TAG,
                    "Dismissing presentation because the current route no longer has a presentation display."
                )
                if (lockScreenPresentation?.isShowing == true) {
                    lockScreenPresentation?.dismiss()
                }
                lockScreenPresentation = null
            }
        } catch (th: Throwable) {
            logE(TAG, "initPresentation failed", th)
        }

        if (lockScreenPresentation == null && display != null) {
            val createMirrorPresentation: LockScreenPresentation =
                createMirrorPresentation(
                    getApplication(),
                    display,
                    R.style.PresentationDialog,
                )
            lockScreenPresentation = createMirrorPresentation
            try {
                createMirrorPresentation.show()
            } catch (e: WindowManager.InvalidDisplayException) {
                logE(TAG, "Couldn't show presentation!  Display was removed in the meantime.", e)
                lockScreenPresentation = null
            }
        }
    }

    private fun createMirrorPresentation(
        context: Context, display: Display, theme: Int
    ): LockScreenPresentation {
        return LockScreenPresentation(context, display, theme)
    }

    private fun convertNaviMode(naviMode: NaviMode): RiderProtocol.NaviMode =
        when (naviMode) {
            NaviMode.Default -> RiderProtocol.NaviMode.DEFAULT_NAVI
            NaviMode.SimpleNavi -> RiderProtocol.NaviMode.SIMPLE_NAVI
            NaviMode.ScreenNavi -> RiderProtocol.NaviMode.SCREEN_NAVI
            NaviMode.CruiseNAVI -> RiderProtocol.NaviMode.CRUISE_NAVI
            NaviMode.MirrorNAVI -> RiderProtocol.NaviMode.MIRROR_NAVI
            NaviMode.LockScreenNavi -> RiderProtocol.NaviMode.LOCK_SCREEN_NAVI
            else -> RiderProtocol.NaviMode.NO_NAVI
        }

    private fun convertProtocolNaviMode(naviMode: RiderProtocol.NaviMode): NaviMode =
        when (naviMode) {
            RiderProtocol.NaviMode.NO_NAVI -> NaviMode.NoNavi
            RiderProtocol.NaviMode.SIMPLE_NAVI -> NaviMode.SimpleNavi
            RiderProtocol.NaviMode.SCREEN_NAVI -> NaviMode.ScreenNavi
            RiderProtocol.NaviMode.MIRROR_NAVI -> NaviMode.MirrorNAVI
            RiderProtocol.NaviMode.CRUISE_NAVI -> NaviMode.CruiseNAVI
            RiderProtocol.NaviMode.LOCK_SCREEN_NAVI -> NaviMode.LockScreenNavi
            else -> NaviMode.Default
        }

    companion object {
        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            RiderService()
        }
        private const val TAG = "RiderService"
    }
}