package com.link.riderservice.api

import android.bluetooth.BluetoothDevice
import android.view.Display
import com.link.riderservice.api.dto.Connection
import com.link.riderservice.api.dto.NaviMode
import com.link.riderservice.api.dto.RiderServiceConfig
import com.link.riderservice.connection.ble.BleDevice

/**
 * RiderService 回调
 */
abstract class RiderServiceCallback {
    /**
     * 返回搜索到的ble设备
     * @param bleDevices ble设备列表
     */
    open fun onScanResult(bleDevices: List<BleDevice>) {}

    /**
     * 通知Ble正在搜索
     */
    open fun onScanning() {}

    /**
     * 通知Ble搜索完成
     */
    open fun onScanFinish() {}

    /**
     * 通知连接状态的变化
     * @param notificationStatus [Connection]
     */
    open fun onConnectStatusChange(notificationStatus: Connection) {}

    /**
     * 通知需要蓝牙扫描权限
     */
    open fun onNeedBluetoothScanPermission() {}

    /**
     * 通知需要打开蓝牙
     */
    open fun onRequestOpenBluetooth() {}

    /**
     * 通知需要位置权限
     */
    open fun onNeedLocationPermission() {}

    /**
     * 通知仪表盘的配置改变,只有在连接成功后才会回调
     * @param riderServiceConfig [RiderServiceConfig]
     */
    open fun onConfigChange(riderServiceConfig: RiderServiceConfig) {}

    /**
     * 通知方控控制的导航模式改变
     * @param newNaviMode
     * @see NaviMode
     */
    open fun onNaviModeChange(newNaviMode: NaviMode) {}

    /**
     * 通知投屏导航的屏幕已经准备好
     * @param releasedDisplay
     * @see releasedDisplay
     */
    open fun onDisplayInitialized(releasedDisplay: Display) {}

    /**
     * 通知投屏导航的屏幕已经释放
     * @param releasedDisplay
     * @see releasedDisplay
     */
    open fun onDisplayReleased(releasedDisplay: Display) {}

    /**
     * 视频频道准备好，可以投屏
     */
    open fun onVideoChannelReady() {}

    /**
     * 通知 RiderService 需要天气信息
     */
    open fun onRequestWeatherInfo() {}

    /**
     * 通知 Cluster 已经准备好
     */
    open fun onClusterReady() {}

    /**
     * 通知导航模式切换
     */
    open fun onNaviModeChangeResponse(newNaviMode: NaviMode, isReady: Boolean) {}
    open fun onRequestMediaProjection() {}
    open fun onMirrorStart() {}
    open fun onMirrorStop() {}
    open fun onNaviModeStopResponse(newNaviMode: NaviMode) {}
    open fun onNaviModeStartResponse(newNaviMode: NaviMode) {}
    open fun onEnableNotificationFailed(bluetoothDevice: BluetoothDevice, notificationStatus: Int) {}
    open fun onWifiState(isWifiOpened: Boolean) {}

    /**
     * 在app层打印dialog
     */
    open fun onDialogShow(title:String, message: String) {}
    open fun changeMap(mapType: Int) {}
    open fun naviVersionResponse(navigationVersion: String) {}
}