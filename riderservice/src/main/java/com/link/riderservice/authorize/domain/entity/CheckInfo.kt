package com.link.riderservice.authorize.domain.entity

import com.link.riderservice.utils.JSON
import com.link.riderservice.utils.JSONParser
import com.link.riderservice.utils.compareTo
import com.link.riderservice.utils.get

data class CheckInfo(
    var status: Int
)

class CheckInfoParser : JSONParser<CheckInfo> {
    override fun parse(json: JSON): CheckInfo {
        val checkInfo = CheckInfo(-1)
        checkInfo::status < json["status"]
        return checkInfo
    }
}

