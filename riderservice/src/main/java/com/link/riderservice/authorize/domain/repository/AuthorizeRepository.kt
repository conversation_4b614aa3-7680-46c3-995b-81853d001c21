package com.link.riderservice.authorize.domain.repository

import com.link.riderservice.authorize.domain.entity.Activate
import com.link.riderservice.authorize.domain.entity.CheckInfo

/**
 * <AUTHOR>
 * @date 2022/11/18
 */
internal interface AuthorizeRepository {
    suspend fun requestActivateStatus(
        key: String,
        mac: String,
        time: String,
        sign: String
    ): Result<Activate>

    suspend fun requestCheckStatus(
        key: String,
        mac: String,
        uuid: String,
        time: String,
        licenseSign: String,
        sign: String
    ): Result<CheckInfo>
}