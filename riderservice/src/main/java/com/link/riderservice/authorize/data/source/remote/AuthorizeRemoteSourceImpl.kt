package com.link.riderservice.authorize.data.source.remote

import com.link.riderservice.authorize.domain.entity.Activate
import com.link.riderservice.authorize.domain.entity.ActivateParser
import com.link.riderservice.authorize.domain.entity.CheckInfo
import com.link.riderservice.authorize.domain.entity.CheckInfoParser
import com.link.riderservice.authorize.domain.repository.AuthorizeRepository
import com.link.riderservice.http.HttpRequest
import com.link.riderservice.http.Method
import com.link.riderservice.utils.JSON
import com.link.riderservice.utils.logD

/**
 * <AUTHOR>
 * @date 2022/11/18
 */
internal class AuthorizeRemoteSourceImpl : AuthorizeRepository {
    override suspend fun requestActivateStatus(
        key: String,
        mac: String,
        time: String,
        sign: String
    ): Result<Activate> {
        val request = HttpRequest(
            url = AUTHORIZE_ACTIVATE_BASE_URL,
            method = Method.GET,
            parameters = mapOf(
                "key" to key,
                "mac" to mac,
                "time" to time.replace("\\s".toRegex(), "%20"),
                "sign" to sign
            ),
            headers = mapOf(
                "User-Agent" to "HttpRequest"
            )
        )
        val result = request.response()
        return if (result.exception == null) {
            if (result.success) {
                val json = JSON(result.body)
                val data = ActivateParser().parse(json)
                Result.success(Activate(data.status, data.uuid))
            } else {
                Result.failure(IllegalStateException("Activate failed"))
            }
        } else {
            Result.failure(result.exception!!)
        }
    }

    override suspend fun requestCheckStatus(
        key: String,
        mac: String,
        uuid: String,
        time: String,
        licenseSign: String,
        sign: String
    ): Result<CheckInfo> {
        val request = HttpRequest(
            url = AUTHORIZE_CHECK_BASE_URL,
            method = Method.GET,
            parameters = mapOf(
                "key" to key,
                "mac" to mac,
                "UUID" to uuid,
                "time" to time.replace("\\s".toRegex(), "%20"),
                "licenseSign" to licenseSign,
                "sign" to sign
            ),
            headers = mapOf(
                "User-Agent" to "HttpRequest"
            )
        )
        val result = request.response()
        logD(TAG, "requestCheckStatus: result = ${result.body}")
        return if (result.exception == null) {
            if (result.success) {
                val json = JSON(result.body)
                val data = CheckInfoParser().parse(json)
                Result.success(CheckInfo(data.status))
            } else {
                Result.failure(IllegalStateException("Check failed"))
            }
        } else {
            throw result.exception!!
        }
    }

    companion object {
        private const val AUTHORIZE_BASE_URL = "http://www.riderlinkcloud.com.cn:8085"
        private const val AUTHORIZE_ACTIVATE_BASE_URL = "$AUTHORIZE_BASE_URL/activate"
        private const val AUTHORIZE_CHECK_BASE_URL = "$AUTHORIZE_BASE_URL/check"
        private const val TAG = "AuthorizeRemoteSourceIm"
    }

}