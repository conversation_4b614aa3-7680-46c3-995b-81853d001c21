package com.link.riderservice.authorize.domain.entity

import com.link.riderservice.utils.JSON
import com.link.riderservice.utils.JSONParser
import com.link.riderservice.utils.compareTo
import com.link.riderservice.utils.get

/**
 * <AUTHOR>
 * @date 2022/11/18
 */
data class Activate(
    var status: Int,
    var uuid: String
)

class ActivateParser : JSONParser<Activate> {
    override fun parse(json: JSON): Activate {
        val activate = Activate(0, "")
        activate::status < json["status"]
        activate::uuid < json["uuid"]
        return activate
    }
}
