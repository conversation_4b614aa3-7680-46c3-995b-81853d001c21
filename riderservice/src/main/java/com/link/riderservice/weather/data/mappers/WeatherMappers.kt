package com.link.riderservice.weather.data.mappers

import com.link.riderservice.weather.data.remote.WeatherDto
import com.link.riderservice.weather.domain.entity.WeatherData


/**
 * <AUTHOR>
 * @date 2022/8/25
 */

internal fun WeatherDto.toWeatherData(): WeatherData {
    return WeatherData(
        wea,
        tem,
        humidity,
        pressure,
        air,
        win,
        win_speed,
        win_meter,
        tem_day,
        tem_night,
        wea_img,
        update_time,
        week,
        date,
        city,
        cityid,
        nums.toString()
    )

}

