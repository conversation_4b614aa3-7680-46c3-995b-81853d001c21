package com.link.riderservice.weather.domain.entity

import com.link.riderservice.utils.JSON
import com.link.riderservice.utils.JSONParser
import com.link.riderservice.utils.compareTo
import com.link.riderservice.utils.get

/**
 * <AUTHOR>
 * @date 2022/8/25
 */
data class WeatherData(
    var wea: String = "",
    var tem: String = "",
    var humidity: String = "",
    var pressure: String = "",
    var air: String = "",
    var win: String = "",
    var winSpeed: String = "",
    var winMeter: String = "",
    var temDay: String = "",
    var temNight: String = "",
    var weaImg: String = "",
    var updateTime: String = "",
    var week: String = "",
    var date: String = "",
    var city: String = "",
    var cityId: String = "",
    var nums: String = ""
)

class WeatherDataParser : JSONParser<WeatherData> {
    override fun parse(json: JSON): WeatherData {
        val weatherData = WeatherData()
        weatherData::wea < json["wea"]
        weatherData::tem < json["tem"]
        weatherData::humidity < json["humidity"]
        weatherData::pressure < json["pressure"]
        weatherData::air < json["air"]
        weatherData::win < json["win"]
        weatherData::winSpeed < json["win_speed"]
        weatherData::winMeter < json["win_meter"]
        weatherData::temDay < json["tem_day"]
        weatherData::temNight < json["tem_night"]
        weatherData::weaImg < json["wea_img"]
        weatherData::updateTime < json["update_time"]
        weatherData::week < json["week"]
        weatherData::date < json["date"]
        weatherData::city < json["city"]
        weatherData::cityId < json["cityid"]
        weatherData::nums < json["nums"]
        return weatherData
    }

}