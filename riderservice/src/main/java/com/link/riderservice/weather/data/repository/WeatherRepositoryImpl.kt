package com.link.riderservice.weather.data.repository

import com.link.riderservice.http.HttpRequest
import com.link.riderservice.http.Method
import com.link.riderservice.utils.JSON
import com.link.riderservice.weather.domain.entity.WeatherData
import com.link.riderservice.weather.domain.entity.WeatherDataParser
import com.link.riderservice.weather.domain.repository.WeatherRepository

/**
 * <AUTHOR>
 * @date 2022/8/25
 */

internal class WeatherRepositoryImpl : WeatherRepository {

    override suspend fun getWeather(): Result<WeatherData> {
        return try {
            val request = HttpRequest(
                url = WEATHER_BASE_URL, method = Method.GET, parameters = mapOf(
                    "appid" to APP_ID,
                    "appsecret" to APP_SECRET,
                    "unescape" to UNESCAPE
                ),
                headers = mapOf(
                    "User-Agent" to "HttpRequest"
                )
            )
            val result = request.response().let {
                it.body?.let { data ->
                    val json = JSON(data)
                    WeatherDataParser().parse(json)
                } ?: WeatherData()
            }
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    companion object {
        private const val WEATHER_BASE_URL = "http://v0.yiketianqi.com/free/day"
        private const val APP_ID = "17753928"
        private const val APP_SECRET = "BJeZLb2C"
        private const val UNESCAPE = 1
    }
}