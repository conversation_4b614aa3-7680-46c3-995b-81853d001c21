package com.link.riderservice.ext

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * <AUTHOR>
 * @date 2022/8/9
 */

fun <T> MutableStateFlow<T>.setState(reducer: T.() -> T) {
    this.value = this.value.reducer()
}

fun <T> Flow<T>.collectWithScope(
    coroutineScope: CoroutineScope,
    collector: FlowCollector<T>
) {
    coroutineScope.launch {
        collect(collector)
    }
}