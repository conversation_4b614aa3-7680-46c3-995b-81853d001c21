package com.link.riderservice.utils

/**
 * Created on 2022/12/7.
 * <AUTHOR>
 */
object Preconditions {
    fun checkArgument(expression: <PERSON><PERSON><PERSON>) {
        if (!expression) {
            throw IllegalArgumentException()
        }
    }

    fun checkArgument(expression: <PERSON><PERSON><PERSON>, errorMessage: Any) {
        if (!expression) {
            throw IllegalArgumentException(errorMessage.toString())
        }
    }

    fun checkState(expression: <PERSON><PERSON>an) {
        if (!expression) {
            throw IllegalStateException()
        }
    }

    fun checkState(expression: <PERSON><PERSON><PERSON>, errorMessage: Any) {
        if (!expression) {
            throw IllegalStateException(errorMessage.toString())
        }
    }

    fun <T> checkNotNull(reference: T?): T {
        return reference ?: throw NullPointerException()
    }

    fun <T> checkNotNull(reference: T?, errorMessage: Any): T {
        return reference ?: throw java.lang.NullPointerException(errorMessage.toString())
    }
}