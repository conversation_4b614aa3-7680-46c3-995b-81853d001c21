package com.link.riderservice.utils

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.view.Surface


/**
 * <AUTHOR>
 * @date 2022/6/29
 */

object Platform {
    const val NORMAL = 1
    private const val CLOCKWISE = 2
    private const val FLIP = 3
    private const val ANTICLOCKWISE = 4

    fun getDisplayRotation(rotation: Int): Int {
        when (rotation) {
            Surface.ROTATION_0 -> return NORMAL
            Surface.ROTATION_90 -> return CLOCKWISE
            Surface.ROTATION_180 -> return FLIP
            Surface.ROTATION_270 -> return ANTICLOCKWISE
        }
        return NORMAL
    }


    @Synchronized
    fun getAppName(context: Context, packageName: String): String {
        try {
            val packageManager = context.packageManager
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            val labelRes = packageInfo.applicationInfo.loadLabel(packageManager)
            return labelRes.toString()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    fun packageName(context: Context): String? {
        val manager = context.packageManager
        var name: String? = null
        try {
            val info = manager.getPackageInfo(context.packageName, 0)
            name = info.versionName
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return name
    }


    fun isHuawei(): Boolean {
        return if (Build.BRAND == null) {
            false
        } else {
            "huawei".equals(Build.BRAND, ignoreCase = true) || "honor"
                .equals(Build.BRAND, ignoreCase = true)
        }
    }

    fun isXiaomi(): Boolean {
        return "xiaomi".equals(Build.BRAND, ignoreCase = true)
    }

    fun isOppo(): Boolean {
        return "oppo".equals(Build.BRAND, ignoreCase = true)
    }

    fun isVivo(): Boolean {
        return "vivo".equals(Build.BRAND, ignoreCase = true)
    }

    fun isMeizu(): Boolean {
        return "meizu".equals(Build.BRAND, ignoreCase = true)
    }

    fun isSamsung(): Boolean {
        return "samsung".equals(Build.BRAND, ignoreCase = true)
    }
}