package com.link.riderservice.utils


object BeaconParser {
    fun parseBeacon(bytes: ByteArray): List<BeaconItem> {
        val items = ArrayList<BeaconItem>()
        var i = 0
        while (i < bytes.size) {
            val item = parse(bytes, i)
            i += if (item != null) {
                items.add(item)
                item.len + 1
            } else {
                break
            }
        }
        return items
    }

    private fun parse(bytes: ByteArray, startIndex: Int): BeaconItem? {
        var item: BeaconItem? = null
        if (bytes.size - startIndex >= 2) {
            val length = bytes[startIndex]
            if (length > 0) {
                val type = bytes[startIndex + 1]
                val firstIndex = startIndex + 2
                if (firstIndex < bytes.size) {
                    item = BeaconItem()
                    var endIndex = firstIndex + length - 2
                    if (endIndex >= bytes.size) {
                        endIndex = bytes.size - 1
                    }
                    item.type = type.toInt() and 0xff
                    item.len = length.toInt()
                    item.bytes = bytes.copyOfRange(firstIndex, endIndex + 1)
                }
            }
        }
        return item
    }
}