package com.link.riderservice.utils

import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * Created on 2022/11/28.
 * <AUTHOR>
 */
internal object TimeUtils {
    private const val DEFAULT_PATTERN = "yyyy/MM/dd HH:mm:ss.SSS"

    private fun millis2String(millis: Long): String {
        return SimpleDateFormat(DEFAULT_PATTERN, Locale.getDefault()).format(Date(millis))
    }

    /**
     * 获取当前 0 时区的时间戳
     *
     * @return
     */
    private fun getCurrentZeroTime(): Long {
        //1、取得本地时间：
        val cal = Calendar.getInstance()
        //2、取得时间偏移量：
        val zoneOffset = cal[Calendar.ZONE_OFFSET]
        //3、取得夏令时差：
        val dstOffset = cal[Calendar.DST_OFFSET]
        //4、从本地时间里扣除这些差量，即可以取得UTC时间：
        cal.add(Calendar.MILLISECOND, -(zoneOffset + dstOffset))
        //之后，您再通过调用cal.get(int x)或cal.getTimeInMillis()方法所取得的时间即是UTC标准时间。
        return cal.timeInMillis
    }

    /**
     * 获取当前 0 时区的时间字符串
     *
     * @return
     */
    fun getCurrentZeroTimeStr(): String {
        return millis2String(getCurrentZeroTime())
    }

    fun getCurrentTimeStr(): String {
        return millis2String(System.currentTimeMillis())
    }
}