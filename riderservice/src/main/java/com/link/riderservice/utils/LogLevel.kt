package com.link.riderservice.utils

/**
 * 日志级别枚举
 * <AUTHOR>
 * @date 2024/01/01
 */
enum class LogLevel(val value: Int, val tag: String) {
    /**
     * 详细信息级别
     */
    VERBOSE(2, "V"),
    
    /**
     * 调试信息级别
     */
    DEBUG(3, "D"),
    
    /**
     * 一般信息级别
     */
    INFO(4, "I"),
    
    /**
     * 警告信息级别
     */
    WARN(5, "W"),
    
    /**
     * 错误信息级别
     */
    ERROR(6, "E"),
    
    /**
     * 断言级别
     */
    ASSERT(7, "A");
    
    companion object {
        /**
         * 根据值获取日志级别
         */
        fun fromValue(value: Int): LogLevel {
            return values().find { it.value == value } ?: DEBUG
        }
    }
} 