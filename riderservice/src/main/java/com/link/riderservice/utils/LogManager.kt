package com.link.riderservice.utils

import android.util.Log
import java.util.concurrent.ConcurrentHashMap

/**
 * 统一日志管理器
 * 管理除BLE模块外的所有日志输出
 * <AUTHOR>
 * @date 2024/01/01
 */
object LogManager : Logger {
    
    /**
     * 日志是否全局启用，默认启用
     */
    @Volatile
    private var isLogEnabled: Boolean = true
    
    /**
     * 当前日志级别，默认为VERBOSE（显示所有级别）
     */
    @Volatile
    private var currentLogLevel: LogLevel = LogLevel.VERBOSE
    
    /**
     * 特定标签的日志启用状态
     * key: tag, value: enabled
     */
    private val tagEnabledMap = ConcurrentHashMap<String, Boolean>()
    
    /**
     * 特定标签的日志级别
     * key: tag, value: LogLevel
     */
    private val tagLevelMap = ConcurrentHashMap<String, LogLevel>()

    
    // ==================== 全局控制API ====================
    
    /**
     * 启用日志输出
     */
    fun enableLog() {
        isLogEnabled = true
    }
    
    /**
     * 禁用日志输出
     */
    fun disableLog() {
        isLogEnabled = false
    }
    
    /**
     * 检查日志是否启用
     */
    fun isLogEnabled(): Boolean = isLogEnabled
    
    /**
     * 设置全局日志级别
     * @param level 日志级别
     */
    fun setLogLevel(level: LogLevel) {
        currentLogLevel = level
    }
    
    /**
     * 获取当前全局日志级别
     */
    fun getLogLevel(): LogLevel = currentLogLevel
    
    // ==================== 标签特定控制API ====================
    
    /**
     * 为特定标签启用日志
     * @param tag 标签名
     */
    fun enableLogForTag(tag: String) {
        tagEnabledMap[tag] = true
    }
    
    /**
     * 为特定标签禁用日志
     * @param tag 标签名
     */
    fun disableLogForTag(tag: String) {
        tagEnabledMap[tag] = false
    }
    
    /**
     * 为特定标签设置日志级别
     * @param tag 标签名
     * @param level 日志级别
     */
    fun setLogLevelForTag(tag: String, level: LogLevel) {
        tagLevelMap[tag] = level
    }
    
    /**
     * 移除特定标签的设置（恢复使用全局设置）
     * @param tag 标签名
     */
    fun removeTagSettings(tag: String) {
        tagEnabledMap.remove(tag)
        tagLevelMap.remove(tag)
    }
    
    /**
     * 清除所有标签特定设置
     */
    fun clearAllTagSettings() {
        tagEnabledMap.clear()
        tagLevelMap.clear()
    }
    
    // ==================== 内部方法 ====================
    
    /**
     * 检查是否应该输出日志
     */
    private fun shouldLog(tag: String, level: LogLevel): Boolean {
        // 检查全局开关
        if (!isLogEnabled) {
            return false
        }
        
        // 检查标签特定开关
        val tagEnabled = tagEnabledMap[tag]
        if (tagEnabled == false) {
            return false
        }
        
        // 检查日志级别
        val effectiveLevel = tagLevelMap[tag] ?: currentLogLevel
        return level.value >= effectiveLevel.value
    }
    
    // ==================== Logger接口实现 ====================
    
    override fun v(tag: String, message: String) {
        if (shouldLog(tag, LogLevel.VERBOSE)) {
            Log.v(tag, message)
        }
    }
    
    override fun v(tag: String, message: String, throwable: Throwable?) {
        if (shouldLog(tag, LogLevel.VERBOSE)) {
            Log.v(tag, message, throwable)
        }
    }
    
    override fun d(tag: String, message: String) {
        if (shouldLog(tag, LogLevel.DEBUG)) {
            Log.d(tag, message)
        }
    }
    
    override fun d(tag: String, message: String, throwable: Throwable?) {
        if (shouldLog(tag, LogLevel.DEBUG)) {
            Log.d(tag, message, throwable)
        }
    }
    
    override fun i(tag: String, message: String) {
        if (shouldLog(tag, LogLevel.INFO)) {
            Log.i(tag, message)
        }
    }
    
    override fun i(tag: String, message: String, throwable: Throwable?) {
        if (shouldLog(tag, LogLevel.INFO)) {
            Log.i(tag, message, throwable)
        }
    }
    
    override fun w(tag: String, message: String) {
        if (shouldLog(tag, LogLevel.WARN)) {
            Log.w(tag, message)
        }
    }
    
    override fun w(tag: String, message: String, throwable: Throwable?) {
        if (shouldLog(tag, LogLevel.WARN)) {
            Log.w(tag, message, throwable)
        }
    }
    
    override fun e(tag: String, message: String) {
        if (shouldLog(tag, LogLevel.ERROR)) {
            Log.e(tag, message)
        }
    }
    
    override fun e(tag: String, message: String, throwable: Throwable?) {
        if (shouldLog(tag, LogLevel.ERROR)) {
            Log.e(tag, message, throwable)
        }
    }
    
    override fun isLoggable(level: LogLevel): Boolean {
        return isLogEnabled && level.value >= currentLogLevel.value
    }
} 