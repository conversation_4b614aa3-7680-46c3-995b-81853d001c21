package com.link.riderservice.connection.display.transport

import com.link.riderservice.utils.logD
import java.io.IOException

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
abstract class DataConnection protected constructor() {
    interface Callback {
        /**
         * Called when data connection is established.
         */
        fun onConnected(transport: Transport)

        /**
         * Called when data connection is disconnected.
         */
        fun onDisconnected()
        fun requestLockScreenDisplay()
    }

    protected var mCallback: Callback? = null
    private var mIsInitialized = true

    /**
     * 关闭连接
     */
    fun shutdown() {
        if (mIsInitialized) {
            logD(TAG,"Shutting down...")
            mIsInitialized = false
            mCallback?.onDisconnected()
            mCallback = null
            onShutdown()
            logD(TAG,"Shutdown completed.")
        }
    }

    /**
     * start DataConnection.
     * @param callback 回调
     * @throws IOException may be error [IOException]
     * @see Callback
     */
    @Throws(IOException::class)
    fun start(callback: Callback?) {
        logD(TAG,"Starting...")
        mCallback = callback
        mIsInitialized = true
        onStart()
        logD(TAG,"Start sequence finished.")
    }

    @Throws(IOException::class)
    protected abstract fun onStart()
    protected abstract fun onShutdown()


    companion object {
        const val TAG = "DataConnection"
    }
}