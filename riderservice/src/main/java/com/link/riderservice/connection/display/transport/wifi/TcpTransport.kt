package com.link.riderservice.connection.display.transport.wifi

import com.link.riderservice.connection.display.transport.Transport
import com.link.riderservice.utils.logE
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.net.Socket

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
class TcpTransport constructor(
    private val mSocket: Socket, private val mInputStream: InputStream = mSocket.getInputStream(),
    private val mOutputStream: OutputStream = mSocket.getOutputStream()
) : Transport {
    override fun stopTransport() {
        try {
            mInputStream.close()
            mOutputStream.close()
            mSocket.close()
        } catch (e: IOException) {
            logE(TAG,"Failed to close streams",e)
        }
    }

    @Throws(IOException::class)
    override fun read(b: ByteArray, off: Int, len: Int): Int {
        return mInputStream.read(b, off, len)
    }

    @Throws(IOException::class)
    override fun write(b: ByteArray, off: Int, len: Int) {
        mOutputStream.write(b, off, len)
    }

    override fun isConnected(): Boolean {
        return mSocket.isConnected
    }


    init {
        mSocket.tcpNoDelay = true
    }

    companion object {
        private const val TAG = "TcpTransport"
    }
}