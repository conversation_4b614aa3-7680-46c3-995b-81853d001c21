package com.link.riderservice.connection.display.transport

import java.io.IOException

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
interface Transport {
    /**
     * 停止传输
     */
    fun stopTransport()

    /**
     * 读数据
     * @param b   buffer
     * @param off 偏移量
     * @param len 期望读取的数据长度
     * @return 读取到的数据长度
     * @throws IOException
     */
    @Throws(IOException::class)
    fun read(b: ByteArray, off: Int, len: Int): Int

    /**
     * 写数据
     * @param b   buffer
     * @param off 偏移量
     * @param len 数据长度
     * @throws IOException
     */
    @Throws(IOException::class)
    fun write(b: ByteArray, off: Int, len: Int)

    /**
     * Socket 是否连接
     */
    fun isConnected(): Boolean
}