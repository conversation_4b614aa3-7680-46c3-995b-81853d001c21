package com.link.riderservice.connection.display.transport.wifi

import com.link.riderservice.connection.display.transport.DataConnection
import com.link.riderservice.utils.logD
import com.link.riderservice.utils.logE
import java.io.IOException
import java.net.BindException
import java.net.InetSocketAddress
import java.net.ServerSocket
import java.util.concurrent.atomic.AtomicBoolean

/**
 * <AUTHOR>
 * @date 2020/4/27
 */
class TcpConnection : DataConnection() {
    private var mServerSocket: ServerSocket? = null
    private var mStarted = AtomicBoolean(false)


    @Throws(IOException::class)
    override fun onStart() {
        if (mServerSocket == null || mServerSocket?.isClosed == true) {
            mServerSocket = ServerSocket().apply {
                reuseAddress = true
            }
            try {
                mServerSocket?.bind(InetSocketAddress(LISTEN_PORT))
            } catch (e: BindException) {
                logE(TAG, "${e.message}")
            }
        }
        ConnectThread("TCP Server").start()
    }

    inner class ConnectThread(name: String) : Thread(name) {
        override fun run() {
            if (!mStarted.get()) {
                try {
                    logD(TAG, "Listening on")
                    mStarted.set(true)
                    val client = mServerSocket?.accept()
                    logD("connect analysis:", "tcp connect end::")
                    logD(TAG, "accept ${client?.inetAddress}")
                    client?.let {
                        val transport = TcpTransport(client)
                        mCallback?.onConnected(transport)
                    }
                } catch (e: IOException) {
                    logE(TAG, "Can't listen to socket", e)
                    mCallback?.onDisconnected()
                }
            }
        }
    }

    override fun onShutdown() {
        mStarted.set(false)
        try {
            mServerSocket?.close()
        } catch (e: IOException) {
            logE(TAG, "Can't close server socket", e)
        }
        mServerSocket = null
    }

    companion object {
        private const val LISTEN_PORT = 30512
        private const val TAG = "TcpConnection"
    }

}