package com.link.riderservice.connection.display.protocol.project

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
class BluetoothEndpoint(var mListener: BluetoothCallbacks) :
    NativeObject {
    override val nativeInstance: Long = 0
    fun create(serviceId: Int, nativeGalReceiver: Long): Boolean {
        return nativeInit(serviceId, nativeGalReceiver) == 0
    }

    override fun destroy() {
        nativeShutdown()
    }

    @Throws(IllegalStateException::class)
    private external fun nativeInit(id: Int, nativeGalReceiver: Long): Int
    private external fun nativeShutdown()
    external fun nativeSendPairingRequest(address: String?, pairMethod: Int)
    external fun nativeSendBluetoothStatus(status: Int, unsolicited: Boolean)
}