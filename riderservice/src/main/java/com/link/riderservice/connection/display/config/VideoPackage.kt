package com.link.riderservice.connection.display.config


/**
 * 视频包
 * <AUTHOR>
 * @date 2019/12/6
 * @property data 视频数据
 * @property size 视频数据大小
 * @property flags 视频数据标识
 */
data class VideoPackage(
    var data: ByteArray = byteArrayOf(),
    var size: Int = 0,
    var flags: Int = 0,
) {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as VideoPackage

        if (!data.contentEquals(other.data)) return false
        if (size != other.size) return false
        if (flags != other.flags) return false

        return true
    }

    override fun hashCode(): Int {
        var result = data.contentHashCode()
        result = 31 * result + size
        result = 31 * result + flags
        return result
    }
}