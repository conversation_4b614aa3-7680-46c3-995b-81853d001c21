package com.link.riderservice.connection.display.protocol.project

import android.util.SparseArray
import androidx.core.util.forEach
import com.link.riderservice.connection.display.protocol.source.ALServiceBase
import com.link.riderservice.connection.display.protocol.source.ALTransport
import com.link.riderservice.connection.display.transport.Transport
import com.link.riderservice.utils.logE
import java.util.Calendar

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
class GalReceiver(
    phoneInfo: PhoneInfo,
    private val appMessageListener: AppMessageListener,
    private val byeByeHandler: ByeByeHandler
) : NativeObject,
    ControllerCallbacks {
    private val mRegisteredServices = SparseArray<ALServiceBase>()
    private var mGalIO: ALTransport?
    override val nativeInstance: Long = 0
    private var mStopped = false
    private var huMake: String? = null

    interface AppMessageListener {
        fun onAutoRotationRequest(isAutoed: Boolean)
        fun onDisconnected()
        fun onUnrecoverableError(err: Int)
        fun onVersionCallback(major: Short, minor: Short)
        fun onCarInfoCallback(
            id: String?, make: String?, model: String?, year: String?, huIc: String?,
            huMake: String?,
            huModel: String?, huSwBuild: String?, huSwVersion: String?,
            huSeries: String?,
            huMuVersion: String?, checkSum: Int
        )

        fun onScreenOrientationInquire()
        fun onForceLandscapeRequest(isLandscape: Boolean)
        fun onExitRequest()
        fun onRunningState(state: Protos.State)
        fun onVideoChannelReady()
    }

    interface ByeByeHandler {
        fun onByeByeRequest(reason: Int)
        fun onByeByeResponse()
    }

    override fun destroy() {
        stop()
        destroyCarServices()
        nativeShutdown()
        mGalIO = null
    }

    fun start() {
        if (mStopped) {
            throw RuntimeException("can't be started after teardown")
        }
        startCarServices()
        nativeStart()
    }

    fun sendByeByeRequest(code: Int) {
        nativeSendByeByeRequest(code)
    }

    fun sendExitResponse() {
        nativeSendExitResponse()
    }

    fun startTransport(transport: Transport?) {
        mGalIO?.start(transport)
    }

    /**
     * stop gal.
     */
    fun stop() {
        if (mStopped) {
            return
        }
        mStopped = true
        nativePrepareShutdown()
        mGalIO?.stop()
    }

    /**
     * register channel.
     *
     * @param serviceId channel id
     * @param service   channel instance
     */
     fun registerCarService(serviceId: Int, service: ALServiceBase) {
        if (mStopped) {
            throw RuntimeException("can't register services after teardown")
        }
        require(mRegisteredServices[serviceId] == null) { "this service ID is already taken" }
        mRegisteredServices.put(serviceId, service)
        if (!service.create(serviceId, nativeInstance)) {
            logE(TAG,"Service $serviceId failed to init.")
        }
    }

    private fun startCarServices() {
        mRegisteredServices.forEach { serviceId, service ->
            if (!nativeRegister(service.nativeInstance)) {
                logE(TAG,"Service $serviceId failed to register.")
            }
        }
    }

    /**
     * close channel.
     */
    fun destroyCarServices() {
        mRegisteredServices.forEach { _, service ->
            service.destroy()
        }
        mRegisteredServices.clear()
    }

    override fun unrecoverableErrorCallback(err: Int) {
        appMessageListener.onUnrecoverableError(err)
    }

    override fun screenOrientationInquire() {
        appMessageListener.onScreenOrientationInquire()
    }

    override fun pingResponseCallback(timestamp: Long) {}
    override fun pingRequestCallback(timestamp: Long, bugReport: Boolean) {
    }

    override fun navigationFocusCallback(type: Int) {}
    override fun forceLandscapeRequestCallback(force: Boolean) {
        appMessageListener.onForceLandscapeRequest(force)
    }

    override fun byeByeResponseCallback() {
        byeByeHandler.onByeByeResponse()
    }

    override fun exitRequestCallback() {
        appMessageListener.onExitRequest()
    }

    override fun byeByeRequestCallback(reason: Int) {
        byeByeHandler.onByeByeRequest(reason)
    }

    override fun authCompleteCallback() {
        appMessageListener.onRunningState(state = Protos.State.PROBE_SUCCESS)
    }

    override fun audioFocusNotificationCallback(request: Int, unsolicited: Boolean) {}
    override fun versionResponseCallback(major: Short, minor: Short) {
        appMessageListener.onVersionCallback(major, minor)
    }

    override fun serviceDiscoveryResponseCallback(
        id: String?, make: String?, model: String?, year: String?,
        huIc: String?, huMake: String?, huModel: String?,
        huSwBuild: String?, huSwVersion: String?,
        huSeries: String?, huMuVersion: String?,
        checkSum: Int
    ) {
        this.huMake = huMake
        appMessageListener.onCarInfoCallback(
            id,
            make,
            model,
            year,
            huIc,
            huMake,
            huModel,
            huSwBuild,
            huSwVersion,
            huSeries,
            huMuVersion,
            checkSum
        )
    }

    override fun autoRotationRequest(autoed: Boolean) {
        appMessageListener.onAutoRotationRequest(autoed)
    }

    override fun screenResolutionInquire() {
        appMessageListener.onScreenOrientationInquire()
    }

    override fun timeDateInquire() {
        val calendar = Calendar.getInstance()
        sendTimeDateNotification(
            calendar[Calendar.YEAR], calendar[Calendar.MONTH] + 1,
            calendar[Calendar.DAY_OF_MONTH], calendar[Calendar.HOUR_OF_DAY],
            calendar[Calendar.MINUTE], calendar[Calendar.SECOND],
            calendar[Calendar.MILLISECOND] * 1000 * 1000,
            calendar[Calendar.WEEK_OF_YEAR],
            calendar[Calendar.DAY_OF_WEEK]
        )
    }

    private external fun nativeInit()
    private external fun nativeStart()
    private external fun nativePrepareShutdown()
    private external fun nativeShutdown()
    private external fun nativeQueueIncoming(buf: ByteArray, len: Int): Int
    private external fun nativeGetAdditionalBytesToRead(buf: ByteArray): Int
    private external fun nativeGetEncodedFrame(buf: ByteArray): Int
    private external fun nativeRegister(nativeProtocolEndpoint: Long): Boolean
    private external fun nativeSetPhoneInfo(info: PhoneInfo)
    private external fun nativePing(timestamp: Long, bugReport: Boolean)
    private external fun nativeSetNavFocus(type: Int)
    private external fun nativeSendByeByeRequest(code: Int)
    private external fun nativeSendByeByeResponse()
    private external fun nativeSendExitResponse()
    external fun nativeSendScreenOrientationNotifi(orientation: Int, rotation: Int)
    external fun nativeSendUpdateVehicleIdNotifi(id: String?)
    private external fun nativeSendRunningStateNotifi(state: Int)
    private external fun nativeSendAutoRotationNotifi(isAutoed: Boolean)
    external fun nativeSendScreenResolutionNotification(
        width: Int, height: Int,
        isRequired: Boolean
    )

    private external fun sendTimeDateNotification(
        year: Int, month: Int, day: Int, hour: Int, minute: Int,
        second: Int,
        nanosecond: Int, week: Int, dayOfWeek: Int
    )

    companion object {
        private const val TAG = "GalReceiver"
        private const val PING_CHECK_TIMEOUT = 7000
    }

    init {
        val frameParser: ALTransport.FrameParser = object : ALTransport.FrameParser {
            override fun readerTerminated() {
                appMessageListener.onDisconnected()
            }

            override fun onIoError() {
                unrecoverableErrorCallback(-251)
            }

            override fun getFrameSizeToRead(header: ByteArray, length: Int): Int {
                return nativeGetAdditionalBytesToRead(header)
            }

            override fun enqueueIncomingFrame(frame: ByteArray, length: Int) {
                nativeQueueIncoming(frame, length)
            }

            override fun dequeueOutgoingFrame(buffer: ByteArray): Int {
                return nativeGetEncodedFrame(buffer)
            }
        }
        mGalIO = ALTransport(frameParser)
        nativeInit()
        nativeSetPhoneInfo(phoneInfo)
    }
}