package com.link.riderservice.connection.display.protocol.source

import com.link.riderservice.connection.display.config.BluetoothConfig
import com.link.riderservice.connection.display.protocol.project.BluetoothCallbacks
import com.link.riderservice.connection.display.protocol.project.BluetoothEndpoint
import com.link.riderservice.connection.display.protocol.project.Protos
import com.link.riderservice.utils.logW
import java.util.TimerTask
import java.util.concurrent.ScheduledExecutorService

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
internal class ALBluetoothEndpoint constructor(
    private val mConfig: BluetoothConfig,
    private val bluetoothListener: BluetoothListener
) :
    ALServiceBase {


    interface BluetoothListener {
        fun onInit(carAddress: String?)

        fun onPairingRequest(carAddress: String?, alreadyPaired: Boolean)
    }

    private var mBluetoothEndpoint: BluetoothEndpoint? = null
    private var mCarBluetoothAddress: String? = null
    private var mScheduledExecutorService: ScheduledExecutorService? = null
    fun sendBluetoothStatus(status: Int, unsolicited: Boolean) {
        mBluetoothEndpoint?.nativeSendBluetoothStatus(status, unsolicited)
    }

    fun sendPairRequest(address: String?) {
        mConfig.address = address
    }

    override fun destroy() {
        if (mScheduledExecutorService != null) {
            mScheduledExecutorService?.shutdownNow()
            mScheduledExecutorService = null
        }
        mBluetoothEndpoint?.destroy()
    }

    override fun create(serviceId: Int, nativeGalReceiver: Long): Boolean {
        return mBluetoothEndpoint?.create(serviceId, nativeGalReceiver) ?: false
    }

    override fun start(): Boolean {
        return true
    }

    override val serviceType: Int
        get() = ALServiceBase.AL_SERVICE_BLUETOOTH
    override val nativeInstance: Long
        get() = mBluetoothEndpoint?.nativeInstance ?: 0

    private val mTimerTask: TimerTask = object : TimerTask() {
        override fun run() {
            mBluetoothEndpoint?.nativeSendPairingRequest(
                mConfig.address,
                Protos.BLUETOOTH_PAIRING_NUMERIC_COMPARISON
            )
        }
    }

    companion object {
        private const val TAG = "ALBluetoothEndpoint"
    }

    init {
        val listener: BluetoothCallbacks = object : BluetoothCallbacks {
            override fun onChannelOpened(): Int {
                bluetoothListener.onInit(mCarBluetoothAddress)
                return Protos.STATUS_SUCCESS
            }

            override fun discoverBluetoothService(
                carAddress: String,
                methodsBitmap: Int
            ): Boolean {
                mCarBluetoothAddress = carAddress
                return true
            }

            override fun onAuthenticationData(authData: String?) {
                //do nothing
            }

            override fun onPhoneBluetoothStatusInquire() {
                //do nothing
            }

            override fun onPairingResponse(status: Int, alreadyPaired: Boolean) {
                if (status == Protos.STATUS_SUCCESS) {
                    if (mScheduledExecutorService != null) {
                        mScheduledExecutorService?.shutdownNow()
                        mScheduledExecutorService = null
                    }
                } else {
                    logW(TAG, "car's bluetooth is busy")
                }
            }
        }
        mBluetoothEndpoint = BluetoothEndpoint(listener)
    }
}