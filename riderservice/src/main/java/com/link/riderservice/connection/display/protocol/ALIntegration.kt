package com.link.riderservice.connection.display.protocol

import android.os.Build
import com.link.riderservice.connection.display.config.AlConfig
import com.link.riderservice.connection.display.config.VideoPackage
import com.link.riderservice.connection.display.protocol.project.GalReceiver
import com.link.riderservice.connection.display.protocol.project.PhoneInfo
import com.link.riderservice.connection.display.protocol.project.Protos
import com.link.riderservice.connection.display.protocol.source.ALServiceBase
import com.link.riderservice.connection.display.protocol.source.ALVideoSource
import com.link.riderservice.connection.display.transport.Transport
import com.link.riderservice.utils.Platform.getDisplayRotation

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
internal class ALIntegration {
    companion object {
        init {
            /**
             * 加载 libaljni.so
             */
            System.loadLibrary("aljni")
        }
    }

    private var mGalReceiver: GalReceiver? = null
    private var mVideoSource: ALVideoSource? = null
    private var isExited = true

    /**
     * 初始化
     * @param config 配置
     * @param videoListener 视频监听
     * @param appMessageListener 应用消息监听
     * @param byeByeHandler 再见处理
     * @param autoStart 是否自动启动 默认 false
     */
    fun init(
        config: AlConfig,
        videoListener: ALVideoSource.IVideoCallback,
        appMessageListener: GalReceiver.AppMessageListener,
        byeByeHandler: GalReceiver.ByeByeHandler,
        autoStart: Boolean = false
    ) {
        mVideoSource = ALVideoSource(
            config.videoCfg, videoListener, appMessageListener, autoStart,
            config.remoteHost
        )
        val phoneInfo = PhoneInfo(
            Build.MANUFACTURER + config.appName, Build.MODEL,
            config.version, config.phoneName, config.screenWidth,
            config.screenHeight
        )
        mGalReceiver = GalReceiver(phoneInfo, appMessageListener, byeByeHandler)
    }

    /**
     * 开启 GAL
     */
    fun start(transport: Transport?) {
        mGalReceiver?.startTransport(transport)
    }

    /**
     * 销毁 GAL
     */
    fun destroy() {
        mGalReceiver?.destroy()
        mGalReceiver = null
        //mBluetooth = null
        mVideoSource = null
    }

    /**
     * 发送手机屏幕信息
     * @param isLand 是否横屏
     * @param rotation 旋转角度
     */
    @Synchronized
    fun sendOrientation(isLand: Int, rotation: Int) {
        if (!isExited) {
            mGalReceiver?.nativeSendScreenOrientationNotifi(
                isLand,
                getDisplayRotation(rotation)
            )
        }

    }

    /**
     * 发送分辨率通知
     * @param width 屏幕宽度
     * @param height 屏幕高度
     * @param isRequired 是否是平台主动请求
     */
    @Synchronized
    fun sendResolutionNotification(width: Int, height: Int, isRequired: Boolean) {
        if (!isExited) {
            mGalReceiver?.nativeSendScreenResolutionNotification(
                width,
                height,
                isRequired
            )
        }
    }

    /**
     * 发送当前的 focus 给平台，该函数为了控制平台的焦点
     * @param focus [Protos.VIDEO_FOCUS_PROJECTED] or [Protos.VIDEO_FOCUS_NATIVE]
     * @param reason 原因
     * @see Protos
     */
    @Synchronized
    fun sendEncoderState(focus: Int, reason: Int) {
        if (!isExited) {
            mVideoSource?.sendVideoFocusRequestNotify(0, focus, reason)
        }
    }

    /**
     * 发送视频帧给平台
     * @param pkg 视频数据
     * @see VideoPackage
     */
    @Synchronized
    fun sendFrame(pkg: VideoPackage) {
        if (!isExited) {
            mVideoSource?.sendData(pkg)
        }
    }

    /**
     * 发送再见请求给平台
     */
    fun sendByeByeRequest() {
        mGalReceiver?.sendByeByeRequest(1)
    }

    private fun startAllChanel() {
        if (mGalReceiver != null) {
            if (mVideoSource != null) {
                mGalReceiver?.registerCarService(
                    ALServiceBase.AL_SERVICE_VIDEO_SOURCE,
                    mVideoSource!!
                )
            }
            mGalReceiver?.start()
        }
    }

    /**
     * 退出
     */
    @Synchronized
    fun exit() {
        mVideoSource?.exitVideoSession()
        mGalReceiver?.destroyCarServices()
        mGalReceiver?.sendExitResponse()
        isExited = true
    }

    /**
     * 启动所有的通道
     */
    @Synchronized
    fun startAllChannel() {
        startAllChanel()
        isExited = false
    }

    fun requestLockScreenDisplay() {
        mVideoSource?.sendDisplayAreaChangeResponse()
    }
}