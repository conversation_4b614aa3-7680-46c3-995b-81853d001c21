package com.link.riderservice.connection.ble

import android.bluetooth.BluetoothDevice
import com.link.riderservice.ble.scanner.ScanResult
import com.link.riderservice.utils.BeaconParser

/**
 * <AUTHOR>
 * @date 2022/8/9
 */

data class BleDevice(
    val scanResult: ScanResult,
    val device: BluetoothDevice = scanResult.device,
    val name: String? = scanResult.scanRecord?.deviceName,
    val rssi: Int = if (scanResult.rssi < 0) scanResult.rssi else -128,
) {
    fun update(scanResult: ScanResult): BleDevice {
        return copy(
            scanResult = scanResult,
            rssi = if (scanResult.rssi < 0) scanResult.rssi else rssi,
        )
    }

    fun completeLocalName(): String? = scanResult.scanRecord?.let {
        it.bytes?.let { bytes ->
            BeaconParser.parseBeacon(bytes)
                .find { item ->
                    item.type == 0x09
                }?.bytes?.toString(Charsets.UTF_8)
        }
    }
}
