package com.link.riderservice.connection.display.protocol.source

import android.os.Process
import android.os.Trace
import com.link.riderservice.connection.display.transport.Transport
import com.link.riderservice.utils.logD
import com.link.riderservice.utils.logE
import java.io.IOException
import java.util.concurrent.atomic.AtomicBoolean

/**
 * <AUTHOR>
 * @date 2017/9/19
 * @desc Input/output processing for GAL.
 */
internal class ALTransport(private val mFrameParser: FrameParser) {
    interface FrameParser {
        fun getFrameSizeToRead(header: ByteArray, length: Int): Int
        fun enqueueIncomingFrame(frame: ByteArray, length: Int)
        fun dequeueOutgoingFrame(buffer: ByteArray): Int
        fun readerTerminated()
        fun onIoError()
    }

    private val mStopRequested = AtomicBoolean(false)
    private var mTransport: Transport? = null

    /**
     * 开启数据交互
     * @param transport
     * @see Transport
     */
    fun start(transport: Transport?) {
        mTransport = transport
        mStopRequested.set(false)
        mReaderThread.start()
        mWriterThread.start()
    }

    /**
     * 关闭数据交互
     */
    fun stop() {
        if (requestStop()) {
            mTransport?.stopTransport()
        }
    }

    private val isStopRequested: Boolean
        get() = !mStopRequested.get()

    private fun requestStop(): Boolean {
        return !mStopRequested.getAndSet(true)
    }

    private fun hexdumpBuffer(buf: ByteArray, len: Int): String {
        val sb = StringBuilder()
        for (i in 0 until len) {
            sb.append(String.format(" %02x", buf[i]))
            if (i == 32) {
                sb.append("...")
                break
            }
        }
        return sb.toString()
    }

    private val mReaderThread: Thread = object : Thread("ReaderThread") {
        private fun read(buf: ByteArray, offset: Int, len: Int): Int {
            if (DBG_TRACE) {
                Trace.beginSection("read")
            }
            var bytesRead = 0
            while (bytesRead < len && isStopRequested) {
                bytesRead += try {
                    val nbytes = mTransport?.read(buf, offset + bytesRead, len - bytesRead) ?: 0
                    if (nbytes > 0) {
                        nbytes
                    } else {
                        throw IOException("the peer has performed an orderly shutdown")
                    }
                } catch (e: IOException) {
                    e.printStackTrace()
                    logE(TAG, "Caught exception on read. Exiting")
                    <EMAIL>()
                    mFrameParser.onIoError()
                    break
                }
            }
            if (DBG_TRACE) {
                Trace.endSection()
            }
            return bytesRead
        }

        val HEADER_READ_LENGTH = 4
        override fun run() {
            Process.setThreadPriority(READER_THREAD_PRIORITY)
            val buf = ByteArray(MAX_FRAME_SIZE)
            while (isStopRequested) {
                if (DBG_TRACE) {
                    Trace.beginSection("readerThread")
                }
                var bytesRead = read(buf, 0, HEADER_READ_LENGTH)
                if (bytesRead != HEADER_READ_LENGTH) {
                    logE(TAG, "read returned $bytesRead while expecting $HEADER_READ_LENGTH")
                    break
                }
                val len = mFrameParser.getFrameSizeToRead(buf, HEADER_READ_LENGTH)
                bytesRead = read(buf, HEADER_READ_LENGTH, len)
                if (bytesRead != len) {
                    logE(TAG, "read returned $bytesRead while expecting $len")
                    break
                }
                if (VERBOSE_LOGGING_ENABLED) {
                    logD(TAG, "Got frame:" + hexdumpBuffer(buf, len + HEADER_READ_LENGTH))
                }
                if (isStopRequested) {
                    mFrameParser.enqueueIncomingFrame(buf, len + HEADER_READ_LENGTH)
                }
                if (DBG_TRACE) {
                    Trace.endSection()
                }
            }
            mFrameParser.readerTerminated()
        }
    }
    private val mWriterThread: Thread = object : Thread("WriterThread") {
        override fun run() {
            Process.setThreadPriority(WRITER_THREAD_PRIORITY)
            val buf = ByteArray(MAX_FRAME_SIZE)
            try {
                while (isStopRequested) {
                    if (DBG_TRACE) {
                        Trace.beginSection("writerThread")
                    }
                    val len = mFrameParser.dequeueOutgoingFrame(buf)
                    if (len == 0) {
                        logE(TAG, "Writer thread shutting down")
                        break
                    }
                    if (VERBOSE_LOGGING_ENABLED) {
                        logD(TAG, "Sending frame:" + hexdumpBuffer(buf, len))
                    }
                    if (DBG_TRACE) {
                        Trace.beginSection("write")
                    }
                    val retryMax = 1
                    var retryCount = 0
                    while (retryCount < retryMax && isStopRequested) {
                        try {
                            mTransport?.write(buf, 0, len)
                            break
                        } catch (e: IOException) {
                            e.printStackTrace()
                            retryCount++
                        }
                    }
                    if (retryCount >= retryMax) {
                        throw IOException("max retry reached")
                    }
                    if (DBG_TRACE) {
                        // for both writerThread and write
                        Trace.endSection()
                        Trace.endSection()
                    }
                }
            } catch (e: IOException) {
                logE(TAG, "Caught exception on write. Exiting", e)
                <EMAIL>()
                mFrameParser.onIoError()
            }
        }
    }

    companion object {
        private const val TAG = "InputOutputThreads"
        private const val DBG_TRACE = false
        private const val VERBOSE_LOGGING_ENABLED = false
        private const val MAX_FRAME_SIZE = 4 + 4 + 65536
        private const val READER_THREAD_PRIORITY = Process.THREAD_PRIORITY_URGENT_AUDIO
        private const val WRITER_THREAD_PRIORITY = Process.THREAD_PRIORITY_URGENT_AUDIO
    }
}