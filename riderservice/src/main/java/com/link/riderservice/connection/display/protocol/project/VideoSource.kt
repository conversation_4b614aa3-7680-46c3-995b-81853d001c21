package com.link.riderservice.connection.display.protocol.project

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
class VideoSource(
    var mVideoListener: VideoSourceCallbacks, private val mAutoStartProjection: Boolean,
    private val mRemoteHost: String?
) : NativeObject {
    override val nativeInstance: Long = 0
    fun create(id: Int, nativeGalReceiver: Long): Boolean {
        return nativeInit(id, nativeGalReceiver, mAutoStartProjection, mRemoteHost) == 0
    }

    override fun destroy() {
        nativeShutdown()
    }

    @Throws(IllegalStateException::class)
    private external fun nativeInit(
        id: Int, nativeGalReceiver: Long, autoStart: Boolean,
        remoteHost: String?
    ): Int

    private external fun nativeShutdown()
    external fun sendSetup(type: Int)
    external fun sendStart(configIndex: Int, sessionId: Int, width: Int, height: Int)
    external fun sendStop()
    external fun sendData(timeStamp: Long, data: ByteArray?, len: Int, flags: Int)
    external fun sendVideoFocusRequestNotifi(channelId: Int, mode: Int, reason: Int)
    external fun sendDisplayAreaChangeResponse()
    external fun sendVideoOrientation(isLandscape: Boolean)
    external fun isStartResponseMessageExist(): Boolean
}