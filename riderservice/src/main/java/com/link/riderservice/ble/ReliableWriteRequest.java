/*
 * Copyright (c) 2018, Nordic Semiconductor
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 *
 * 3. Neither the name of the copyright holder nor the names of its contributors may be used to endorse or promote products derived from this
 * software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
 * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE
 * USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

package com.link.riderservice.ble;

import android.bluetooth.BluetoothGatt;
import android.os.Handler;

import androidx.annotation.IntRange;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.link.riderservice.ble.callback.AfterCallback;
import com.link.riderservice.ble.callback.BeforeCallback;
import com.link.riderservice.ble.callback.FailCallback;
import com.link.riderservice.ble.callback.InvalidRequestCallback;
import com.link.riderservice.ble.callback.SuccessCallback;

/**
 * This queue performs the operations using Reliable Write, also known as Queued Write, procedure.
 * Before the first operation is executed, the Reliable Write procedure is started. Then, all
 * operations are executed one by one.
 *
 * Cancellation will cause the Reliable Write procedure to be aborted and all operations shall be
 * discarded on the device side.
 *
 * @see BluetoothGatt#beginReliableWrite()
 * @see BluetoothGatt#executeReliableWrite()
 * @see BluetoothGatt#abortReliableWrite()
 */
@SuppressWarnings("unused")
public final class ReliableWriteRequest extends RequestQueue {
	private boolean initialized;
	private boolean closed;

	@NonNull
	@Override
	ReliableWriteRequest setRequestHandler(@NonNull final RequestHandler requestHandler) {
		super.setRequestHandler(requestHandler);
		return this;
	}

	@NonNull
	@Override
	public ReliableWriteRequest setHandler(@Nullable final Handler handler) {
		super.setHandler(handler);
		return this;
	}

	@Override
	@NonNull
	public ReliableWriteRequest done(@NonNull final SuccessCallback callback) {
		super.done(callback);
		return this;
	}

	@Override
	@NonNull
	public ReliableWriteRequest fail(@NonNull final FailCallback callback) {
		super.fail(callback);
		return this;
	}

	@NonNull
	@Override
	public ReliableWriteRequest invalid(@NonNull final InvalidRequestCallback callback) {
		super.invalid(callback);
		return this;
	}

	@Override
	@NonNull
	public ReliableWriteRequest before(@NonNull final BeforeCallback callback) {
		super.before(callback);
		return this;
	}

	@NonNull
	@Override
	public ReliableWriteRequest then(@NonNull final AfterCallback callback) {
		super.then(callback);
		return this;
	}

	@NonNull
	@Override
	public ReliableWriteRequest timeout(@IntRange(from = 0) final long timeout) {
		super.timeout(timeout);
		return this;
	}

	@NonNull
	@Override
	public ReliableWriteRequest add(@NonNull final Operation operation) {
		super.add(operation);
		// Make sure the write request uses splitting, as Long Write is not supported
		// in Reliable Write sub-procedure.
		if (operation instanceof WriteRequest) {
			((WriteRequest) operation).forceSplit();
		}
		return this;
	}

	/**
	 * Alias for {@link #cancel()}.
	 */
	public void abort() {
		cancel();
	}

	@Override
	public int size() {
		int size = super.size();

		// Add Begin Reliable Write
		if (!initialized)
			size += 1;

		// Add Execute or Abort Reliable Write
		if (!closed)
			size += 1;
		return size;
	}

	@Override
    Request getNext() {
		if (!initialized) {
			initialized = true;
			return newBeginReliableWriteRequest();
		}
		if (super.isEmpty()) {
			closed = true;

			if (cancelled)
				return newAbortReliableWriteRequest();
			return newExecuteReliableWriteRequest();
		}
		return super.getNext();
	}

	@Override
	boolean hasMore() {
		// If no operations were added, consider the RW request empty, no requests will be executed.
		if (!initialized)
			return super.hasMore();
		return !closed;
	}

	@Override
	void cancelQueue() {
		cancelled = true;
		super.cancelQueue();
	}
}
