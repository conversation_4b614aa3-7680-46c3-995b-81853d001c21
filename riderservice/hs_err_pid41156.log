#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=41156, tid=22572
#
# JRE version: OpenJDK Runtime Environment (21.0.6) (build 21.0.6+-13368085-b895.109)
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+-13368085-b895.109, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.9-bin\90cnw93cvbtalezasaz0blq0a\gradle-8.9\lib\agents\gradle-instrumentation-agent-8.9.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.9

Host: Intel(R) Core(TM) i5-10400 CPU @ 2.90GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
Time: Thu May 29 10:30:21 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.4391) elapsed time: 45.633345 seconds (0d 0h 0m 45s)

---------------  T H R E A D  ---------------

Current thread (0x00000223919578a0):  JavaThread "WorkerExecutor Queue Thread 2"        [_thread_in_vm, id=22572, stack(0x000000b5e8500000,0x000000b5e8600000) (1024K)]

Stack: [0x000000b5e8500000,0x000000b5e8600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d0639]
V  [jvm.dll+0x85eb03]
V  [jvm.dll+0x86105e]
V  [jvm.dll+0x861743]
V  [jvm.dll+0x27e6e6]
V  [jvm.dll+0x8599de]
V  [jvm.dll+0x674055]
V  [jvm.dll+0x1e3131]
V  [jvm.dll+0x1e2efe]
V  [jvm.dll+0x6769a8]
V  [jvm.dll+0x6767e2]
V  [jvm.dll+0x674a4e]
V  [jvm.dll+0x269a6f]
V  [jvm.dll+0x67ea35]
V  [jvm.dll+0x214ecc]
V  [jvm.dll+0x215415]
V  [jvm.dll+0x215df6]
V  [jvm.dll+0x20ae49]
V  [jvm.dll+0x5b1ff3]
V  [jvm.dll+0x7d6ac0]
V  [jvm.dll+0x7d6bee]
V  [jvm.dll+0x46eddd]
V  [jvm.dll+0x474728]
C  [java.dll+0x17ec]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
J 804  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class; java.base@21.0.6 (0 bytes) @ 0x00000223f7c8fa09 [0x00000223f7c8f940+0x00000000000000c9]
J 18218 c2 java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class; java.base@21.0.6 (43 bytes) @ 0x00000223f87816c4 [0x00000223f8781100+0x00000000000005c4]
J 12338 c1 java.security.SecureClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class; java.base@21.0.6 (16 bytes) @ 0x00000223f160cf1c [0x00000223f160ce40+0x00000000000000dc]
J 11156 c2 java.net.URLClassLoader$1.run()Ljava/lang/Class; java.base@21.0.6 (81 bytes) @ 0x00000223f82a9c0c [0x00000223f82a8a20+0x00000000000011ec]
J 9093 c2 java.net.URLClassLoader$1.run()Ljava/lang/Object; java.base@21.0.6 (5 bytes) @ 0x00000223f80d5260 [0x00000223f80d5220+0x0000000000000040]
J 8922 c2 java.net.URLClassLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; java.base@21.0.6 (47 bytes) @ 0x00000223f80bb35c [0x00000223f80bb2a0+0x00000000000000bc]
J 13769 c2 java.lang.ClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class; java.base@21.0.6 (121 bytes) @ 0x00000223f8508854 [0x00000223f8508520+0x0000000000000334]
J 3432 c2 java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class; java.base@21.0.6 (7 bytes) @ 0x00000223f7d4016c [0x00000223f7d40120+0x000000000000004c]
v  ~StubRoutines::call_stub 0x00000223f76a100d
j  com.intellij.psi.stubs.StubList.idSubList(Lcom/intellij/psi/stubs/MostlyUShortIntList;II)Ljava/util/List;+0
j  com.intellij.psi.stubs.StubList.getChildrenStubs(I)Ljava/util/List;+96
j  com.intellij.psi.stubs.StubBase.getChildrenStubs()Ljava/util/List;+8
j  com.intellij.psi.stubs.StubBase.getChildrenByType(Lcom/intellij/psi/tree/TokenSet;Lcom/intellij/util/ArrayFactory;)[Lcom/intellij/psi/PsiElement;+19
j  org.jetbrains.kotlin.psi.KtCommonFile.getDeclarations()Ljava/util/List;+19
j  org.jetbrains.kotlin.psi.KtCommonFile.hasTopLevelCallables()Z+20
j  org.jetbrains.kotlin.analysis.providers.impl.KotlinStaticDeclarationProviderFactory.addToFacadeFileMap(Lorg/jetbrains/kotlin/psi/KtFile;)V+1
j  org.jetbrains.kotlin.analysis.providers.impl.KotlinStaticDeclarationProviderFactory.processStub(Lorg/jetbrains/kotlin/psi/stubs/impl/KotlinFileStubImpl;)V+17
j  org.jetbrains.kotlin.analysis.providers.impl.KotlinStaticDeclarationProviderFactory.<init>(Lcom/intellij/openapi/project/Project;Ljava/util/Collection;Ljava/util/List;Ljava/util/List;ZZ)V+168
j  org.jetbrains.kotlin.analysis.providers.impl.KotlinStaticDeclarationProviderFactory.<init>(Lcom/intellij/openapi/project/Project;Ljava/util/Collection;Ljava/util/List;Ljava/util/List;ZZILkotlin/jvm/internal/DefaultConstructorMarker;)V+56
j  com.android.tools.lint.Fe10UastEnvironmentKt.configureAnalysisApiServices(Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreProjectEnvironment;Lcom/android/tools/lint/Fe10UastEnvironment$Configuration;)V+300
j  com.android.tools.lint.Fe10UastEnvironmentKt.configureFe10ProjectEnvironment(Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreProjectEnvironment;Lcom/android/tools/lint/Fe10UastEnvironment$Configuration;)V+54
j  com.android.tools.lint.Fe10UastEnvironmentKt.createKotlinCompilerEnv(Lcom/intellij/openapi/Disposable;Lcom/android/tools/lint/Fe10UastEnvironment$Configuration;)Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment;+81
j  com.android.tools.lint.Fe10UastEnvironmentKt.access$createKotlinCompilerEnv(Lcom/intellij/openapi/Disposable;Lcom/android/tools/lint/Fe10UastEnvironment$Configuration;)Lorg/jetbrains/kotlin/cli/jvm/compiler/KotlinCoreEnvironment;+2
j  com.android.tools.lint.Fe10UastEnvironment$Companion.create(Lcom/android/tools/lint/Fe10UastEnvironment$Configuration;)Lcom/android/tools/lint/Fe10UastEnvironment;+20
j  com.android.tools.lint.UastEnvironment$Companion.create(Lcom/android/tools/lint/UastEnvironment$Configuration;)Lcom/android/tools/lint/UastEnvironment;+45
j  com.android.tools.lint.UastEnvironment.create(Lcom/android/tools/lint/UastEnvironment$Configuration;)Lcom/android/tools/lint/UastEnvironment;+4
j  com.android.tools.lint.annotations.ExtractAnnotationsDriver.run([Ljava/lang/String;)I+1569
j  java.lang.invoke.LambdaForm$DMH+0x00000001002c0000.invokeVirtual(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)I+11 java.base@21.0.6
j  java.lang.invoke.LambdaForm$MH+0x0000000100df0c00.invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+52 java.base@21.0.6
J 12670 c2 jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; java.base@21.0.6 (92 bytes) @ 0x00000223f84097dc [0x00000223f84096c0+0x000000000000011c]
J 15338 c1 java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object; java.base@21.0.6 (108 bytes) @ 0x00000223f1c34334 [0x00000223f1c33e40+0x00000000000004f4]
j  com.android.build.gradle.internal.lint.AndroidLintWorkAction.invokeLintMainRunMethod(Ljava/lang/ClassLoader;Ljava/util/List;)I+104
j  com.android.build.gradle.internal.lint.AndroidLintWorkAction.runLint(Ljava/util/List;Z)I+60
j  com.android.build.gradle.internal.lint.AndroidLintWorkAction.execute()V+148
j  org.gradle.workers.internal.DefaultWorkerServer.execute(Lorg/gradle/workers/internal/SimpleActionExecutionSpec;)Lorg/gradle/workers/internal/DefaultWorkResult;+128
j  org.gradle.workers.internal.NoIsolationWorkerFactory$1$1.create()Lorg/gradle/workers/internal/DefaultWorkResult;+29
j  org.gradle.workers.internal.NoIsolationWorkerFactory$1$1.create()Ljava/lang/Object;+1
j  org.gradle.internal.classloader.ClassLoaderUtils.executeInClassloader(Ljava/lang/ClassLoader;Lorg/gradle/internal/Factory;)Ljava/lang/Object;+15
j  org.gradle.workers.internal.NoIsolationWorkerFactory$1.lambda$execute$0(Ljava/lang/ClassLoader;Lorg/gradle/workers/WorkerExecutor;Lorg/gradle/workers/internal/IsolatedParametersActionExecutionSpec;)Lorg/gradle/workers/internal/DefaultWorkResult;+10
j  org.gradle.workers.internal.NoIsolationWorkerFactory$1$$Lambda+0x0000000101056ff0.execute(Lorg/gradle/workers/internal/IsolatedParametersActionExecutionSpec;)Lorg/gradle/workers/internal/DefaultWorkResult;+13
j  org.gradle.workers.internal.AbstractWorker$1.call(Lorg/gradle/internal/operations/BuildOperationContext;)Lorg/gradle/workers/internal/DefaultWorkResult;+8
j  org.gradle.workers.internal.AbstractWorker$1.call(Lorg/gradle/internal/operations/BuildOperationContext;)Ljava/lang/Object;+2
J 7947 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V (10 bytes) @ 0x00000223f0628924 [0x00000223f06286e0+0x0000000000000244]
J 16086 c2 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object; (12 bytes) @ 0x00000223f86d48b8 [0x00000223f86d42e0+0x00000000000005d8]
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object;+141
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V+18
J 7920 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.call(Lorg/gradle/internal/operations/CallableBuildOperation;)Ljava/lang/Object; (24 bytes) @ 0x00000223f0795db4 [0x00000223f0795920+0x0000000000000494]
j  org.gradle.workers.internal.AbstractWorker.executeWrappedInBuildOperation(Lorg/gradle/workers/internal/IsolatedParametersActionExecutionSpec;Lorg/gradle/internal/operations/BuildOperationRef;Lorg/gradle/workers/internal/AbstractWorker$Work;)Lorg/gradle/workers/internal/DefaultWorkResult;+15
j  org.gradle.workers.internal.NoIsolationWorkerFactory$1.execute(Lorg/gradle/workers/internal/IsolatedParametersActionExecutionSpec;Lorg/gradle/internal/operations/BuildOperationRef;)Lorg/gradle/workers/internal/DefaultWorkResult;+17
j  org.gradle.workers.internal.DefaultWorkerExecutor.lambda$submitWork$0(Lorg/gradle/workers/internal/WorkerFactory;Lorg/gradle/workers/internal/WorkerRequirement;Lorg/gradle/workers/internal/IsolatedParametersActionExecutionSpec;Lorg/gradle/internal/operations/BuildOperationRef;)Lorg/gradle/workers/internal/DefaultWorkResult;+13
j  org.gradle.workers.internal.DefaultWorkerExecutor$$Lambda+0x00000001010562d0.call()Ljava/lang/Object;+16
j  java.util.concurrent.FutureTask.run()V+39 java.base@21.0.6
j  org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.runExecution(Lorg/gradle/internal/work/ConditionalExecution;)V+6
j  org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.access$700(Lorg/gradle/internal/work/DefaultConditionalExecutionQueue$ExecutionRunner;Lorg/gradle/internal/work/ConditionalExecution;)V+2
j  org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner$1.run()V+14
j  org.gradle.internal.Factories$1.create()Ljava/lang/Object;+4
j  org.gradle.internal.work.DefaultWorkerLeaseService.withLocks(Ljava/util/Collection;Lorg/gradle/internal/Factory;)Ljava/lang/Object;+28
j  org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(Lorg/gradle/internal/Factory;)Ljava/lang/Object;+33
j  org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(Ljava/lang/Runnable;)V+5
j  org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.runBatch(Lorg/gradle/internal/work/ConditionalExecution;)V+16
j  org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.run()V+11
J 11494 c2 java.util.concurrent.FutureTask.run()V java.base@21.0.6 (123 bytes) @ 0x00000223f82eb8c8 [0x00000223f82eb440+0x0000000000000488]
J 8677 c2 org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(Ljava/lang/Runnable;)V (29 bytes) @ 0x00000223f7e81b14 [0x00000223f7e81ac0+0x0000000000000054]
j  org.gradle.internal.concurrent.AbstractManagedExecutor$1.run()V+25
j  java.util.concurrent.ThreadPoolExecutor.runWorker(Ljava/util/concurrent/ThreadPoolExecutor$Worker;)V+92 java.base@21.0.6
j  java.util.concurrent.ThreadPoolExecutor$Worker.run()V+5 java.base@21.0.6
j  java.lang.Thread.runWith(Ljava/lang/Object;Ljava/lang/Runnable;)V+5 java.base@21.0.6
j  java.lang.Thread.run()V+19 java.base@21.0.6
v  ~StubRoutines::call_stub 0x00000223f76a100d

Compiled method (n/a) 45791  804     n 0       java.lang.ClassLoader::defineClass1 (native)
 total in heap  [0x00000223f7c8f790,0x00000223f7c8fde8] = 1624
 relocation     [0x00000223f7c8f8f0,0x00000223f7c8f928] = 56
 main code      [0x00000223f7c8f940,0x00000223f7c8fde7] = 1191
 stub code      [0x00000223f7c8fde7,0x00000223f7c8fde8] = 1

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x0000022385025018} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader'
  # parm0:    rdx:rdx   = 'java/lang/ClassLoader'
  # parm1:    r8:r8     = 'java/lang/String'
  # parm2:    r9:r9     = '[B'
  # parm3:    rdi       = int
  # parm4:    rsi       = int
  # parm5:    rcx:rcx   = 'java/security/ProtectionDomain'
  # parm6:    [sp+0xa0]   = 'java/lang/String'  (sp of caller)
  0x00000223f7c8f940: 448b 5208 | 49c1 e203 | 493b c20f | 8407 0000 

  0x00000223f7c8f950: ;   {runtime_call ic_miss_stub}
  0x00000223f7c8f950: 00e9 2aee | a5ff 6690 
[Verified Entry Point]
  0x00000223f7c8f958: 8984 2400 | 80ff ff55 | 488b ec48 | 81ec 9000 | 0000 6690 | 4181 7f20 | 1100 0000 

  0x00000223f7c8f974: ;   {runtime_call StubRoutines (final stubs)}
  0x00000223f7c8f974: 7405 e8a5 | a1a4 ff48 | 837d 1000 | 488d 4510 | 480f 4445 | 1048 8944 | 2440 4889 | 4c24 7048 
  0x00000223f7c8f994: 83f9 0048 | 8d44 2470 | 480f 4444 | 2470 4889 | 4424 3848 | 8974 2430 | 4889 7c24 | 284c 894c 
  0x00000223f7c8f9b4: 2458 4983 | f900 488d | 4424 5848 | 0f44 4424 | 5848 8944 | 2420 4c89 | 4424 5049 | 83f8 004c 
  0x00000223f7c8f9d4: 8d4c 2450 | 4c0f 444c | 2450 4889 | 5424 4848 | 83fa 004c | 8d44 2448 | 4c0f 4444 

  0x00000223f7c8f9f0: ;   {oop(a 'java/lang/Class'{0x0000000080867920} = 'java/lang/ClassLoader')}
  0x00000223f7c8f9f0: 2448 49be | 2079 8680 | 0000 0000 | 4c89 7424 | 784c 8d74 | 2478 498b | d6c5 f877 

  0x00000223f7c8fa0c: ;   {internal_word}
  0x00000223f7c8fa0c: 49ba 09fa | c8f7 2302 | 0000 4d89 | 97a0 0300 | 0049 89a7 | 9803 0000 

  0x00000223f7c8fa24: ;   {external_word}
  0x00000223f7c8fa24: 49ba 8057 | 59a8 fd7f | 0000 4180 | 3a00 0f84 | 4e00 0000 | 5241 5041 

  0x00000223f7c8fa3c: ;   {metadata({method} {0x0000022385025018} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x00000223f7c8fa3c: 5148 ba10 | 5002 8523 | 0200 0049 | 8bcf 4883 | ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 
  0x00000223f7c8fa5c: ;   {runtime_call}
  0x00000223f7c8fa5c: 48b8 5077 | 13a8 fd7f | 0000 ffd0 | 4883 c408 | e90c 0000 

  0x00000223f7c8fa70: ;   {runtime_call}
  0x00000223f7c8fa70: 0048 b850 | 7713 a8fd | 7f00 00ff | d048 83c4 | 2041 5941 | 585a 498d | 8fb8 0300 | 0041 c787 
  0x00000223f7c8fa90: 4404 0000 | 0400 0000 

  0x00000223f7c8fa98: ;   {runtime_call}
  0x00000223f7c8fa98: 48b8 ac16 | a039 fe7f | 0000 ffd0 | c5f8 7741 | c787 4404 | 0000 0500 | 0000 f083 | 4424 c000 
  0x00000223f7c8fab8: 493b af48 | 0400 000f | 870e 0000 | 0041 83bf | 4004 0000 | 000f 842b | 0000 00c5 | f877 4889 
  0x00000223f7c8fad8: 45f8 498b | cf4c 8be4 | 4883 ec20 | 4883 e4f0 

  0x00000223f7c8fae8: ;   {runtime_call}
  0x00000223f7c8fae8: 48b8 20dc | dea7 fd7f | 0000 ffd0 | 498b e44d | 33e4 488b | 45f8 41c7 | 8744 0400 | 0008 0000 
  0x00000223f7c8fb08: 0041 83bf | c004 0000 | 020f 84a3 

  0x00000223f7c8fb14: ;   {external_word}
  0x00000223f7c8fb14: 0200 0049 | ba80 5759 | a8fd 7f00 | 0041 803a | 000f 844c | 0000 0048 

  0x00000223f7c8fb2c: ;   {metadata({method} {0x0000022385025018} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x00000223f7c8fb2c: 8945 f848 | ba10 5002 | 8523 0200 | 0049 8bcf | 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 
  0x00000223f7c8fb4c: ;   {runtime_call}
  0x00000223f7c8fb4c: ec08 48b8 | 5077 13a8 | fd7f 0000 | ffd0 4883 | c408 e90c 

  0x00000223f7c8fb60: ;   {runtime_call}
  0x00000223f7c8fb60: 0000 0048 | b850 7713 | a8fd 7f00 | 00ff d048 | 83c4 2048 | 8b45 f849 | c787 9803 | 0000 0000 
  0x00000223f7c8fb80: 0000 49c7 | 87a0 0300 | 0000 0000 | 00c5 f877 | 4885 c00f | 84fe 0100 | 00a8 030f | 8508 0000 
  0x00000223f7c8fba0: 0048 8b00 | e9ee 0100 | 00a8 010f | 8509 0000 | 0048 8b40 | fee9 dd01 | 0000 488b | 40ff 4180 
  0x00000223f7c8fbc0: 7f40 000f | 84ce 0100 | 0048 83f8 | 000f 84c4 | 0100 0049 | 8b4f 2848 | 83f9 000f | 8414 0000 
  0x00000223f7c8fbe0: 0048 83e9 | 0849 894f | 2849 034f | 3848 8901 | e9a2 0100 | 0048 81ec | d000 0000 | 4889 0424 
  0x00000223f7c8fc00: 4889 4c24 | 0848 8954 | 2410 4889 | 7424 1848 | 897c 2420 | 4c89 4424 | 284c 894c | 2430 4c89 
  0x00000223f7c8fc20: 5424 384c | 895c 2440 | c5fb 1144 | 2450 c5fb | 114c 2458 | c5fb 1154 | 2460 c5fb | 115c 2468 
  0x00000223f7c8fc40: c5fb 1164 | 2470 c5fb | 116c 2478 | c5fb 11b4 | 2480 0000 | 00c5 fb11 | bc24 8800 | 0000 c57b 
  0x00000223f7c8fc60: ;   {no_reloc}
  0x00000223f7c8fc60: 1184 2490 | 0000 00c5 | 7b11 8c24 | 9800 0000 | c57b 1194 | 24a0 0000 | 00c5 7b11 | 9c24 a800 
  0x00000223f7c8fc80: 0000 c57b | 11a4 24b0 | 0000 00c5 | 7b11 ac24 | b800 0000 | c57b 11b4 | 24c0 0000 | 00c5 7b11 
  0x00000223f7c8fca0: bc24 c800 | 0000 498b | d748 8bc8 | 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 

  0x00000223f7c8fcbc: ;   {runtime_call}
  0x00000223f7c8fcbc: ec08 48b8 | 70ee d1a7 | fd7f 0000 | ffd0 4883 | c408 e90c 

  0x00000223f7c8fcd0: ;   {runtime_call}
  0x00000223f7c8fcd0: 0000 0048 | b870 eed1 | a7fd 7f00 | 00ff d048 | 83c4 20c5 | 7b10 bc24 | c800 0000 | c57b 10b4 
  0x00000223f7c8fcf0: 24c0 0000 | 00c5 7b10 | ac24 b800 | 0000 c57b | 10a4 24b0 | 0000 00c5 | 7b10 9c24 | a800 0000 
  0x00000223f7c8fd10: c57b 1094 | 24a0 0000 | 00c5 7b10 | 8c24 9800 | 0000 c57b | 1084 2490 | 0000 00c5 | fb10 bc24 
  0x00000223f7c8fd30: 8800 0000 | c5fb 10b4 | 2480 0000 | 00c5 fb10 | 6c24 78c5 | fb10 6424 | 70c5 fb10 | 5c24 68c5 
  0x00000223f7c8fd50: fb10 5424 | 60c5 fb10 | 4c24 58c5 | fb10 4424 | 504c 8b5c | 2440 4c8b | 5424 384c | 8b4c 2430 
  0x00000223f7c8fd70: 4c8b 4424 | 2848 8b7c | 2420 488b | 7424 1848 | 8b54 2410 | 488b 4c24 | 0848 8b04 | 2448 81c4 
  0x00000223f7c8fd90: d000 0000 | c5f8 7749 | 8b8f 2804 | 0000 c781 | 0001 0000 | 0000 0000 | c949 837f | 0800 0f85 
  0x00000223f7c8fdb0: 0100 0000 

  0x00000223f7c8fdb4: ;   {runtime_call StubRoutines (initial stubs)}
  0x00000223f7c8fdb4: c3e9 4611 | a1ff c5f8 | 7748 8945 | f84c 8be4 | 4883 ec20 | 4883 e4f0 

  0x00000223f7c8fdcc: ;   {runtime_call}
  0x00000223f7c8fdcc: 48b8 f0ae | 13a8 fd7f | 0000 ffd0 | 498b e44d | 33e4 488b | 45f8 e930 | fdff fff4 
[/MachCode]


Compiled method (c2) 45807 18218       4       java.lang.ClassLoader::defineClass (43 bytes)
 total in heap  [0x00000223f8780e10,0x00000223f8783c40] = 11824
 relocation     [0x00000223f8780f70,0x00000223f87810e8] = 376
 main code      [0x00000223f8781100,0x00000223f8782ae8] = 6632
 stub code      [0x00000223f8782ae8,0x00000223f8782b78] = 144
 oops           [0x00000223f8782b78,0x00000223f8782b90] = 24
 metadata       [0x00000223f8782b90,0x00000223f8782d80] = 496
 scopes data    [0x00000223f8782d80,0x00000223f87836c8] = 2376
 scopes pcs     [0x00000223f87836c8,0x00000223f8783a08] = 832
 dependencies   [0x00000223f8783a08,0x00000223f8783a90] = 136
 handler table  [0x00000223f8783a90,0x00000223f8783bf8] = 360
 nul chk table  [0x00000223f8783bf8,0x00000223f8783c40] = 72

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x0000022385024e38} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader'
  # this:     rdx:rdx   = 'java/lang/ClassLoader'
  # parm0:    r8:r8     = 'java/lang/String'
  # parm1:    r9:r9     = '[B'
  # parm2:    rdi       = int
  # parm3:    rsi       = int
  # parm4:    rcx:rcx   = 'java/security/ProtectionDomain'
  #           [sp+0xd0]  (sp of caller)
  0x00000223f8781100: 448b 5208 | 49c1 e203 

  0x00000223f8781108: ;   {runtime_call ic_miss_stub}
  0x00000223f8781108: 493b c20f | 856f d6f6 | fe66 6690 | 0f1f 8400 | 0000 0000 | 6666 6690 
[Verified Entry Point]
  0x00000223f8781120: 8984 2400 | 80ff ff55 | 4881 ecc0 | 0000 0090 | 4181 7f20 | 1100 0000 | 0f85 9c19 | 0000 4889 
  0x00000223f8781140: 4c24 4089 | 7424 3c89 | 7c24 384c | 894c 2430 | 4889 5424 | 284c 8944 | 2448 660f | 1f44 0000 
  0x00000223f8781160: ; implicit exception: dispatches to 0x00000223f8782870
  0x00000223f8781160: 458b 4014 

  0x00000223f8781164: ; implicit exception: dispatches to 0x00000223f8782858
  0x00000223f8781164: 458b 500c | 4585 d20f | 8627 1000 | 004c 8b5c | 2448 410f | be4b 1085 | c966 6690 | 0f85 5e10 
  0x00000223f8781184: 0000 c442 | 72f7 ca45 | 3bca 418b | d941 0f4f | da4d 8bd8 | 4d8d 7010 | 0f1f 4000 | 85db 0f8e 
  0x00000223f87811a4: cf00 0000 | 443b d30f | 8203 1000 | 00b8 2f00 | 0000 498b | fe8b d348 | 8bdf 83fa | 100f 8c79 
  0x00000223f87811c4: 0000 0083 | fa20 0f8c | 3600 0000 | c5f9 6ec0 | c4e2 7d78 | c0c5 edef | d28b ca83 | e1e0 83e2 
  0x00000223f87811e4: 1fc5 fe6f | 0bc5 f574 | c8c4 e27d | 17d1 0f83 | 6900 0000 | 4883 c320 | 83e9 2075 | e4e9 0d00 
  0x00000223f8781204: 0000 c5f9 

  0x00000223f8781208: ;   {no_reloc}
  0x00000223f8781208: 6ec0 c5e9 | efd2 c4e2 | 7900 c283 | fa10 0f8c | 2400 0000 | 8bca 83e1 | f083 e20f | c5fa 6f0b 
  0x00000223f8781228: c5f1 74c8 | c4e2 7917 | d10f 832a | 0000 0048 | 83c3 1083 | e910 75e4 | 85d2 0f84 | 1200 0000 
  0x00000223f8781248: 0fb6 0b3b | c174 1c48 | 83c3 0183 | ea01 7402 | ebee bbff | ffff ffeb | 0dc5 fdd7 | c90f bcc1 
  0x00000223f8781268: 4803 d848 | 2bdf 83fb | ff0f 85c9 | 0f00 0041 | 0fb6 2e0f | 1f44 0000 | 83fd 5b0f | 84d7 0f00 
  0x00000223f8781288: 0041 8d59 | fb85 db0f | 8c5f 0000 | 0041 0fbe | 0e83 f96a | 0f1f 4000 | 0f85 4e00 | 0000 4183 
  0x00000223f87812a8: fa04 0f86 | 2811 0000 | 4180 7811 | 610f 8539 | 0000 000f | 1f44 0000 | 4180 7812 | 7675 2d41 
  0x00000223f87812c8: 8078 1361 | 7526 4180 | 7814 2e75 

  0x00000223f87812d4: ;   {oop(a 'jdk/internal/loader/ClassLoaders$PlatformClassLoader'{0x0000000080690570})}
  0x00000223f87812d4: 1f48 b970 | 0569 8000 | 0000 0048 | 8b5c 2428 | 483b d90f | 854f 1400 | 0048 894c | 2450 eb0a 
  0x00000223f87812f4: 48ff 7424 | 2848 8f44 | 2450 488b | 4c24 408b | 5910 418b | faff cf41 | 8bc9 ffc9 | 3bcf 0f4f 
  0x00000223f8781314: cf85 c90f | 8c08 0400 | 0033 ff89 | 7c24 2066 | 0f1f 8400 | 0000 0000 | 6666 6690 | 413b ca0f 
  0x00000223f8781334: 8338 0d00 | 000f 1f80 | 0000 0000 | 4180 7c08 | 102e 7419 | 498b 9750 | 0400 00ff | c948 8b7c 
  0x00000223f8781354: ; ImmutableOopMap {r11=Oop r14=Derived_oop_r11 r8=NarrowOop rbx=NarrowOop rdi=Oop [40]=Oop [48]=Oop [64]=Oop [72]=Oop [80]=Oop }
                      ;*goto {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.StringLatin1::lastIndexOf@35
                      ; - java.lang.String::lastIndexOf@13
                      ; - java.lang.String::lastIndexOf@8
                      ; - java.lang.ClassLoader::checkCerts@3
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
                      ;   {poll}
  0x00000223f8781354: 2448 8502 | 85c9 7dd4 | b9ff ffff | ff83 f9ff | 0f84 c603 | 0000 85c9 | 0f8c a60e | 0000 413b 
  0x00000223f8781374: c90f 8f61 | 0f00 000f | 1f44 0000 | 413b c90f | 847f 0300 | 0085 c90f | 8481 0300 | 0049 8bb7 
  0x00000223f8781394: b801 0000 | 4c8b ce49 | 83c1 1890 | 4d3b 8fc8 | 0100 000f | 8358 0b00 | 004d 898f | b801 0000 
  0x00000223f87813b4: 410f 0d89 | c000 0000 | 48c7 0601 

  0x00000223f87813c0: ;   {metadata('java/lang/String')}
  0x00000223f87813c0: 0000 00c7 | 4608 d683 | 0020 4489 | 660c 4c89 | 6610 413b | ca0f 84f6 | 0000 000f | 1f44 0000 
  0x00000223f87813e0: 81f9 0000 | 1000 0f87 | 3c0f 0000 | 4c63 c944 | 3bd1 418b | ea0f 4fe9 | 443b d50f | 1f44 0000 
  0x00000223f8781400: 0f82 960f | 0000 3bcd | 0f82 8e0f | 0000 4983 | c117 4d8b | c149 83e0 | f80f 1f80 | 0000 0000 
  0x00000223f8781420: 81f9 0000 | 1000 0f87 | 7c0c 0000 | 4d8b afb8 | 0100 004d | 8bd5 4d03 | d00f 1f80 | 0000 0000 
  0x00000223f8781440: 4d3b 97c8 | 0100 000f | 835b 0c00 | 004d 8997 | b801 0000 | 410f 0d8a | c000 0000 | 49c7 4500 
  0x00000223f8781460: 0100 0000 | 410f 0d8a | 0001 0000 

  0x00000223f878146c: ;   {metadata({type array byte})}
  0x00000223f878146c: 41c7 4508 | 4581 0020 | 4189 4d0c | 410f 0d8a | 4001 0000 | 410f 0d8a | 8001 0000 | 4d8b d549 
  0x00000223f878148c: 83c2 1049 | c1e9 0385 | ed0f 8495 | 0e00 003b | e966 6690 | 0f8c 3107 | 0000 4983 | c0f0 49c1 
  0x00000223f87814ac: e803 498b | ce49 8bd2 | c5f8 7749 | ba60 816d | f723 0200 | 0041 ffd2 

  0x00000223f87814c4: ;   {other}
  0x00000223f87814c4: 0f1f 8400 | 0000 0000 | e9a2 0000 | 004d 63ca | 0f1f 8400 | 0000 0000 | 6666 6690 | 4181 fa00 
  0x00000223f87814e4: 0010 000f | 87d1 0a00 | 004d 8baf | b801 0000 | 498d 4917 | 4883 e1f8 | 498b fd48 | 03f9 493b 
  0x00000223f8781504: bfc8 0100 | 000f 83af | 0a00 0049 | 89bf b801 | 0000 0f0d | 8fc0 0000 | 0049 c745 | 0001 0000 
  0x00000223f8781524: 000f 0d8f | 0001 0000 

  0x00000223f878152c: ;   {metadata({type array byte})}
  0x00000223f878152c: 41c7 4508 | 4581 0020 | 4589 550c | 0f0d 8f40 | 0100 000f | 0d8f 8001 | 0000 498b | d548 83c2 
  0x00000223f878154c: 1049 83c1 | 0749 c1e9 | 0349 8bce | 4d8b c1c5 | f877 49ba | 6081 6df7 | 2302 0000 

  0x00000223f8781568: ;   {other}
  0x00000223f8781568: 41ff d20f | 1f84 0000 | 0000 0041 | 807f 4000 | 0f85 c101 | 0000 4c8b | d64d 8bc5 | 4489 4614 
  0x00000223f8781588: 4d8b dd4d | 33da 49c1 | eb14 4d85 | db74 1a49 | c1ea 0948 | b900 006c | ff23 0200 | 0049 03ca 
  0x00000223f87815a8: 8039 020f | 85c1 0100 | 0044 8866 | 1085 db74 | 1d44 8b5b | 1466 6690 | 4585 db0f | 85fc 0100 
  0x00000223f87815c8: 0044 8b53 | 1045 85d2 | 0f85 a102 

  0x00000223f87815d4: ;   {oop(a 'java/security/cert/Certificate'[0] {0x00000000808679a0})}
  0x00000223f87815d4: 0000 49ba | a079 8680 | 0000 0000 | 4c89 5424 | 204c 8b54 | 2450 458b | 5a2c 4585 | db0f 846e 
  0x00000223f87815f4: 0b00 0049 | 8bd3 bf01 | 0000 004c | 8bc6 4c8b | 4c24 2090 

  0x00000223f8781608: ;   {optimized virtual_call}
  0x00000223f8781608: c5f8 77e8 

  0x00000223f878160c: ; ImmutableOopMap {[32]=Oop [40]=Oop [48]=Oop [64]=Oop [72]=Oop [80]=Oop }
                      ;*invokevirtual putVal {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.util.concurrent.ConcurrentHashMap::putIfAbsent@4
                      ; - java.lang.ClassLoader::checkCerts@62
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f878160c: 307c d6ff 

  0x00000223f8781610: ;   {other}
  0x00000223f8781610: 0f1f 8400 | 0008 0001 | 4885 c074 | 3544 8b50 

  0x00000223f8781620: ;   {metadata('java/security/cert/Certificate'[])}
  0x00000223f8781620: 0841 81fa | 3956 0020 | 0f85 ae11 | 0000 4c8b | c84c 8b54 | 2420 458b | 520c 458b | 410c 6690 
  0x00000223f8781640: 4585 d20f | 872c 0600 | 0045 85c0 | 0f87 260c | 0000 4c8b | 5424 4045 | 8b5a 100f | 1f44 0000 
  0x00000223f8781660: 4585 db0f | 8467 0500 | 0041 8b6b | 0c85 ed0f | 8442 0900 | 0044 8b5d | 3845 85db | 0f1f 4000 
  0x00000223f8781680: 0f84 fa0a | 0000 498b | d34c 8bc5 

  0x00000223f878168c: ;   {optimized virtual_call}
  0x00000223f878168c: c5f8 77e8 

  0x00000223f8781690: ; ImmutableOopMap {[40]=Oop [48]=Oop [64]=Oop [72]=Oop }
                      ;*invokevirtual toExternalForm {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.net.URL::toExternalForm@5
                      ; - java.net.URL::toString@1
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22
                      ; - java.lang.ClassLoader::defineClass@12
  0x00000223f8781690: 0c86 53ff 

  0x00000223f8781694: ;   {other}
  0x00000223f8781694: 0f1f 8400 | 8408 0002 | 488b 5424 | 284c 8b44 | 2448 4c8b | 4c24 308b | 7c24 388b | 7424 3c48 
  0x00000223f87816b4: 8b4c 2440 | 4889 0424 

  0x00000223f87816bc: ;   {static_call}
  0x00000223f87816bc: c5f8 77e8 

  0x00000223f87816c0: ; ImmutableOopMap {[40]=Oop [64]=Oop }
                      ;*invokestatic defineClass1 {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.lang.ClassLoader::defineClass@27
  0x00000223f87816c0: 94e2 50ff 

  0x00000223f87816c4: ;   {other}
  0x00000223f87816c4: 0f1f 8400 | b408 0003 | 488b e848 | 8b54 2428 | 4c8b c04c | 8b4c 2440 

  0x00000223f87816dc: ;   {optimized virtual_call}
  0x00000223f87816dc: c5f8 77e8 

  0x00000223f87816e0: ; ImmutableOopMap {rbp=Oop }
                      ;*invokevirtual postDefineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClass@37
  0x00000223f87816e0: 9cb6 a6ff 

  0x00000223f87816e4: ;   {other}
  0x00000223f87816e4: 0f1f 8400 | d408 0004 | 488b c5c5 | f877 4881 | c4c0 0000 

  0x00000223f87816f8: ;   {poll_return}
  0x00000223f87816f8: 005d 493b | a748 0400 | 000f 87bd | 1300 00c3 | 488b 7424 | 48e9 a3fe 

  0x00000223f8781710: ;   {oop(""{0x000000008069bba0})}
  0x00000223f8781710: ffff 48be | a0bb 6980 | 0000 0000 | 0f1f 4000 | e990 feff | ff33 c989 | 4c24 20e9 | 2cfc ffff 
  0x00000223f8781730: ;   {oop(""{0x000000008069bba0})}
  0x00000223f8781730: 48be a0bb | 6980 0000 | 0000 e976 | feff ff44 | 8b56 1445 | 85d2 0f84 | 32fe ffff | 4d8b 5f28 
  0x00000223f8781750: 498b ca4d | 85db 0f84 | ac0b 0000 | 4d8b 5738 | 4b89 4c1a | f849 83c3 | f84d 895f | 28e9 0cfe 
  0x00000223f8781770: ffff 4d8b | 5748 4d8b | 5f58 f083 | 4424 c000 | 8039 000f | 8428 feff | ff44 8821 | 4d85 d275 
  0x00000223f8781790: 2049 8bd7 | c5f8 7749 | ba50 eed1 | a7fd 7f00 | 0041 ffd2 

  0x00000223f87817a4: ;   {other}
  0x00000223f87817a4: 0f1f 8400 | 0000 0000 | e900 feff | ff4b 894c | 13f8 4983 | c2f8 4d89 | 5748 6690 | e9ec fdff 
  0x00000223f87817c4: ff45 8b43 | 0c45 8b53 | 0849 8beb | 4963 f848 | c1e7 0266 | 0f1f 8400 | 0000 0000 | 4181 f800 
  0x00000223f87817e4: 0004 000f | 87a4 0c00 | 0049 8b9f | b801 0000 | 4c8d 4f17 | 4983 e1f8 | 488b cb49 | 03c9 493b 
  0x00000223f8781804: 8fc8 0100 | 000f 8382 | 0c00 0049 | 898f b801 | 0000 0f0d | 89c0 0000 | 0048 c703 | 0100 0000 
  0x00000223f8781824: 4489 5308 | 4489 430c | 0f0d 8900 | 0100 000f | 0d89 4001 | 0000 0f0d | 8980 0100 | 0049 8d4b 
  0x00000223f8781844: 1048 8bd3 | 4883 c210 | 4883 c707 | 48c1 ef03 | 4c8b c7c5 | f877 49ba | 6081 6df7 | 2302 0000 
  0x00000223f8781864: ;   {other}
  0x00000223f8781864: 41ff d20f | 1f84 0000 | 0000 0048 | 8bfb e94f | 0300 0049 | 8b87 b801 | 0000 4c8b | d849 83c3 
  0x00000223f8781884: 184c 8bcb | 4d3b 9fc8 | 0100 000f | 8350 0c00 | 004d 899f | b801 0000 | 410f 0d8b | c000 0000 
  0x00000223f87818a4: 48c7 0001 

  0x00000223f87818a8: ;   {metadata('java/util/ArrayList')}
  0x00000223f87818a8: 0000 00c7 | 4008 45ae | 0020 4489 | 600c 4489 | 6010 418b 

  0x00000223f87818bc: ;   {oop(a 'java/lang/Object'[0] {0x0000000080697ac8})}
  0x00000223f87818bc: 4a0c c740 | 14c8 7a69 | 804c 8bc0 | 85c9 760f | 48b9 0000 | 6cff 2302 | 0000 4533 | dbeb 5749 
  0x00000223f87818dc: ba00 006c | ff23 0200 | 00e9 3301 | 0000 660f | 1f44 0000 | 4889 6c24 | 4848 ff74 | 2420 488f 
  0x00000223f87818fc: 4424 2848 | 8944 24f8 | 8b44 2450 | 8944 2420 | 488b 4424 | f848 ff74 | 2458 488f | 4424 5048 
  0x00000223f878191c: 8b4c 2468 | 488b 7424 | 704c 8b4c | 2478 4c8b | 8424 8000 | 0000 418b | 7a0c 443b | df0f 8389 
  0x00000223f878193c: 0d00 0047 | 8b54 9a10 

  0x00000223f8781944: ; implicit exception: dispatches to 0x00000223f87828f0
  0x00000223f8781944: 418b 7a10 

  0x00000223f8781948: ; implicit exception: dispatches to 0x00000223f8782908
  0x00000223f8781948: 448b 5710 | 4489 5c24 | 644c 8984 | 2480 0000 | 004c 894c | 2478 4889 | 7424 7048 | 894c 2468 
  0x00000223f8781968: 895c 2460 | 48ff 7424 | 5048 8f44 | 2458 4889 | 4424 f88b | 4424 2089 | 4424 5048 | 8b44 24f8 
  0x00000223f8781988: 48ff 7424 | 2848 8f44 | 2420 488b | 6c24 484d | 8bc2 488b | 9424 8000 | 0000 6690 

  0x00000223f87819a4: ;   {optimized virtual_call}
  0x00000223f87819a4: c5f8 77e8 

  0x00000223f87819a8: ; ImmutableOopMap {rbp=Oop [32]=Oop [48]=Oop [64]=Oop [88]=Oop [96]=NarrowOop [112]=Oop [120]=Oop [128]=Oop }
                      ;*invokevirtual addAll {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.CodeSource::getCertificates@57
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87819a8: d4d0 f6fe 

  0x00000223f87819ac: ;   {other}
  0x00000223f87819ac: 0f1f 8400 | 9c0b 0005 | 4d8b 8750 | 0400 008b | 5c24 6044 | 8b53 1044 | 8b5c 2464 

  0x00000223f87819c8: ; ImmutableOopMap {r10=NarrowOop rbx=NarrowOop rbp=Oop [32]=Oop [48]=Oop [64]=Oop [88]=Oop [112]=Oop [120]=Oop [128]=Oop }
                      ;*goto {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.security.CodeSource::getCertificates@64
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
                      ;   {poll}
  0x00000223f87819c8: 41ff c341 | 8500 458b | 4a0c 453b | d90f 8c15 | ffff ff48 | 896c 2448 | 48ff 7424 | 2048 8f44 
  0x00000223f87819e8: 2428 4889 | 4424 f88b | 4424 5089 | 4424 2048 | 8b44 24f8 | 48ff 7424 | 5848 8f44 | 2450 4c8b 
  0x00000223f8781a08: 5424 6848 | 8b74 2470 | 4c8b 4c24 | 784c 8b84 | 2480 0000 | 0049 8b87 | b801 0000 | 4c8b d849 
  0x00000223f8781a28: 83c3 104d | 3b9f c801 | 0000 0f83 | 4d0b 0000 | 4d89 9fb8 | 0100 0041 | 0f0d 8bc0 | 0000 0048 
  0x00000223f8781a48: c700 0100 | 0000 410f | 0d8b 0001 

  0x00000223f8781a54: ;   {metadata('java/security/cert/Certificate'[])}
  0x00000223f8781a54: 0000 c740 | 0839 5600 | 2041 0f0d | 8b40 0100 | 0044 8960 | 0c41 0f0d | 8b80 0100 | 0048 8b6c 
  0x00000223f8781a74: 2448 48ff | 7424 2848 | 8f44 2420 | 895c 2458 | 4c89 5424 | 6048 8974 | 2468 4c89 | 4c24 704c 
  0x00000223f8781a94: 8944 2478 | 4c8b c048 | 8b54 2478 

  0x00000223f8781aa0: ;   {optimized virtual_call}
  0x00000223f8781aa0: c5f8 77e8 

  0x00000223f8781aa4: ; ImmutableOopMap {rbp=Oop [32]=Oop [48]=Oop [64]=Oop [80]=Oop [88]=NarrowOop [104]=Oop [112]=Oop }
                      ;*invokevirtual toArray {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.security.CodeSource::getCertificates@73
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8781aa4: d8cf f6fe 

  0x00000223f8781aa8: ;   {other}
  0x00000223f8781aa8: 0f1f 8400 | 980c 0007 | 488b d844 | 8b58 0866 | 0f1f 8400 | 0000 0000 

  0x00000223f8781ac0: ;   {metadata('java/security/cert/Certificate'[])}
  0x00000223f8781ac0: 4181 fb39 | 5600 200f | 852f 0d00 | 0041 807f | 4000 0f85 | 2009 0000 | 8b7c 2458 | 4c8b 5424 
  0x00000223f8781ae0: 704d 8bda | 4c8b d344 | 8957 144c | 8bd3 4d33 | d349 c1ea | 144d 85d2 | 7416 49c1 | eb09 4c8b 
  0x00000223f8781b00: 4424 604d | 03c3 4180 | 3802 0f85 | 1f09 0000 | 448b 430c | 448b 5308 | 4d63 d849 | c1e3 0290 
  0x00000223f8781b20: 4181 f800 | 0004 000f | 87a8 0a00 | 0049 8bbf | b801 0000 | 4d8d 4b17 | 4983 e1f8 | 488b cf49 
  0x00000223f8781b40: 03c9 493b | 8fc8 0100 | 000f 8386 | 0a00 0049 | 898f b801 | 0000 0f0d | 89c0 0000 | 0048 c707 
  0x00000223f8781b60: 0100 0000 | 4489 5708 | 4489 470c | 0f0d 8900 | 0100 000f | 0d89 4001 | 0000 0f0d | 8980 0100 
  0x00000223f8781b80: 0048 8bcb | 4883 c110 | 488b d748 | 83c2 1049 | 83c3 0749 | c1eb 034d | 8bc3 c5f8 | 7749 ba60 
  0x00000223f8781ba0: 816d f723 | 0200 0041 

  0x00000223f8781ba8: ;   {other}
  0x00000223f8781ba8: ffd2 0f1f | 8400 0000 | 0000 4889 | 6c24 4848 | ff74 2420 | 488f 4424 | 2848 8b74 | 2468 4889 
  0x00000223f8781bc8: 7c24 20e9 | 15fa ffff | 33c0 e9c5 | faff ff4c | 63c5 4d8d | 5810 498b | cb48 83e1 | f849 8bfd 
  0x00000223f8781be8: 4803 f949 | c1eb 034d | 2bcb 498b | c948 33c0 | 4883 f908 | 7f10 48ff | c978 4f48 | 8904 cf48 
  0x00000223f8781c08: ffc9 7df7 | eb44 c5fd | efc0 e90d | 0000 00c5 | fe7f 07c5 | fe7f 4720 | 4883 c740 | 4883 e908 
  0x00000223f8781c28: 7ded 4883 | c104 7c0c | c5fe 7f07 | 4883 c720 | 4883 e904 | 4883 c104 | 7e10 48ff | c9c5 f9d6 
  0x00000223f8781c48: 0748 83c7 | 0848 ffc9 | 7df3 498b | ce49 8bd2 | c5f8 7749 | bac0 7a6d | f723 0200 | 0041 ffd2 
  0x00000223f8781c68: ;   {other}
  0x00000223f8781c68: 0f1f 8400 | 0000 0000 | e957 f8ff | ff45 3bd0 | 0f85 1802 | 0000 418b | caff c941 | 3bca 0f83 
  0x00000223f8781c88: f40a 0000 | 4585 c00f | 86eb 0a00 | 0041 8bc8 | ffc9 4533 | dbeb 4944 | 8b5c 246c | 41ff c344 
  0x00000223f8781ca8: 3b9c 2498 | 0000 000f | 8d33 0200 | 0048 ffb4 | 2488 0000 | 0048 8f44 | 2428 48ff | 7424 6048 
  0x00000223f8781cc8: 8f44 2420 | 4c8b 4c24 | 5044 8b94 | 2498 0000 | 0044 8b84 | 249c 0000 | 008b 8c24 | a000 0000 
  0x00000223f8781ce8: 488b 5c24 | 2042 8b5c | 9b10 413b | c80f 830f | 0900 0085 | db66 6690 | 0f84 0409 | 0000 488b 
  0x00000223f8781d08: fb33 ed48 | ff74 2448 | 488f 8424 | 8000 0000 | 48ff 7424 | 2848 8f84 | 2488 0000 | 0048 ff74 
  0x00000223f8781d28: 2420 488f | 8424 9000 | 0000 4489 | 9424 9800 | 0000 4489 | 8424 9c00 | 0000 898c | 24a0 0000 
  0x00000223f8781d48: 0044 895c | 246c 895c | 2470 4889 | bc24 a800 | 0000 eb09 | 0f1f 4000 | 4c8b 4c24 

  0x00000223f8781d64: ;   {no_reloc}
  0x00000223f8781d64: 5045 8b5c | a910 4c89 | 4c24 504d | 8bc3 488b | 9424 a800 | 0000 48ff | b424 8000 | 0000 488f 
  0x00000223f8781d84: 4424 284c | 8b54 2428 | 4c89 5424 | 484c 8b94 | 2490 0000 | 004c 8954 | 2460 6690 

  0x00000223f8781da0: ;   {optimized virtual_call}
  0x00000223f8781da0: c5f8 77e8 

  0x00000223f8781da4: ; ImmutableOopMap {[48]=Oop [64]=Oop [72]=Oop [80]=Oop [96]=Oop [112]=NarrowOop [128]=Oop [136]=Oop [144]=Oop [168]=Oop }
                      ;*invokevirtual equals {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::compareCerts@81
                      ; - java.lang.ClassLoader::checkCerts@80
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8781da4: d8cc f6fe 

  0x00000223f8781da8: ;   {other}
  0x00000223f8781da8: 0f1f 8400 | 980f 0008 | 85c0 0f85 | e7fe ffff | ffc5 660f | 1f44 0000 | 3bac 249c | 0000 007c 
  0x00000223f8781dc8: 9748 ffb4 | 2488 0000 | 0048 8f44 | 2428 e9bb | 0000 0044 | 8b44 2464 | 41ff c044 | 3b84 249c 
  0x00000223f8781de8: 0000 000f | 8dff 0000 | 004c 8b54 | 2450 478b | 5c82 100f | 1f44 0000 | 4585 db0f | 8433 0800 
  0x00000223f8781e08: 004d 8bcb | 33ed 4489 | 4424 6444 | 895c 2468 | 4c89 4c24 | 7866 6690 | 4c8b 9424 | 9000 0000 
  0x00000223f8781e28: 458b 54aa | 104d 8bc2 | 488b 5424 | 7848 ffb4 | 2480 0000 | 0048 8f44 | 2428 4c8b | 5424 284c 
  0x00000223f8781e48: 8954 2448 | 4c8b 9424 | 9000 0000 | 4c89 5424 | 5866 6690 

  0x00000223f8781e5c: ;   {optimized virtual_call}
  0x00000223f8781e5c: c5f8 77e8 

  0x00000223f8781e60: ; ImmutableOopMap {[48]=Oop [64]=Oop [72]=Oop [80]=Oop [88]=Oop [104]=NarrowOop [120]=Oop [128]=Oop [136]=Oop [144]=Oop }
                      ;*invokevirtual equals {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::compareCerts@166
                      ; - java.lang.ClassLoader::checkCerts@80
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8781e60: 1ccc f6fe 

  0x00000223f8781e64: ;   {other}
  0x00000223f8781e64: 0f1f 8400 | 5410 0009 | 85c0 0f85 | 67ff ffff | ffc5 6666 | 0f1f 8400 | 0000 0000 | 3bac 2498 
  0x00000223f8781e84: 0000 007c | 9748 ffb4 | 2488 0000 | 0048 8f44 | 2428 ba45 | ffff ff48 | 8b6c 2428 | 48ff 7424 
  0x00000223f8781ea4: 3048 8f44 | 2428 4889 | 4424 f88b | 4424 3889 | 4424 3048 | 8b44 24f8 | 4889 4424 | f88b 4424 
  0x00000223f8781ec4: 3c89 4424 | 3448 8b44 | 24f8 48ff | 7424 4048 | 8f44 2438 

  0x00000223f8781ed8: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8781ed8: c5f8 77e8 

  0x00000223f8781edc: ; ImmutableOopMap {rbp=Oop [40]=Oop [56]=Oop [72]=Oop }
                      ;*ifne {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::checkCerts@83
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8781edc: 2027 f7fe 

  0x00000223f8781ee0: ;   {other}
  0x00000223f8781ee0: 0f1f 8400 | d010 000a | 4533 c0e9 | 01ff ffff | 48ff b424 | 8800 0000 | 488f 4424 | 2866 6690 
  0x00000223f8781f00: e94d f7ff | ff89 4c24 | 7889 5c24 | 5c48 ff74 | 2450 488f | 4424 704c | 8974 2468 | 4c89 5c24 
  0x00000223f8781f20: 6048 8944 | 24f8 8b44 | 2420 8944 | 2458 488b | 4424 f844 | 8954 2454 | 4489 4424 | 5048 ff74 
  0x00000223f8781f40: 2428 488f | 4424 2048 | 8b6c 2448 

  0x00000223f8781f4c: ;   {metadata('java/lang/String')}
  0x00000223f8781f4c: 48ba b01e | 0400 0100 | 0000 6690 

  0x00000223f8781f58: ;   {runtime_call _new_instance_Java}
  0x00000223f8781f58: c5f8 77e8 

  0x00000223f8781f5c: ; ImmutableOopMap {rbp=Oop [32]=Oop [48]=Oop [64]=Oop [80]=NarrowOop [92]=NarrowOop [96]=Oop [104]=Derived_oop_[96] [112]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.lang.StringLatin1::newString@7
                      ; - java.lang.String::substring@41
                      ; - java.lang.ClassLoader::checkCerts@21
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8781f5c: 2053 03ff 

  0x00000223f8781f60: ;   {other}
  0x00000223f8781f60: 0f1f 8400 | 5011 000b | 4889 6c24 | 4848 ff74 | 2420 488f | 4424 2844 | 8b44 2450 | 448b 5424 
  0x00000223f8781f80: 5448 8944 | 24f8 8b44 | 2458 8944 | 2420 488b | 4424 f84c | 8b5c 2460 | 4c8b 7424 | 6848 ff74 
  0x00000223f8781fa0: 2470 488f | 4424 508b | 5c24 5c8b | 4c24 7848 | 8bf0 e91b | f4ff ff33 | c0e9 def6 | ffff 4c89 
  0x00000223f8781fc0: 8c24 8000 | 0000 4889 | 7424 7889 | 5c24 7048 | ff74 2450 | 488f 4424 | 684c 8974 | 2460 4c89 
  0x00000223f8781fe0: 5c24 5848 | 8944 24f8 | 8b44 2420 | 8944 2454 | 488b 4424 | f844 8944 | 2450 48ff | 7424 2848 
  0x00000223f8782000: 8f44 2420 | 488b 6c24 

  0x00000223f8782008: ;   {metadata({type array byte})}
  0x00000223f8782008: 4848 ba28 | 0a04 0001 | 0000 0045 | 8bc2 6690 

  0x00000223f8782018: ;   {runtime_call _new_array_nozero_Java}
  0x00000223f8782018: c5f8 77e8 

  0x00000223f878201c: ; ImmutableOopMap {rbp=Oop [32]=Oop [48]=Oop [64]=Oop [80]=NarrowOop [88]=Oop [96]=Derived_oop_[88] [104]=Oop [112]=NarrowOop [120]=Oop }
                      ;*invokevirtual clone {reexecute=1 rethrow=0 return_oop=1}
                      ; - (reexecute) java.util.Arrays::copyOfRange@18
                      ; - java.lang.StringLatin1::newString@16
                      ; - java.lang.String::substring@41
                      ; - java.lang.ClassLoader::checkCerts@21
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f878201c: 6058 03ff 

  0x00000223f8782020: ;   {other}
  0x00000223f8782020: 0f1f 8400 | 1012 000c | 4889 6c24 | 4848 ff74 | 2420 488f | 4424 2848 | 8944 24f8 | 8b44 2454 
  0x00000223f8782040: 8944 2420 | 488b 4424 | f84c 8b74 | 2460 48ff | 7424 6848 | 8f44 2450 | 8b5c 2470 | 488b 7424 
  0x00000223f8782060: 784c 8b8c | 2480 0000 | 004c 8be8 | e9d5 f4ff | ffba e4ff | ffff 894c | 2420 448b | 5424 3844 
  0x00000223f8782080: 8954 2424 | 448b 5c24 | 3c44 895c | 2438 895c | 243c 4489 | 4424 6490 

  0x00000223f8782098: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782098: c5f8 77e8 

  0x00000223f878209c: ; ImmutableOopMap {[40]=Oop [48]=Oop [60]=NarrowOop [64]=Oop [72]=Oop [80]=Oop [100]=NarrowOop }
                      ;*baload {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringLatin1::lastIndexOf@24
                      ; - java.lang.String::lastIndexOf@13
                      ; - java.lang.String::lastIndexOf@8
                      ; - java.lang.ClassLoader::checkCerts@3
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f878209c: 6025 f7fe 

  0x00000223f87820a0: ;   {other}
  0x00000223f87820a0: 0f1f 8400 | 9012 000d | 4c89 8424 | 8800 0000 | 4c89 8c24 | 8000 0000 | 4889 7424 | 7889 4c24 
  0x00000223f87820c0: 7489 5c24 | 7048 ff74 | 2450 488f | 4424 684c | 8974 2460 | 4c89 5c24 | 5848 8944 | 24f8 8b44 
  0x00000223f87820e0: 2420 8944 | 2450 488b | 4424 f848 | ff74 2448 | 488f 4424 

  0x00000223f87820f4: ;   {metadata({type array byte})}
  0x00000223f87820f4: 2048 ba28 | 0a04 0001 | 0000 0044 | 8bc1 6690 

  0x00000223f8782104: ;   {runtime_call _new_array_nozero_Java}
  0x00000223f8782104: c5f8 77e8 

  0x00000223f8782108: ; ImmutableOopMap {[32]=Oop [40]=Oop [48]=Oop [64]=Oop [88]=Oop [96]=Derived_oop_[88] [104]=Oop [112]=NarrowOop [120]=Oop }
                      ;*newarray {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.util.Arrays::copyOfRangeByte@10
                      ; - java.util.Arrays::copyOfRange@13
                      ; - java.lang.StringLatin1::newString@16
                      ; - java.lang.String::substring@41
                      ; - java.lang.ClassLoader::checkCerts@21
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782108: 7457 03ff 

  0x00000223f878210c: ;   {other}
  0x00000223f878210c: 0f1f 8400 | fc12 000e | 48ff 7424 | 2048 8f44 | 2448 4889 | 4424 f88b | 4424 5089 | 4424 2048 
  0x00000223f878212c: 8b44 24f8 | 4c8b 7424 | 6048 ff74 | 2468 488f | 4424 508b | 5c24 708b | 4c24 7448 | 8b74 2478 
  0x00000223f878214c: 4c8b e84c | 8b8c 2480 | 0000 004c | 8b84 2488 | 0000 0090 | e923 f3ff | ffba f6ff | ffff 488b 
  0x00000223f878216c: ee66 6690 

  0x00000223f8782170: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782170: c5f8 77e8 

  0x00000223f8782174: ; ImmutableOopMap {rbp=Oop [32]=Oop }
                      ;*invokevirtual putIfAbsent {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::checkCerts@62
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782174: 8824 f7fe 

  0x00000223f8782178: ;   {other}
  0x00000223f8782178: 0f1f 8400 | 6813 000f | baf6 ffff | ff66 6690 

  0x00000223f8782188: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782188: c5f8 77e8 

  0x00000223f878218c: ; ImmutableOopMap {rbp=NarrowOop }
                      ;*invokevirtual toExternalForm {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.net.URL::toExternalForm@5
                      ; - java.net.URL::toString@1
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22
                      ; - java.lang.ClassLoader::defineClass@12
  0x00000223f878218c: 7024 f7fe 

  0x00000223f8782190: ;   {other}
  0x00000223f8782190: 0f1f 8400 | 8013 0010 | ba45 ffff | ff48 8b6c | 2428 6690 

  0x00000223f87821a4: ;   {runtime_call UncommonTrapBlob}
  0x00000223f87821a4: c5f8 77e8 

  0x00000223f87821a8: ; ImmutableOopMap {rbp=Oop [48]=Oop [64]=Oop [72]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::checkName@8
                      ; - java.lang.ClassLoader::preDefineClass@1
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87821a8: 5424 f7fe 

  0x00000223f87821ac: ;   {other}
  0x00000223f87821ac: 0f1f 8400 | 9c13 0011 | bace ffff | ff48 8b6c | 2428 48ff | 7424 4848 | 8f44 2420 | 4489 4424 
  0x00000223f87821cc: 5889 5c24 | 5c66 6690 

  0x00000223f87821d4: ;   {runtime_call UncommonTrapBlob}
  0x00000223f87821d4: c5f8 77e8 

  0x00000223f87821d8: ; ImmutableOopMap {rbp=Oop [32]=Oop [48]=Oop [64]=Oop [88]=NarrowOop }
                      ;*invokestatic indexOfChar {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringLatin1::indexOf@33
                      ; - java.lang.String::indexOf@17
                      ; - java.lang.String::indexOf@3
                      ; - java.lang.ClassLoader::checkName@16
                      ; - java.lang.ClassLoader::preDefineClass@1
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87821d8: 2424 f7fe 

  0x00000223f87821dc: ;   {other}
  0x00000223f87821dc: 0f1f 8400 | cc13 0012 | 4533 db85 | c941 0f94 | c3ba 45ff | ffff 488b | 6c24 2848 | ff74 2448 
  0x00000223f87821fc: 488f 4424 | 2044 895c | 2460 6690 

  0x00000223f8782208: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782208: c5f8 77e8 

  0x00000223f878220c: ; ImmutableOopMap {rbp=Oop [32]=Oop [48]=Oop [64]=Oop }
                      ;*ifeq {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.String::indexOf@4
                      ; - java.lang.String::indexOf@3
                      ; - java.lang.ClassLoader::checkName@16
                      ; - java.lang.ClassLoader::preDefineClass@1
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f878220c: f023 f7fe 

  0x00000223f8782210: ;   {other}
  0x00000223f8782210: 0f1f 8400 | 0014 0013 | ba45 ffff | ff89 4c24 | 2089 4c24 | 2489 5c24 | 6044 894c | 2470 6690 
  0x00000223f8782230: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782230: c5f8 77e8 

  0x00000223f8782234: ; ImmutableOopMap {[40]=Oop [48]=Oop [64]=Oop [72]=Oop [80]=Oop [96]=NarrowOop }
                      ;*if_icmpgt {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) jdk.internal.util.Preconditions::checkFromToIndex@6
                      ; - java.lang.String::checkBoundsBeginEnd@6
                      ; - java.lang.String::substring@8
                      ; - java.lang.ClassLoader::checkCerts@21
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782234: c823 f7fe 

  0x00000223f8782238: ;   {other}
  0x00000223f8782238: 0f1f 8400 | 2814 0014 | ba45 ffff | ff48 8b6c | 2428 895c | 2450 6690 

  0x00000223f8782250: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782250: c5f8 77e8 

  0x00000223f8782254: ; ImmutableOopMap {rbp=Oop [48]=Oop [64]=Oop [72]=Oop }
                      ;*if_icmpne {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::checkName@20
                      ; - java.lang.ClassLoader::preDefineClass@1
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782254: a823 f7fe 

  0x00000223f8782258: ;   {other}
  0x00000223f8782258: 0f1f 8400 | 4814 0015 | ba45 ffff | ff66 6690 

  0x00000223f8782268: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782268: c5f8 77e8 

  0x00000223f878226c: ; ImmutableOopMap {[40]=Oop [48]=Oop [64]=Oop [72]=Oop }
                      ;*if_icmpne {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::checkName@30
                      ; - java.lang.ClassLoader::preDefineClass@1
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f878226c: 9023 f7fe 

  0x00000223f8782270: ;   {other}
  0x00000223f8782270: 0f1f 8400 | 6014 0016 | ba45 ffff | ff48 8b6c | 2428 48ff | 7424 3048 | 8f44 2428 | 4889 4424 
  0x00000223f8782290: f88b 4424 | 3889 4424 | 3048 8b44 | 24f8 4889 | 4424 f88b | 4424 3c89 | 4424 3448 | 8b44 24f8 
  0x00000223f87822b0: 48ff 7424 | 4048 8f44 | 2438 48ff | 7424 4848 | 8f44 2440 | 4489 4424 | 4866 6690 

  0x00000223f87822cc: ;   {runtime_call UncommonTrapBlob}
  0x00000223f87822cc: c5f8 77e8 

  0x00000223f87822d0: ; ImmutableOopMap {rbp=Oop [40]=Oop [56]=Oop [64]=Oop }
                      ;*ifne {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::compareCerts@7
                      ; - java.lang.ClassLoader::checkCerts@80
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87822d0: 2c23 f7fe 

  0x00000223f87822d4: ;   {other}
  0x00000223f87822d4: 0f1f 8400 | c414 0017 | ba45 ffff | ff89 4c24 | 2089 4c24 | 2489 5c24 | 6044 894c | 2470 4489 
  0x00000223f87822f4: 4c24 7490 

  0x00000223f87822f8: ;   {runtime_call UncommonTrapBlob}
  0x00000223f87822f8: c5f8 77e8 

  0x00000223f87822fc: ; ImmutableOopMap {[40]=Oop [48]=Oop [64]=Oop [72]=Oop [80]=Oop [96]=NarrowOop }
                      ;*if_icmple {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) jdk.internal.util.Preconditions::checkFromToIndex@11
                      ; - java.lang.String::checkBoundsBeginEnd@6
                      ; - java.lang.String::substring@8
                      ; - java.lang.ClassLoader::checkCerts@21
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87822fc: 0023 f7fe 

  0x00000223f8782300: ;   {other}
  0x00000223f8782300: 0f1f 8400 | f014 0018 | 498b d7c5 | f877 49ba | 70ee d1a7 | fd7f 0000 

  0x00000223f8782318: ;   {other}
  0x00000223f8782318: 41ff d20f | 1f84 0000 | 0000 00e9 | 56f2 ffff | 4c63 c9e9 | bff0 ffff | 4983 c1fe | 498b c949 
  0x00000223f8782338: 8bfa 4833 | c048 83f9 | 087f 1048 | ffc9 784f | 4889 04cf | 48ff c97d | f7eb 44c5 | fdef c0e9 
  0x00000223f8782358: 0d00 0000 | c5fe 7f07 | c5fe 7f47 | 2048 83c7 | 4048 83e9 | 087d ed48 | 83c1 047c | 0cc5 fe7f 
  0x00000223f8782378: 0748 83c7 | 2048 83e9 | 0448 83c1 | 047e 1048 | ffc9 c5f9 | d607 4883 | c708 48ff | c97d f3e9 
  0x00000223f8782398: 30f1 ffff | bacc ffff | ff89 4c24 | 2044 8b5c | 2438 4489 | 5c24 2444 | 8b54 243c | 4489 5424 
  0x00000223f87823b8: 3889 5c24 | 3c48 8974 | 2468 4489 | 4424 7090 

  0x00000223f87823c8: ;   {runtime_call UncommonTrapBlob}
  0x00000223f87823c8: c5f8 77e8 

  0x00000223f87823cc: ; ImmutableOopMap {[40]=Oop [48]=Oop [60]=NarrowOop [64]=Oop [72]=Oop [80]=Oop [104]=Oop [112]=NarrowOop }
                      ;*newarray {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.Arrays::copyOfRangeByte@10
                      ; - java.util.Arrays::copyOfRange@13
                      ; - java.lang.StringLatin1::newString@16
                      ; - java.lang.String::substring@41
                      ; - java.lang.ClassLoader::checkCerts@21
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87823cc: 3022 f7fe 

  0x00000223f87823d0: ;   {other}
  0x00000223f87823d0: 0f1f 8400 | c015 0019 | bae4 ffff | ff48 8b6c | 2428 4489 | 4424 5490 

  0x00000223f87823e8: ;   {runtime_call UncommonTrapBlob}
  0x00000223f87823e8: c5f8 77e8 

  0x00000223f87823ec: ; ImmutableOopMap {rbp=Oop [48]=Oop [64]=Oop [72]=Oop [84]=NarrowOop }
                      ;*baload {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.util.ArraysSupport::mismatch@82
                      ; - java.lang.String::startsWith@70
                      ; - java.lang.String::startsWith@3
                      ; - java.lang.ClassLoader::preDefineClass@43
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87823ec: 1022 f7fe 

  0x00000223f87823f0: ;   {other}
  0x00000223f87823f0: 0f1f 8400 | e015 001a | 8b7c 2458 | 448b 5714 | 4585 d20f | 84d3 f6ff | ff4d 8b5f | 2849 8bca 
  0x00000223f8782410: 4d85 db0f | 848b 0300 | 004d 8b57 | 384b 894c | 1af8 4983 | c3f8 4d89 | 5f28 e9ad | f6ff ff4d 
  0x00000223f8782430: 8b57 484d | 8b5f 58f0 | 8344 24c0 | 0066 6690 | 4180 3800 | 7435 4588 | 204d 85d2 | 740f 4f89 
  0x00000223f8782450: 4413 f849 | 83c2 f84d | 8957 48eb | 1e49 8bc8 | 498b d7c5 | f877 49ba | 50ee d1a7 | fd7f 0000 
  0x00000223f8782470: ;   {other}
  0x00000223f8782470: 41ff d20f | 1f84 0000 | 0000 0044 | 8b57 1490 | 4585 d20f | 843c 0300 | 0049 8bda | e97f f6ff 
  0x00000223f8782490: ff48 897c | 2468 4489 | 5c24 6048 | 8974 2458 | 48ff 7424 | 4848 8f44 | 2420 4833 | d24a 8d14 
  0x00000223f87824b0: d266 6690 

  0x00000223f87824b4: ;   {runtime_call _new_array_Java}
  0x00000223f87824b4: c5f8 77e8 

  0x00000223f87824b8: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [48]=Oop [64]=Oop [80]=Oop [88]=Oop [96]=NarrowOop }
                      ;*invokevirtual clone {reexecute=1 rethrow=0 return_oop=1}
                      ; - (reexecute) java.security.CodeSource::getCertificates@11
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87824b8: c450 03ff 

  0x00000223f87824bc: ;   {other}
  0x00000223f87824bc: 0f1f 8400 | ac16 001b | 48ff 7424 | 2048 8f44 | 2448 488b | 7424 5844 | 8b5c 2460 | 488b 7c24 
  0x00000223f87824dc: 6848 8bd8 | e95c f3ff | ff4c 894c | 2470 4489 | 5424 6448 | 8974 2468 | 895c 2460 | 48ff 7424 
  0x00000223f87824fc: 5048 8f44 | 2458 4889 | 4424 f88b | 4424 2089 | 4424 5048 | 8b44 24f8 | 48ff 7424 | 2848 8f44 
  0x00000223f878251c: 2420 488b 

  0x00000223f8782520: ;   {metadata('java/util/ArrayList')}
  0x00000223f8782520: 6c24 4848 | ba28 7205 | 0001 0000 | 0066 6690 

  0x00000223f8782530: ;   {runtime_call _new_instance_Java}
  0x00000223f8782530: c5f8 77e8 

  0x00000223f8782534: ; ImmutableOopMap {rbp=Oop [32]=Oop [48]=Oop [64]=Oop [88]=Oop [96]=NarrowOop [100]=NarrowOop [104]=Oop [112]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.security.CodeSource::getCertificates@25
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782534: 484d 03ff 

  0x00000223f8782538: ;   {other}
  0x00000223f8782538: 0f1f 8400 | 2817 001c | 4889 6c24 | 4848 ff74 | 2420 488f | 4424 2848 | 8944 24f8 | 8b44 2450 
  0x00000223f8782558: 8944 2420 | 488b 4424 | f848 ff74 | 2458 488f | 4424 508b | 5c24 6048 | 8b74 2468 | 448b 5424 
  0x00000223f8782578: 644c 8b4c | 2470 6690 | e935 f3ff | ff4c 8944 | 2478 4c89 | 4c24 7048 | 8974 2468 | 4c89 5424 
  0x00000223f8782598: 6089 5c24 | 5844 8b54 | 2420 48ff | 7424 2848 | 8f44 2420 | 488b 6c24 

  0x00000223f87825b0: ;   {metadata('java/security/cert/Certificate'[])}
  0x00000223f87825b0: 4848 bac8 | b102 0001 | 0000 0045 | 8bc2 6690 

  0x00000223f87825c0: ;   {runtime_call _new_array_Java}
  0x00000223f87825c0: c5f8 77e8 

  0x00000223f87825c4: ; ImmutableOopMap {rbp=Oop [32]=Oop [48]=Oop [64]=Oop [80]=Oop [88]=NarrowOop [104]=Oop [112]=Oop [120]=Oop }
                      ;*anewarray {reexecute=0 rethrow=0 return_oop=1}
                      ; - java.security.CodeSource::getCertificates@70
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87825c4: b84f 03ff 

  0x00000223f87825c8: ;   {other}
  0x00000223f87825c8: 0f1f 8400 | b817 001d | e9c3 f4ff | ff4c 895c | 2448 4889 | 5c24 2848 | 33d2 4a8d | 14d2 6690 
  0x00000223f87825e8: ;   {runtime_call _new_array_Java}
  0x00000223f87825e8: c5f8 77e8 

  0x00000223f87825ec: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [48]=Oop [64]=Oop [80]=Oop [104]=Oop }
                      ;*invokevirtual clone {reexecute=1 rethrow=0 return_oop=1}
                      ; - (reexecute) java.security.CodeSource::getCertificates@86
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87825ec: 904f 03ff 

  0x00000223f87825f0: ;   {other}
  0x00000223f87825f0: 0f1f 8400 | e017 001e | 488b 5c24 | 284c 8b5c | 2448 488b | f8e9 77f5 | ffff ba76 | ffff ff48 
  0x00000223f8782610: 8b6c 2428 | 4489 5424 | 6044 895c | 2464 895c | 2468 4c89 | 4c24 7044 | 8944 2478 

  0x00000223f878262c: ;   {runtime_call UncommonTrapBlob}
  0x00000223f878262c: c5f8 77e8 

  0x00000223f8782630: ; ImmutableOopMap {rbp=Oop [32]=Oop [48]=Oop [64]=Oop [72]=Oop [104]=NarrowOop [112]=Oop }
                      ;*if_icmpge {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::compareCerts@67
                      ; - java.lang.ClassLoader::checkCerts@80
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782630: cc1f f7fe 

  0x00000223f8782634: ;   {other}
  0x00000223f8782634: 0f1f 8400 | 2418 001f | ba76 ffff | ff48 8bac | 2488 0000 | 004c 8b8c | 2480 0000 | 004c 894c 
  0x00000223f8782654: 2420 48ff | 7424 3048 | 8f44 2428 | 4889 4424 | f88b 4424 | 3889 4424 | 3048 8b44 | 24f8 4889 
  0x00000223f8782674: 4424 f88b | 4424 3c89 | 4424 3448 | 8b44 24f8 | 4c8b 4c24 | 404c 894c | 2438 4c8b | 8c24 9000 
  0x00000223f8782694: 0000 4489 | 4424 5c44 | 895c 2460 | 4c89 4c24 | 6844 8b9c | 2498 0000 | 0045 8bd3 | 4489 5424 
  0x00000223f87826b4: 7066 6690 

  0x00000223f87826b8: ;   {runtime_call UncommonTrapBlob}
  0x00000223f87826b8: c5f8 77e8 

  0x00000223f87826bc: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [56]=Oop [80]=Oop [96]=NarrowOop [104]=Oop [144]=Oop }
                      ;*if_icmpge {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::compareCerts@152
                      ; - java.lang.ClassLoader::checkCerts@80
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87826bc: 401f f7fe 

  0x00000223f87826c0: ;   {other}
  0x00000223f87826c0: 0f1f 8400 | b018 0020 | bae4 ffff | ff48 8b6c | 2428 48ff | 7424 3048 | 8f44 2428 | 4889 4424 
  0x00000223f87826e0: f88b 4424 | 3889 4424 | 3048 8b44 | 24f8 4889 | 4424 f88b | 4424 3c89 | 4424 3448 | 8b44 24f8 
  0x00000223f8782700: 48ff 7424 | 4048 8f44 | 2438 48ff | 7424 5048 | 8f44 2440 | 4889 7424 | 5089 5c24 | 584c 8944 
  0x00000223f8782720: 2468 4489 | 5424 7044 | 895c 2474 

  0x00000223f878272c: ;   {runtime_call UncommonTrapBlob}
  0x00000223f878272c: c5f8 77e8 

  0x00000223f8782730: ; ImmutableOopMap {rbp=Oop [40]=Oop [56]=Oop [64]=Oop [72]=Oop [80]=Oop [88]=NarrowOop [104]=Oop [112]=NarrowOop }
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.CodeSource::getCertificates@50
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782730: cc1e f7fe 

  0x00000223f8782734: ;   {other}
  0x00000223f8782734: 0f1f 8400 | 2419 0021 | ba37 0100 | 0048 8beb | 48ff 7424 | 4848 8f44 | 2420 48ff | 7424 3048 
  0x00000223f8782754: 8f44 2428 | 4889 4424 | f88b 4424 | 3889 4424 | 3048 8b44 | 24f8 8974 | 2434 6690 

  0x00000223f8782770: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782770: c5f8 77e8 

  0x00000223f8782774: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::preDefineClass@56
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782774: 881e f7fe 

  0x00000223f8782778: ;   {other}
  0x00000223f8782778: 0f1f 8400 | 6819 0022 | ba76 ffff | ff48 8b6c | 2428 4c89 | 4c24 5044 | 8954 2464 

  0x00000223f8782794: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782794: c5f8 77e8 

  0x00000223f8782798: ; ImmutableOopMap {rbp=Oop [32]=Oop [48]=Oop [64]=Oop [72]=Oop [80]=Oop }
                      ;*if_icmpge {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::compareCerts@40
                      ; - java.lang.ClassLoader::checkCerts@80
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782798: 641e f7fe 

  0x00000223f878279c: ;   {other}
  0x00000223f878279c: 0f1f 8400 | 8c19 0023 | 498b d7c5 | f877 49ba | 70ee d1a7 | fd7f 0000 

  0x00000223f87827b4: ;   {other}
  0x00000223f87827b4: 41ff d20f | 1f84 0000 | 0000 0090 | e917 f3ff | ffba f6ff | ffff 6690 

  0x00000223f87827cc: ;   {runtime_call UncommonTrapBlob}
  0x00000223f87827cc: c5f8 77e8 

  0x00000223f87827d0: ; ImmutableOopMap {}
                      ;*invokevirtual clone {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.security.CodeSource::getCertificates@86
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87827d0: 2c1e f7fe 

  0x00000223f87827d4: ;   {other}
  0x00000223f87827d4: 0f1f 8400 | c419 0024 | bade ffff | ff48 8b6c | 2428 4889 | 4424 5890 

  0x00000223f87827ec: ;   {runtime_call UncommonTrapBlob}
  0x00000223f87827ec: c5f8 77e8 

  0x00000223f87827f0: ; ImmutableOopMap {rbp=Oop [32]=Oop [48]=Oop [64]=Oop [72]=Oop [80]=Oop [88]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::checkCerts@65
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87827f0: 0c1e f7fe 

  0x00000223f87827f4: ;   {other}
  0x00000223f87827f4: 0f1f 8400 | e419 0025 | bade ffff | ff48 ff74 | 2430 488f | 4424 2848 | 8944 24f8 | 8b44 2438 
  0x00000223f8782814: 8944 2430 | 488b 4424 | f848 8944 | 24f8 8b44 | 243c 8944 | 2434 488b | 4424 f848 | ff74 2440 
  0x00000223f8782834: 488f 4424 | 3848 ff74 | 2450 488f | 4424 4048 | 8944 2460 

  0x00000223f8782848: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782848: c5f8 77e8 

  0x00000223f878284c: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [56]=Oop [64]=Oop [88]=NarrowOop [96]=Oop [104]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.CodeSource::getCertificates@76
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f878284c: b01d f7fe 

  0x00000223f8782850: ;   {other}
  0x00000223f8782850: 0f1f 8400 | 401a 0026 | baf6 ffff | ff66 6690 

  0x00000223f8782860: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782860: c5f8 77e8 

  0x00000223f8782864: ; ImmutableOopMap {}
                      ;*arraylength {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::isEmpty@4
                      ; - java.lang.ClassLoader::checkName@5
                      ; - java.lang.ClassLoader::preDefineClass@1
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782864: 981d f7fe 

  0x00000223f8782868: ;   {other}
  0x00000223f8782868: 0f1f 8400 | 581a 0027 | ba45 ffff | ff48 8b6c | 2428 48ff | 7424 4848 | 8f44 2420 

  0x00000223f8782884: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782884: c5f8 77e8 

  0x00000223f8782888: ; ImmutableOopMap {rbp=Oop [32]=Oop [48]=Oop [64]=Oop }
                      ;*ifnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::checkName@1
                      ; - java.lang.ClassLoader::preDefineClass@1
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782888: 741d f7fe 

  0x00000223f878288c: ;   {other}
  0x00000223f878288c: 0f1f 8400 | 7c1a 0028 | ba45 ffff | ff48 8b6c | 2428 48ff | 7424 3048 | 8f44 2428 | 4889 4424 
  0x00000223f87828ac: f88b 4424 | 3889 4424 | 3048 8b44 | 24f8 8974 | 2434 48ff | 7424 5048 | 8f44 2438 

  0x00000223f87828c8: ;   {runtime_call UncommonTrapBlob}
  0x00000223f87828c8: c5f8 77e8 

  0x00000223f87828cc: ; ImmutableOopMap {rbp=Oop [40]=Oop [56]=Oop [64]=Oop [72]=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::preDefineClass@95
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87828cc: 301d f7fe 

  0x00000223f87828d0: ;   {other}
  0x00000223f87828d0: 0f1f 8400 | c01a 0029 | baf6 ffff | ff41 8beb 

  0x00000223f87828e0: ;   {runtime_call UncommonTrapBlob}
  0x00000223f87828e0: c5f8 77e8 

  0x00000223f87828e4: ; ImmutableOopMap {}
                      ;*aaload {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.CodeSource::getCertificates@50
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87828e4: 181d f7fe 

  0x00000223f87828e8: ;   {other}
  0x00000223f87828e8: 0f1f 8400 | d81a 002a | baf6 ffff | ff66 6690 

  0x00000223f87828f8: ;   {runtime_call UncommonTrapBlob}
  0x00000223f87828f8: c5f8 77e8 

  0x00000223f87828fc: ; ImmutableOopMap {}
                      ;*invokevirtual getSignerCertPath {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.CodeSource::getCertificates@51
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87828fc: 001d f7fe 

  0x00000223f8782900: ;   {other}
  0x00000223f8782900: 0f1f 8400 | f01a 002b | baf6 ffff | ff66 6690 

  0x00000223f8782910: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782910: c5f8 77e8 

  0x00000223f8782914: ; ImmutableOopMap {}
                      ;*invokevirtual getCertificates {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.CodeSource::getCertificates@54
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782914: e81c f7fe 

  0x00000223f8782918: ;   {other}
  0x00000223f8782918: 0f1f 8400 | 081b 002c | baf6 ffff | ff66 6690 

  0x00000223f8782928: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782928: c5f8 77e8 

  0x00000223f878292c: ; ImmutableOopMap {}
                      ;*arraylength {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.CodeSource::getCertificates@40
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f878292c: d01c f7fe 

  0x00000223f8782930: ;   {other}
  0x00000223f8782930: 0f1f 8400 | 201b 002d | ba97 ffff | ff48 ff74 | 2430 488f | 4424 2848 | 8944 24f8 | 8b44 2438 
  0x00000223f8782950: 8944 2430 | 488b 4424 | f848 8944 | 24f8 8b44 | 243c 8944 | 2434 488b | 4424 f848 | ff74 2440 
  0x00000223f8782970: 488f 4424 | 3848 896c | 2448 4c8b | 5424 784c | 8954 2460 

  0x00000223f8782984: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782984: c5f8 77e8 

  0x00000223f8782988: ; ImmutableOopMap {[32]=Oop [40]=Oop [56]=Oop [72]=Oop [80]=NarrowOop [96]=Oop [104]=Oop [112]=NarrowOop [120]=Oop }
                      ;*invokevirtual clone {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.util.Arrays::copyOfRange@18
                      ; - java.lang.StringLatin1::newString@16
                      ; - java.lang.String::substring@41
                      ; - java.lang.ClassLoader::checkCerts@21
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782988: 741c f7fe 

  0x00000223f878298c: ;   {other}
  0x00000223f878298c: 0f1f 8400 | 7c1b 002e | baf4 ffff | ff48 ff74 | 2430 488f | 4424 2848 | 8944 24f8 | 8b44 2438 
  0x00000223f87829ac: 8944 2430 | 488b 4424 | f848 8944 | 24f8 8b44 | 243c 8944 | 2434 488b | 4424 f848 | ff74 2440 
  0x00000223f87829cc: 488f 4424 | 3848 ff74 | 2450 488f | 4424 4090 

  0x00000223f87829dc: ;   {runtime_call UncommonTrapBlob}
  0x00000223f87829dc: c5f8 77e8 

  0x00000223f87829e0: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [56]=Oop [64]=Oop [88]=NarrowOop [104]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.CodeSource::getCertificates@76
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f87829e0: 1c1c f7fe 

  0x00000223f87829e4: ;   {other}
  0x00000223f87829e4: 0f1f 8400 | d41b 002f | ba97 ffff | ff48 8b6c | 2428 48ff | 7424 3048 | 8f44 2428 | 4889 4424 
  0x00000223f8782a04: f88b 4424 | 3889 4424 | 3048 8b44 | 24f8 4889 | 4424 f88b | 4424 3c89 | 4424 3448 | 8b44 24f8 
  0x00000223f8782a24: 4c8b 5424 | 404c 8954 | 2438 4c8b | 5424 504c | 8954 2440 | 4c8b 5424 | 204c 8954 | 2448 48ff 
  0x00000223f8782a44: 7424 5848 | 8f44 2450 

  0x00000223f8782a4c: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782a4c: c5f8 77e8 

  0x00000223f8782a50: ; ImmutableOopMap {rbp=Oop [40]=Oop [56]=Oop [64]=Oop [72]=Oop [80]=Oop [96]=NarrowOop }
                      ;*invokevirtual clone {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.security.CodeSource::getCertificates@11
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782a50: ac1b f7fe 

  0x00000223f8782a54: ;   {other}
  0x00000223f8782a54: 0f1f 8400 | 441c 0030 | ba97 ffff | ff48 ff74 | 2450 488f | 4424 4890 

  0x00000223f8782a6c: ;   {runtime_call UncommonTrapBlob}
  0x00000223f8782a6c: c5f8 77e8 

  0x00000223f8782a70: ; ImmutableOopMap {rbp=Oop [32]=Oop [40]=Oop [48]=Oop [64]=Oop [72]=Oop [104]=Oop }
                      ;*invokevirtual clone {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.security.CodeSource::getCertificates@86
                      ; - java.lang.ClassLoader::checkCerts@34
                      ; - java.lang.ClassLoader::preDefineClass@113
                      ; - java.lang.ClassLoader::defineClass@4
  0x00000223f8782a70: 8c1b f7fe 

  0x00000223f8782a74: ;   {other}
  0x00000223f8782a74: 0f1f 8400 | 641c 0031 | eb16 6690 | eb12 488b | d0e9 2a00 | 0000 488b | d0e9 2200 | 0000 eb00 
  0x00000223f8782a94: 488b d0eb | 1beb 0048 | 8bd0 6690 | eb12 488b | d0eb 0d48 | 8bd0 eb08 | 488b d0eb | 0348 8bd0 
  0x00000223f8782ab4: c5f8 7748 | 81c4 c000 

  0x00000223f8782abc: ;   {runtime_call _rethrow_Java}
  0x00000223f8782abc: 0000 5de9 | bc70 03ff 

  0x00000223f8782ac4: ;   {internal_word}
  0x00000223f8782ac4: 49ba fa16 | 78f8 2302 | 0000 4d89 | 9760 0400 

  0x00000223f8782ad4: ;   {runtime_call SafepointBlob}
  0x00000223f8782ad4: 00e9 262c 

  0x00000223f8782ad8: ;   {runtime_call StubRoutines (final stubs)}
  0x00000223f8782ad8: f7fe e841 | 70f5 fee9 | 5ae6 ffff | f4f4 f4f4 
[Stub Code]
  0x00000223f8782ae8: ;   {no_reloc}
  0x00000223f8782ae8: 48bb 0000 | 0000 0000 

  0x00000223f8782af0: ;   {runtime_call nmethod}
  0x00000223f8782af0: 0000 e9fb 

  0x00000223f8782af4: ;   {static_stub}
  0x00000223f8782af4: ffff ff48 | bb00 0000 | 0000 0000 

  0x00000223f8782b00: ;   {runtime_call nmethod}
  0x00000223f8782b00: 00e9 fbff 

  0x00000223f8782b04: ;   {static_stub}
  0x00000223f8782b04: ffff 48bb | 0000 0000 | 0000 0000 

  0x00000223f8782b10: ;   {runtime_call nmethod}
  0x00000223f8782b10: e9fb ffff 

  0x00000223f8782b14: ;   {static_stub}
  0x00000223f8782b14: ff48 bb00 | 0000 0000 

  0x00000223f8782b1c: ;   {runtime_call nmethod}
  0x00000223f8782b1c: 0000 00e9 | fbff ffff 

  0x00000223f8782b24: ;   {static_stub}
  0x00000223f8782b24: 48bb 0000 | 0000 0000 

  0x00000223f8782b2c: ;   {runtime_call nmethod}
  0x00000223f8782b2c: 0000 e9fb 

  0x00000223f8782b30: ;   {static_stub}
  0x00000223f8782b30: ffff ff48 | bb00 0000 | 0000 0000 

  0x00000223f8782b3c: ;   {runtime_call nmethod}
  0x00000223f8782b3c: 00e9 fbff 

  0x00000223f8782b40: ;   {static_stub}
  0x00000223f8782b40: ffff 48bb | 0000 0000 | 0000 0000 

  0x00000223f8782b4c: ;   {runtime_call nmethod}
  0x00000223f8782b4c: e9fb ffff 

  0x00000223f8782b50: ;   {static_stub}
  0x00000223f8782b50: ff48 bb00 | 0000 0000 

  0x00000223f8782b58: ;   {runtime_call nmethod}
  0x00000223f8782b58: 0000 00e9 | fbff ffff 
[Exception Handler]
  0x00000223f8782b60: ;   {runtime_call ExceptionBlob}
  0x00000223f8782b60: e91b 0e03 | ffe8 0000 | 0000 4883 

  0x00000223f8782b6c: ;   {runtime_call DeoptimizationBlob}
  0x00000223f8782b6c: 2c24 05e9 | 2c1e f7fe | f4f4 f4f4 
[/MachCode]


Compiled method (c1) 45909 12338       3       java.security.SecureClassLoader::defineClass (16 bytes)
 total in heap  [0x00000223f160cc90,0x00000223f160d090] = 1024
 relocation     [0x00000223f160cdf0,0x00000223f160ce40] = 80
 main code      [0x00000223f160ce40,0x00000223f160cf98] = 344
 stub code      [0x00000223f160cf98,0x00000223f160cfe8] = 80
 metadata       [0x00000223f160cfe8,0x00000223f160cff0] = 8
 scopes data    [0x00000223f160cff0,0x00000223f160d038] = 72
 scopes pcs     [0x00000223f160d038,0x00000223f160d088] = 80
 dependencies   [0x00000223f160d088,0x00000223f160d090] = 8

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x000002238503d670} 'defineClass' '(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;' in 'java/security/SecureClassLoader'
  # this:     rdx:rdx   = 'java/security/SecureClassLoader'
  # parm0:    r8:r8     = 'java/lang/String'
  # parm1:    r9:r9     = '[B'
  # parm2:    rdi       = int
  # parm3:    rsi       = int
  # parm4:    rcx:rcx   = 'java/security/CodeSource'
  #           [sp+0x80]  (sp of caller)
  0x00000223f160ce40: 448b 5208 | 49c1 e203 

  0x00000223f160ce48: ;   {runtime_call ic_miss_stub}
  0x00000223f160ce48: 4c3b d00f | 852f 190e | 0666 6666 | 0f1f 8400 | 0000 0000 | 6666 6690 
[Verified Entry Point]
  0x00000223f160ce60: 8984 2400 | 80ff ff55 | 4883 ec70 | 4181 7f20 | 1100 0000 

  0x00000223f160ce74: ;   {runtime_call StubRoutines (final stubs)}
  0x00000223f160ce74: 7405 e8a5 

  0x00000223f160ce78: ;   {metadata(method data for {method} {0x000002238503d670} 'defineClass' '(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;' in 'java/security/SecureClassLoader')}
  0x00000223f160ce78: cc0c 0648 | bb28 a59a | 8523 0200 | 008b 83cc | 0000 0083 | c002 8983 | cc00 0000 | 25fe 0700 
  0x00000223f160ce98: 0085 c00f | 8496 0000 | 0089 7424 | 5c89 7c24 | 584c 894c | 2450 4c89 | 4424 4848 

  0x00000223f160ceb4: ;   {metadata(method data for {method} {0x000002238503d670} 'defineClass' '(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;' in 'java/security/SecureClassLoader')}
  0x00000223f160ceb4: 8bda 48b8 | 28a5 9a85 | 2302 0000 | 4883 8010 | 0100 0001 | 4c8b c148 | 8bc2 488b | d048 8944 
  0x00000223f160ced4: ;   {optimized virtual_call}
  0x00000223f160ced4: 2440 90e8 

  0x00000223f160ced8: ; ImmutableOopMap {[64]=Oop [72]=Oop [80]=Oop }
                      ;*invokevirtual getProtectionDomain {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.SecureClassLoader::defineClass@9
  0x00000223f160ced8: a4b4 af06 

  0x00000223f160cedc: ;   {other}
  0x00000223f160cedc: 0f1f 8400 | 4c02 0000 | 488b 5424 

  0x00000223f160cee8: ;   {metadata(method data for {method} {0x000002238503d670} 'defineClass' '(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;' in 'java/security/SecureClassLoader')}
  0x00000223f160cee8: 4049 b828 | a59a 8523 | 0200 0049 | 8380 4801 | 0000 014c | 8b44 2448 | 4c8b 4c24 | 508b 7c24 
  0x00000223f160cf08: 588b 7424 | 5c48 8bc8 | 488b 5424 

  0x00000223f160cf14: ;   {optimized virtual_call}
  0x00000223f160cf14: 4066 90e8 

  0x00000223f160cf18: ; ImmutableOopMap {}
                      ;*invokevirtual defineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.SecureClassLoader::defineClass@12
  0x00000223f160cf18: 0442 1707 

  0x00000223f160cf1c: ;   {other}
  0x00000223f160cf1c: 0f1f 8400 | 8c02 0001 | 4883 c470 

  0x00000223f160cf28: ;   {poll_return}
  0x00000223f160cf28: 5d49 3ba7 | 4804 0000 | 0f87 2200 

  0x00000223f160cf34: ;   {metadata({method} {0x000002238503d670} 'defineClass' '(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;' in 'java/security/SecureClassLoader')}
  0x00000223f160cf34: 0000 c349 | ba68 d603 | 8523 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x00000223f160cf4c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x00000223f160cf4c: ffff e8ad 

  0x00000223f160cf50: ; ImmutableOopMap {rdx=Oop r8=Oop r9=Oop rcx=Oop }
                      ;*synchronization entry
                      ; - java.security.SecureClassLoader::defineClass@-1
  0x00000223f160cf50: 541a 06e9 | 49ff ffff 

  0x00000223f160cf58: ;   {internal_word}
  0x00000223f160cf58: 49ba 29cf | 60f1 2302 | 0000 4d89 | 9760 0400 

  0x00000223f160cf68: ;   {runtime_call SafepointBlob}
  0x00000223f160cf68: 00e9 9287 | 0e06 498b | 87f8 0400 | 0049 c787 | f804 0000 | 0000 0000 | 49c7 8700 | 0500 0000 
  0x00000223f160cf88: 0000 0048 | 83c4 705d 

  0x00000223f160cf90: ;   {runtime_call unwind_exception Runtime1 stub}
  0x00000223f160cf90: e96b f419 | 06f4 f4f4 
[Stub Code]
  0x00000223f160cf98: ;   {no_reloc}
  0x00000223f160cf98: 48bb 0000 | 0000 0000 

  0x00000223f160cfa0: ;   {runtime_call nmethod}
  0x00000223f160cfa0: 0000 e9fb 

  0x00000223f160cfa4: ;   {static_stub}
  0x00000223f160cfa4: ffff ff48 | bb30 4e02 | 8523 0200 

  0x00000223f160cfb0: ;   {runtime_call I2C/C2I adapters}
  0x00000223f160cfb0: 00e9 3646 

  0x00000223f160cfb4: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x00000223f160cfb4: 0d06 e845 

  0x00000223f160cfb8: ;   {external_word}
  0x00000223f160cfb8: 211a 0648 | b950 de33 | a8fd 7f00 | 0048 83e4 

  0x00000223f160cfc8: ;   {runtime_call}
  0x00000223f160cfc8: f048 b8b0 | 7902 a8fd | 7f00 00ff 

  0x00000223f160cfd4: ;   {section_word}
  0x00000223f160cfd4: d0f4 49ba | d6cf 60f1 | 2302 0000 

  0x00000223f160cfe0: ;   {runtime_call DeoptimizationBlob}
  0x00000223f160cfe0: 4152 e9b9 | 790e 06f4 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000022397b6aef0, length=172, elements={
0x00000223ecf1a230, 0x0000022384c794c0, 0x0000022384c79ee0, 0x0000022384cae220,
0x0000022384caef70, 0x0000022384cd1ef0, 0x0000022384cd36b0, 0x0000022384cb00f0,
0x0000022384cb3790, 0x0000022384cd5780, 0x0000022384cd3d40, 0x0000022384cd43d0,
0x0000022384cd50f0, 0x0000022384cd5e10, 0x0000022384cd2990, 0x0000022384cd3020,
0x0000022384cd4a60, 0x000002238b655170, 0x000002238b657f60, 0x000002238b651660,
0x000002238b650fd0, 0x000002238b6530a0, 0x000002238b656bb0, 0x000002238b656520,
0x000002238b652380, 0x000002238b653730, 0x000002238b652a10, 0x000002238b655800,
0x000002238b651cf0, 0x000002238b653dc0, 0x000002238b6578d0, 0x000002238b654450,
0x000002238b654ae0, 0x000002238b657240, 0x000002238d6e3850, 0x000002238d6e5920,
0x000002238d6ea7e0, 0x000002238d6e6640, 0x000002238d6e5fb0, 0x000002238d6e7360,
0x000002238d6e8da0, 0x000002238d6e9430, 0x000002238d6e9ac0, 0x000002238d6eae70,
0x000002238d6e4570, 0x000002238d6e4c00, 0x000002238bf90cc0, 0x000002238bf94e60,
0x000002238bf8ffa0, 0x000002238bf91350, 0x000002238bf947d0, 0x000002238bf90630,
0x000002238bf8ded0, 0x000002238bf94140, 0x000002238bf919e0, 0x000002238bf8e560,
0x000002238bf8f280, 0x000002238bf954f0, 0x000002238bf92700, 0x000002238bf92070,
0x000002238bf92d90, 0x000002238bf8ebf0, 0x000002238bf93420, 0x000002238bf93ab0,
0x000002238bf8f910, 0x000002238f2b9430, 0x000002238f2bae70, 0x000002238f2b8da0,
0x000002238f2bbb90, 0x000002238f2bc220, 0x000002238f2bb500, 0x000002238f2ba7e0,
0x000002238f2b6640, 0x000002238f2aa7c0, 0x000002238f2aae50, 0x000002238f2ab4e0,
0x000002238c9f2110, 0x000002238c9ef320, 0x000002238c9f06d0, 0x000002238c9f0d60,
0x000002238c9ee600, 0x000002238c9ef9b0, 0x000002238c9f13f0, 0x000002238c9f1a80,
0x00000223917096d0, 0x0000022391707600, 0x000002239170b110, 0x000002239170a3f0,
0x000002239170be30, 0x000002239170c4c0, 0x000002239170cb50, 0x0000022391f5c4a0,
0x0000022391f5a3d0, 0x0000022391f59020, 0x0000022391f59d40, 0x0000022391f5b0f0,
0x0000022391f5aa60, 0x0000022391f5be10, 0x0000022391f5cb30, 0x0000022391f5e570,
0x0000022391f5ffb0, 0x0000022391f596b0, 0x0000022391950910, 0x0000022391953070,
0x0000022391952350, 0x00000223919557d0, 0x00000223919529e0, 0x0000022391955140,
0x0000022391950fa0, 0x0000022391950280, 0x0000022391953700, 0x0000022391953d90,
0x0000022391954420, 0x0000022391954ab0, 0x000002239194eed0, 0x0000022391951cc0,
0x000002239194f560, 0x0000022391956b80, 0x000002239194fbf0, 0x000002239195b3b0,
0x00000223919578a0, 0x000002239195db10, 0x00000223919585c0, 0x000002239195cdf0,
0x0000022391958c50, 0x000002239195c760, 0x000002239195a690, 0x0000022391959970,
0x00000223919592e0, 0x000002239195a000, 0x000002239195ba40, 0x000002239195ad20,
0x000002239195c0d0, 0x000002239195d480, 0x00000223917089b0, 0x000002239170aa80,
0x0000022391709d60, 0x00000223917068e0, 0x000002239170b7a0, 0x0000022391707c90,
0x000002239170d1e0, 0x0000022391706250, 0x0000022391708320, 0x000002238c9f0040,
0x000002238c9f3b50, 0x000002238c9ecbc0, 0x000002238c9f41e0, 0x000002238c9ed250,
0x000002238c9ed8e0, 0x000002238c9edf70, 0x000002238c9eec90, 0x000002238d0cfbd0,
0x000002238d0d1ca0, 0x000002238d0d4a90, 0x000002238d0d36e0, 0x000002238d0d1610,
0x000002238d0ce820, 0x000002238d0d2330, 0x000002238d0d29c0, 0x000002238d0ce190,
0x000002238d0cdb00, 0x000002238d0d5120, 0x000002238d0d3050, 0x000002238d0d3d70,
0x000002238d0d08f0, 0x000002238d0d0f80, 0x000002238d0d0260, 0x000002238f2aa130,
0x000002238f2abb70, 0x00000223916cc930, 0x00000223917d59d0, 0x00000223925d2c20
}

Java Threads: ( => current thread )
  0x00000223ecf1a230 JavaThread "main"                              [_thread_blocked, id=32496, stack(0x000000b5dfa00000,0x000000b5dfb00000) (1024K)]
  0x0000022384c794c0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=40356, stack(0x000000b5e0200000,0x000000b5e0300000) (1024K)]
  0x0000022384c79ee0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=21584, stack(0x000000b5e0300000,0x000000b5e0400000) (1024K)]
  0x0000022384cae220 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=2788, stack(0x000000b5e0400000,0x000000b5e0500000) (1024K)]
  0x0000022384caef70 JavaThread "Attach Listener"            daemon [_thread_blocked, id=56112, stack(0x000000b5e0500000,0x000000b5e0600000) (1024K)]
  0x0000022384cd1ef0 JavaThread "Service Thread"             daemon [_thread_blocked, id=33808, stack(0x000000b5e0600000,0x000000b5e0700000) (1024K)]
  0x0000022384cd36b0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=31172, stack(0x000000b5e0700000,0x000000b5e0800000) (1024K)]
  0x0000022384cb00f0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=62368, stack(0x000000b5e0800000,0x000000b5e0900000) (1024K)]
  0x0000022384cb3790 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=26572, stack(0x000000b5e0900000,0x000000b5e0a00000) (1024K)]
  0x0000022384cd5780 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=24764, stack(0x000000b5e0a00000,0x000000b5e0b00000) (1024K)]
  0x0000022384cd3d40 JavaThread "Notification Thread"        daemon [_thread_blocked, id=12088, stack(0x000000b5e0b00000,0x000000b5e0c00000) (1024K)]
  0x0000022384cd43d0 JavaThread "Daemon health stats"               [_thread_blocked, id=34340, stack(0x000000b5e1600000,0x000000b5e1700000) (1024K)]
  0x0000022384cd50f0 JavaThread "Incoming local TCP Connector on port 7460"        [_thread_in_native, id=23980, stack(0x000000b5e1700000,0x000000b5e1800000) (1024K)]
  0x0000022384cd5e10 JavaThread "Daemon periodic checks"            [_thread_blocked, id=32600, stack(0x000000b5e1800000,0x000000b5e1900000) (1024K)]
  0x0000022384cd2990 JavaThread "Daemon"                            [_thread_blocked, id=21576, stack(0x000000b5e1900000,0x000000b5e1a00000) (1024K)]
  0x0000022384cd3020 JavaThread "Handler for socket connection from /127.0.0.1:7460 to /127.0.0.1:7461"        [_thread_in_native, id=42940, stack(0x000000b5e1a00000,0x000000b5e1b00000) (1024K)]
  0x0000022384cd4a60 JavaThread "Cancel handler"                    [_thread_blocked, id=17976, stack(0x000000b5e1b00000,0x000000b5e1c00000) (1024K)]
  0x000002238b655170 JavaThread "Daemon worker"                     [_thread_blocked, id=31220, stack(0x000000b5e1c00000,0x000000b5e1d00000) (1024K)]
  0x000002238b657f60 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:7460 to /127.0.0.1:7461"        [_thread_blocked, id=24596, stack(0x000000b5e1d00000,0x000000b5e1e00000) (1024K)]
  0x000002238b651660 JavaThread "Stdin handler"                     [_thread_blocked, id=20772, stack(0x000000b5e1e00000,0x000000b5e1f00000) (1024K)]
  0x000002238b650fd0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=15392, stack(0x000000b5e1f00000,0x000000b5e2000000) (1024K)]
  0x000002238b6530a0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=50492, stack(0x000000b5e2200000,0x000000b5e2300000) (1024K)]
  0x000002238b656bb0 JavaThread "File lock request listener"        [_thread_in_native, id=33356, stack(0x000000b5e2300000,0x000000b5e2400000) (1024K)]
  0x000002238b656520 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.9\fileHashes)"        [_thread_blocked, id=43880, stack(0x000000b5e2400000,0x000000b5e2500000) (1024K)]
  0x000002238b652380 JavaThread "Cache worker for file hash cache (D:\Workspace\RiderService_SDK\.gradle\8.9\fileHashes)"        [_thread_blocked, id=68748, stack(0x000000b5e2600000,0x000000b5e2700000) (1024K)]
  0x000002238b653730 JavaThread "Cache worker for Build Output Cleanup Cache (D:\Workspace\RiderService_SDK\.gradle\buildOutputCleanup)"        [_thread_blocked, id=7424, stack(0x000000b5e2700000,0x000000b5e2800000) (1024K)]
  0x000002238b652a10 JavaThread "File watcher server"        daemon [_thread_in_native, id=64400, stack(0x000000b5e2800000,0x000000b5e2900000) (1024K)]
  0x000002238b655800 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=30328, stack(0x000000b5e2900000,0x000000b5e2a00000) (1024K)]
  0x000002238b651cf0 JavaThread "Cache worker for checksums cache (D:\Workspace\RiderService_SDK\.gradle\8.9\checksums)"        [_thread_blocked, id=39152, stack(0x000000b5e2a00000,0x000000b5e2b00000) (1024K)]
  0x000002238b653dc0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.9\fileContent)"        [_thread_blocked, id=34136, stack(0x000000b5e2b00000,0x000000b5e2c00000) (1024K)]
  0x000002238b6578d0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.9\md-rule)"        [_thread_blocked, id=19440, stack(0x000000b5e2c00000,0x000000b5e2d00000) (1024K)]
  0x000002238b654450 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.9\md-supplier)"        [_thread_blocked, id=17204, stack(0x000000b5e2d00000,0x000000b5e2e00000) (1024K)]
  0x000002238b654ae0 JavaThread "jar transforms"                    [_thread_blocked, id=17984, stack(0x000000b5e2e00000,0x000000b5e2f00000) (1024K)]
  0x000002238b657240 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=63868, stack(0x000000b5e2f00000,0x000000b5e3000000) (1024K)]
  0x000002238d6e3850 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=10272, stack(0x000000b5e3000000,0x000000b5e3100000) (1024K)]
  0x000002238d6e5920 JavaThread "Unconstrained build operations"        [_thread_blocked, id=35272, stack(0x000000b5e3100000,0x000000b5e3200000) (1024K)]
  0x000002238d6ea7e0 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=2392, stack(0x000000b5e3200000,0x000000b5e3300000) (1024K)]
  0x000002238d6e6640 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=21804, stack(0x000000b5e3300000,0x000000b5e3400000) (1024K)]
  0x000002238d6e5fb0 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=53376, stack(0x000000b5e2500000,0x000000b5e2600000) (1024K)]
  0x000002238d6e7360 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=52748, stack(0x000000b5e3400000,0x000000b5e3500000) (1024K)]
  0x000002238d6e8da0 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=44312, stack(0x000000b5e3500000,0x000000b5e3600000) (1024K)]
  0x000002238d6e9430 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=12500, stack(0x000000b5e3600000,0x000000b5e3700000) (1024K)]
  0x000002238d6e9ac0 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=61004, stack(0x000000b5e3700000,0x000000b5e3800000) (1024K)]
  0x000002238d6eae70 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=18220, stack(0x000000b5e3800000,0x000000b5e3900000) (1024K)]
  0x000002238d6e4570 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=65964, stack(0x000000b5e3900000,0x000000b5e3a00000) (1024K)]
  0x000002238d6e4c00 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=39324, stack(0x000000b5e3a00000,0x000000b5e3b00000) (1024K)]
  0x000002238bf90cc0 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=25144, stack(0x000000b5e3b00000,0x000000b5e3c00000) (1024K)]
  0x000002238bf94e60 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=48312, stack(0x000000b5e3c00000,0x000000b5e3d00000) (1024K)]
  0x000002238bf8ffa0 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=49668, stack(0x000000b5e3d00000,0x000000b5e3e00000) (1024K)]
  0x000002238bf91350 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=63832, stack(0x000000b5e3e00000,0x000000b5e3f00000) (1024K)]
  0x000002238bf947d0 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=54488, stack(0x000000b5e3f00000,0x000000b5e4000000) (1024K)]
  0x000002238bf90630 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=56044, stack(0x000000b5e4000000,0x000000b5e4100000) (1024K)]
  0x000002238bf8ded0 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=42088, stack(0x000000b5e4100000,0x000000b5e4200000) (1024K)]
  0x000002238bf94140 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=32624, stack(0x000000b5e4200000,0x000000b5e4300000) (1024K)]
  0x000002238bf919e0 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=70604, stack(0x000000b5e4300000,0x000000b5e4400000) (1024K)]
  0x000002238bf8e560 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=62576, stack(0x000000b5e4400000,0x000000b5e4500000) (1024K)]
  0x000002238bf8f280 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=43580, stack(0x000000b5e4500000,0x000000b5e4600000) (1024K)]
  0x000002238bf954f0 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=69936, stack(0x000000b5e4600000,0x000000b5e4700000) (1024K)]
  0x000002238bf92700 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=40604, stack(0x000000b5e4700000,0x000000b5e4800000) (1024K)]
  0x000002238bf92070 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=65872, stack(0x000000b5e4800000,0x000000b5e4900000) (1024K)]
  0x000002238bf92d90 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=30856, stack(0x000000b5e4a00000,0x000000b5e4b00000) (1024K)]
  0x000002238bf8ebf0 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=67724, stack(0x000000b5e4b00000,0x000000b5e4c00000) (1024K)]
  0x000002238bf93420 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=65176, stack(0x000000b5e4c00000,0x000000b5e4d00000) (1024K)]
  0x000002238bf93ab0 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=22820, stack(0x000000b5e4d00000,0x000000b5e4e00000) (1024K)]
  0x000002238bf8f910 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=26352, stack(0x000000b5e4e00000,0x000000b5e4f00000) (1024K)]
  0x000002238f2b9430 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=32188, stack(0x000000b5e4f00000,0x000000b5e5000000) (1024K)]
  0x000002238f2bae70 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=23232, stack(0x000000b5e5000000,0x000000b5e5100000) (1024K)]
  0x000002238f2b8da0 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=15568, stack(0x000000b5e5100000,0x000000b5e5200000) (1024K)]
  0x000002238f2bbb90 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=28316, stack(0x000000b5e5200000,0x000000b5e5300000) (1024K)]
  0x000002238f2bc220 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=52028, stack(0x000000b5e5300000,0x000000b5e5400000) (1024K)]
  0x000002238f2bb500 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=26100, stack(0x000000b5e5400000,0x000000b5e5500000) (1024K)]
  0x000002238f2ba7e0 JavaThread "build event listener"              [_thread_blocked, id=28712, stack(0x000000b5e4900000,0x000000b5e4a00000) (1024K)]
  0x000002238f2b6640 JavaThread "Memory manager"                    [_thread_blocked, id=64540, stack(0x000000b5e5500000,0x000000b5e5600000) (1024K)]
  0x000002238f2aa7c0 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=48796, stack(0x000000b5e5700000,0x000000b5e5800000) (1024K)]
  0x000002238f2aae50 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=25584, stack(0x000000b5e5800000,0x000000b5e5900000) (1024K)]
  0x000002238f2ab4e0 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=39476, stack(0x000000b5e5900000,0x000000b5e5a00000) (1024K)]
  0x000002238c9f2110 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=36528, stack(0x000000b5e5a00000,0x000000b5e5b00000) (1024K)]
  0x000002238c9ef320 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=16100, stack(0x000000b5e5b00000,0x000000b5e5c00000) (1024K)]
  0x000002238c9f06d0 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=41500, stack(0x000000b5e5c00000,0x000000b5e5d00000) (1024K)]
  0x000002238c9f0d60 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=7548, stack(0x000000b5e5d00000,0x000000b5e5e00000) (1024K)]
  0x000002238c9ee600 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=62140, stack(0x000000b5e5e00000,0x000000b5e5f00000) (1024K)]
  0x000002238c9ef9b0 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=65148, stack(0x000000b5e5f00000,0x000000b5e6000000) (1024K)]
  0x000002238c9f13f0 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=22868, stack(0x000000b5e6000000,0x000000b5e6100000) (1024K)]
  0x000002238c9f1a80 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=44212, stack(0x000000b5e6100000,0x000000b5e6200000) (1024K)]
  0x00000223917096d0 JavaThread "included builds"                   [_thread_blocked, id=61196, stack(0x000000b5e6200000,0x000000b5e6300000) (1024K)]
  0x0000022391707600 JavaThread "Execution worker"                  [_thread_blocked, id=29552, stack(0x000000b5e6300000,0x000000b5e6400000) (1024K)]
  0x000002239170b110 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=67304, stack(0x000000b5e6400000,0x000000b5e6500000) (1024K)]
  0x000002239170a3f0 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=68164, stack(0x000000b5e6500000,0x000000b5e6600000) (1024K)]
  0x000002239170be30 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=42280, stack(0x000000b5e6600000,0x000000b5e6700000) (1024K)]
  0x000002239170c4c0 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=64252, stack(0x000000b5e6700000,0x000000b5e6800000) (1024K)]
  0x000002239170cb50 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=10144, stack(0x000000b5e6800000,0x000000b5e6900000) (1024K)]
  0x0000022391f5c4a0 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=48820, stack(0x000000b5e6900000,0x000000b5e6a00000) (1024K)]
  0x0000022391f5a3d0 JavaThread "Execution worker Thread 8"         [_thread_blocked, id=36496, stack(0x000000b5e6a00000,0x000000b5e6b00000) (1024K)]
  0x0000022391f59020 JavaThread "Execution worker Thread 9"         [_thread_blocked, id=14784, stack(0x000000b5e6b00000,0x000000b5e6c00000) (1024K)]
  0x0000022391f59d40 JavaThread "Execution worker Thread 10"        [_thread_blocked, id=47676, stack(0x000000b5e6c00000,0x000000b5e6d00000) (1024K)]
  0x0000022391f5b0f0 JavaThread "Execution worker Thread 11"        [_thread_blocked, id=4560, stack(0x000000b5e6d00000,0x000000b5e6e00000) (1024K)]
  0x0000022391f5aa60 JavaThread "Cache worker for execution history cache (D:\Workspace\RiderService_SDK\.gradle\8.9\executionHistory)"        [_thread_blocked, id=51660, stack(0x000000b5e6e00000,0x000000b5e6f00000) (1024K)]
  0x0000022391f5be10 JavaThread "Exec process"                      [_thread_blocked, id=70636, stack(0x000000b5e6f00000,0x000000b5e7000000) (1024K)]
  0x0000022391f5cb30 JavaThread "Exec process Thread 2"             [_thread_blocked, id=13588, stack(0x000000b5e0c00000,0x000000b5e0d00000) (1024K)]
  0x0000022391f5e570 JavaThread "Exec process Thread 3"             [_thread_blocked, id=61152, stack(0x000000b5e5600000,0x000000b5e5700000) (1024K)]
  0x0000022391f5ffb0 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=54540, stack(0x000000b5e7100000,0x000000b5e7200000) (1024K)]
  0x0000022391f596b0 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=68372, stack(0x000000b5e7300000,0x000000b5e7400000) (1024K)]
  0x0000022391950910 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=63468, stack(0x000000b5e7400000,0x000000b5e7500000) (1024K)]
  0x0000022391953070 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=28016, stack(0x000000b5e7500000,0x000000b5e7600000) (1024K)]
  0x0000022391952350 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=22940, stack(0x000000b5e7600000,0x000000b5e7700000) (1024K)]
  0x00000223919557d0 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=67300, stack(0x000000b5e7700000,0x000000b5e7800000) (1024K)]
  0x00000223919529e0 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=25272, stack(0x000000b5e7800000,0x000000b5e7900000) (1024K)]
  0x0000022391955140 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=45280, stack(0x000000b5e7900000,0x000000b5e7a00000) (1024K)]
  0x0000022391950fa0 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=61404, stack(0x000000b5e7a00000,0x000000b5e7b00000) (1024K)]
  0x0000022391950280 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=69660, stack(0x000000b5e7b00000,0x000000b5e7c00000) (1024K)]
  0x0000022391953700 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=61512, stack(0x000000b5e7c00000,0x000000b5e7d00000) (1024K)]
  0x0000022391953d90 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=66620, stack(0x000000b5e7d00000,0x000000b5e7e00000) (1024K)]
  0x0000022391954420 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=33160, stack(0x000000b5e7e00000,0x000000b5e7f00000) (1024K)]
  0x0000022391954ab0 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=62528, stack(0x000000b5e7f00000,0x000000b5e8000000) (1024K)]
  0x000002239194eed0 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=69032, stack(0x000000b5e7200000,0x000000b5e7300000) (1024K)]
  0x0000022391951cc0 JavaThread "RMI Scheduler(0)"           daemon [_thread_blocked, id=22316, stack(0x000000b5e7000000,0x000000b5e7100000) (1024K)]
  0x000002239194f560 JavaThread "RMI GC Daemon"              daemon [_thread_blocked, id=28168, stack(0x000000b5e8100000,0x000000b5e8200000) (1024K)]
  0x0000022391956b80 JavaThread "RMI TCP Accept-0"           daemon [_thread_in_native, id=56824, stack(0x000000b5e8200000,0x000000b5e8300000) (1024K)]
  0x000002239194fbf0 JavaThread "RMI Reaper"                        [_thread_blocked, id=18804, stack(0x000000b5e8300000,0x000000b5e8400000) (1024K)]
  0x000002239195b3b0 JavaThread "RMI TCP Connection(1)-127.0.0.1" daemon [_thread_in_native, id=64792, stack(0x000000b5e8400000,0x000000b5e8500000) (1024K)]
=>0x00000223919578a0 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_in_vm, id=22572, stack(0x000000b5e8500000,0x000000b5e8600000) (1024K)]
  0x000002239195db10 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=62860, stack(0x000000b5e8800000,0x000000b5e8900000) (1024K)]
  0x00000223919585c0 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=57920, stack(0x000000b5e8900000,0x000000b5e8a00000) (1024K)]
  0x000002239195cdf0 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=14464, stack(0x000000b5e8a00000,0x000000b5e8b00000) (1024K)]
  0x0000022391958c50 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=68140, stack(0x000000b5e8b00000,0x000000b5e8c00000) (1024K)]
  0x000002239195c760 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=39044, stack(0x000000b5e8c00000,0x000000b5e8d00000) (1024K)]
  0x000002239195a690 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=61444, stack(0x000000b5e8d00000,0x000000b5e8e00000) (1024K)]
  0x0000022391959970 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=15712, stack(0x000000b5e8e00000,0x000000b5e8f00000) (1024K)]
  0x00000223919592e0 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=38424, stack(0x000000b5e8f00000,0x000000b5e9000000) (1024K)]
  0x000002239195a000 JavaThread "RMI TCP Connection(2)-127.0.0.1" daemon [_thread_in_native, id=67144, stack(0x000000b5e9000000,0x000000b5e9100000) (1024K)]
  0x000002239195ba40 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=31816, stack(0x000000b5e9100000,0x000000b5e9200000) (1024K)]
  0x000002239195ad20 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=52684, stack(0x000000b5e9300000,0x000000b5e9400000) (1024K)]
  0x000002239195c0d0 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=36040, stack(0x000000b5e9400000,0x000000b5e9500000) (1024K)]
  0x000002239195d480 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=4052, stack(0x000000b5e9500000,0x000000b5e9600000) (1024K)]
  0x00000223917089b0 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=26768, stack(0x000000b5e9600000,0x000000b5e9700000) (1024K)]
  0x000002239170aa80 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=49812, stack(0x000000b5e9700000,0x000000b5e9800000) (1024K)]
  0x0000022391709d60 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=30520, stack(0x000000b5e9800000,0x000000b5e9900000) (1024K)]
  0x00000223917068e0 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=10492, stack(0x000000b5e9900000,0x000000b5e9a00000) (1024K)]
  0x000002239170b7a0 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=23220, stack(0x000000b5e9a00000,0x000000b5e9b00000) (1024K)]
  0x0000022391707c90 JavaThread "pool-3-thread-1"                   [_thread_blocked, id=11480, stack(0x000000b5e8600000,0x000000b5e8700000) (1024K)]
  0x000002239170d1e0 JavaThread "stderr"                            [_thread_in_native, id=20448, stack(0x000000b5e9200000,0x000000b5e9300000) (1024K)]
  0x0000022391706250 JavaThread "stdout"                            [_thread_in_native, id=2552, stack(0x000000b5e9b00000,0x000000b5e9c00000) (1024K)]
  0x0000022391708320 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=10232, stack(0x000000b5e9d00000,0x000000b5e9e00000) (1024K)]
  0x000002238c9f0040 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=66628, stack(0x000000b5e9e00000,0x000000b5e9f00000) (1024K)]
  0x000002238c9f3b50 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=24404, stack(0x000000b5e9f00000,0x000000b5ea000000) (1024K)]
  0x000002238c9ecbc0 JavaThread "Unconstrained build operations Thread 81"        [_thread_blocked, id=2668, stack(0x000000b5ea000000,0x000000b5ea100000) (1024K)]
  0x000002238c9f41e0 JavaThread "Unconstrained build operations Thread 82"        [_thread_blocked, id=37360, stack(0x000000b5ea100000,0x000000b5ea200000) (1024K)]
  0x000002238c9ed250 JavaThread "Unconstrained build operations Thread 83"        [_thread_blocked, id=7832, stack(0x000000b5ea200000,0x000000b5ea300000) (1024K)]
  0x000002238c9ed8e0 JavaThread "Unconstrained build operations Thread 84"        [_thread_blocked, id=58908, stack(0x000000b5ea300000,0x000000b5ea400000) (1024K)]
  0x000002238c9edf70 JavaThread "Unconstrained build operations Thread 85"        [_thread_blocked, id=23408, stack(0x000000b5ea400000,0x000000b5ea500000) (1024K)]
  0x000002238c9eec90 JavaThread "Unconstrained build operations Thread 86"        [_thread_blocked, id=24476, stack(0x000000b5ea500000,0x000000b5ea600000) (1024K)]
  0x000002238d0cfbd0 JavaThread "Unconstrained build operations Thread 87"        [_thread_blocked, id=16272, stack(0x000000b5ea600000,0x000000b5ea700000) (1024K)]
  0x000002238d0d1ca0 JavaThread "Unconstrained build operations Thread 88"        [_thread_blocked, id=18588, stack(0x000000b5ea700000,0x000000b5ea800000) (1024K)]
  0x000002238d0d4a90 JavaThread "WorkerExecutor Queue Thread 3"        [_thread_blocked, id=37284, stack(0x000000b5ea800000,0x000000b5ea900000) (1024K)]
  0x000002238d0d36e0 JavaThread "WorkerExecutor Queue Thread 4"        [_thread_blocked, id=29784, stack(0x000000b5ea900000,0x000000b5eaa00000) (1024K)]
  0x000002238d0d1610 JavaThread "Cache worker for Java compile cache (C:\Users\<USER>\.gradle\caches\8.9\javaCompile)"        [_thread_blocked, id=56600, stack(0x000000b5eaa00000,0x000000b5eab00000) (1024K)]
  0x000002238d0ce820 JavaThread "Build operations"                  [_thread_blocked, id=70024, stack(0x000000b5eac00000,0x000000b5ead00000) (1024K)]
  0x000002238d0d2330 JavaThread "Build operations Thread 2"         [_thread_blocked, id=7008, stack(0x000000b5ead00000,0x000000b5eae00000) (1024K)]
  0x000002238d0d29c0 JavaThread "Build operations Thread 3"         [_thread_blocked, id=66440, stack(0x000000b5eae00000,0x000000b5eaf00000) (1024K)]
  0x000002238d0ce190 JavaThread "Build operations Thread 4"         [_thread_blocked, id=37968, stack(0x000000b5eaf00000,0x000000b5eb000000) (1024K)]
  0x000002238d0cdb00 JavaThread "Build operations Thread 5"         [_thread_blocked, id=57940, stack(0x000000b5eb000000,0x000000b5eb100000) (1024K)]
  0x000002238d0d5120 JavaThread "Build operations Thread 6"         [_thread_blocked, id=62636, stack(0x000000b5eb100000,0x000000b5eb200000) (1024K)]
  0x000002238d0d3050 JavaThread "Build operations Thread 7"         [_thread_blocked, id=21884, stack(0x000000b5eb200000,0x000000b5eb300000) (1024K)]
  0x000002238d0d3d70 JavaThread "Build operations Thread 8"         [_thread_blocked, id=57856, stack(0x000000b5eb300000,0x000000b5eb400000) (1024K)]
  0x000002238d0d08f0 JavaThread "Build operations Thread 9"         [_thread_blocked, id=68284, stack(0x000000b5eb400000,0x000000b5eb500000) (1024K)]
  0x000002238d0d0f80 JavaThread "Build operations Thread 10"        [_thread_blocked, id=70620, stack(0x000000b5eb500000,0x000000b5eb600000) (1024K)]
  0x000002238d0d0260 JavaThread "Build operations Thread 11"        [_thread_blocked, id=35012, stack(0x000000b5eb600000,0x000000b5eb700000) (1024K)]
  0x000002238f2aa130 JavaThread "WorkerExecutor Queue Thread 5"        [_thread_blocked, id=66708, stack(0x000000b5e8000000,0x000000b5e8100000) (1024K)]
  0x000002238f2abb70 JavaThread "Build operations Thread 12"        [_thread_blocked, id=60324, stack(0x000000b5e9c00000,0x000000b5e9d00000) (1024K)]
  0x00000223916cc930 JavaThread "WorkerExecutor Queue Thread 6"        [_thread_blocked, id=16488, stack(0x000000b5eb700000,0x000000b5eb800000) (1024K)]
  0x00000223917d59d0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=55792, stack(0x000000b5e8700000,0x000000b5e8800000) (1024K)]
  0x00000223925d2c20 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=5620, stack(0x000000b5eb900000,0x000000b5eba00000) (1024K)]
Total: 172

Other Threads:
  0x0000022384beb8b0 VMThread "VM Thread"                           [id=59712, stack(0x000000b5e0100000,0x000000b5e0200000) (1024K)]
  0x0000022384ba2920 WatcherThread "VM Periodic Task Thread"        [id=25568, stack(0x000000b5e0000000,0x000000b5e0100000) (1024K)]
  0x00000223ecf6f190 WorkerThread "GC Thread#0"                     [id=65720, stack(0x000000b5dfb00000,0x000000b5dfc00000) (1024K)]
  0x0000022389cf13c0 WorkerThread "GC Thread#1"                     [id=31216, stack(0x000000b5e0d00000,0x000000b5e0e00000) (1024K)]
  0x0000022389cf1760 WorkerThread "GC Thread#2"                     [id=61184, stack(0x000000b5e0e00000,0x000000b5e0f00000) (1024K)]
  0x0000022389cf1b00 WorkerThread "GC Thread#3"                     [id=64072, stack(0x000000b5e0f00000,0x000000b5e1000000) (1024K)]
  0x00000223899140b0 WorkerThread "GC Thread#4"                     [id=26280, stack(0x000000b5e1000000,0x000000b5e1100000) (1024K)]
  0x0000022389914450 WorkerThread "GC Thread#5"                     [id=53108, stack(0x000000b5e1100000,0x000000b5e1200000) (1024K)]
  0x00000223899147f0 WorkerThread "GC Thread#6"                     [id=45468, stack(0x000000b5e1200000,0x000000b5e1300000) (1024K)]
  0x0000022389914b90 WorkerThread "GC Thread#7"                     [id=30624, stack(0x000000b5e1300000,0x000000b5e1400000) (1024K)]
  0x0000022389914f30 WorkerThread "GC Thread#8"                     [id=38036, stack(0x000000b5e1400000,0x000000b5e1500000) (1024K)]
  0x000002238982b500 WorkerThread "GC Thread#9"                     [id=30192, stack(0x000000b5e1500000,0x000000b5e1600000) (1024K)]
  0x00000223e827bfe0 ConcurrentGCThread "G1 Main Marker"            [id=31548, stack(0x000000b5dfc00000,0x000000b5dfd00000) (1024K)]
  0x00000223e827d190 WorkerThread "G1 Conc#0"                       [id=55876, stack(0x000000b5dfd00000,0x000000b5dfe00000) (1024K)]
  0x000002238b795ec0 WorkerThread "G1 Conc#1"                       [id=43444, stack(0x000000b5e2000000,0x000000b5e2100000) (1024K)]
  0x000002238b796260 WorkerThread "G1 Conc#2"                       [id=22852, stack(0x000000b5e2100000,0x000000b5e2200000) (1024K)]
  0x0000022384a78050 ConcurrentGCThread "G1 Refine#0"               [id=67528, stack(0x000000b5dfe00000,0x000000b5dff00000) (1024K)]
  0x0000022384a79ae0 ConcurrentGCThread "G1 Service"                [id=51728, stack(0x000000b5dff00000,0x000000b5e0000000) (1024K)]
Total: 18

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffda85ac1a0] Metaspace_lock - owner thread: 0x00000223919578a0

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x0000000140000000, reserved size: 1073741824
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x140000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 12 total, 12 available
 Memory: 32660M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 318464K, used 229511K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 59 young (60416K), 16 survivors (16384K)
 Metaspace       used 153869K, committed 156352K, reserved 1245184K
  class space    used 20421K, committed 21632K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080000000| PB 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080100000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080200000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080300000| PB 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080400000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080600000| PB 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080700000| PB 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080800000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%|HS|  |TAMS 0x0000000080900000| PB 0x0000000080900000| Complete 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%|HS|  |TAMS 0x0000000080b00000| PB 0x0000000080b00000| Complete 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081300000| PB 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081600000| PB 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081700000| PB 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081800000| PB 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081900000| PB 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081a00000| PB 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081b00000| PB 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%|HS|  |TAMS 0x0000000081c00000| PB 0x0000000081c00000| Complete 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081d00000| PB 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081e00000| PB 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000081f00000| PB 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082000000| PB 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082100000| PB 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082200000| PB 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082300000| PB 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082400000| PB 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082500000| PB 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082600000| PB 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082700000| PB 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082800000| PB 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082900000| PB 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082a00000| PB 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082b00000| PB 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082c00000| PB 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082d00000| PB 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082e00000| PB 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000082f00000| PB 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083000000| PB 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%| O|  |TAMS 0x0000000083100000| PB 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%| O|  |TAMS 0x0000000083200000| PB 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083300000| PB 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083400000| PB 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%|HS|  |TAMS 0x0000000083500000| PB 0x0000000083500000| Complete 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%|HS|  |TAMS 0x0000000083600000| PB 0x0000000083600000| Complete 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%|HC|  |TAMS 0x0000000083700000| PB 0x0000000083700000| Complete 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%|HS|  |TAMS 0x0000000083800000| PB 0x0000000083800000| Complete 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%|HS|  |TAMS 0x0000000083900000| PB 0x0000000083900000| Complete 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%|HC|  |TAMS 0x0000000083a00000| PB 0x0000000083a00000| Complete 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%|HC|  |TAMS 0x0000000083b00000| PB 0x0000000083b00000| Complete 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%|HC|  |TAMS 0x0000000083c00000| PB 0x0000000083c00000| Complete 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083d00000| PB 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083e00000| PB 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000083f00000| PB 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%|HS|  |TAMS 0x0000000084000000| PB 0x0000000084000000| Complete 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%|HS|  |TAMS 0x0000000084100000| PB 0x0000000084100000| Complete 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084200000| PB 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%|HS|  |TAMS 0x0000000084300000| PB 0x0000000084300000| Complete 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%|HC|  |TAMS 0x0000000084400000| PB 0x0000000084400000| Complete 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%|HS|  |TAMS 0x0000000084500000| PB 0x0000000084500000| Complete 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%|HS|  |TAMS 0x0000000084600000| PB 0x0000000084600000| Complete 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%|HC|  |TAMS 0x0000000084700000| PB 0x0000000084700000| Complete 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%|HC|  |TAMS 0x0000000084800000| PB 0x0000000084800000| Complete 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%|HC|  |TAMS 0x0000000084900000| PB 0x0000000084900000| Complete 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084a00000| PB 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084b00000| PB 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084c00000| PB 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084d00000| PB 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000084f00000| PB 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085800000| PB 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086600000| PB 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%|HS|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Complete 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%|HC|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Complete 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%|HC|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Complete 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087000000| PB 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087100000| PB 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087200000| PB 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087300000| PB 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|  |TAMS 0x0000000087400000| PB 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087500000| PB 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087700000, 0x0000000087800000|  0%| F|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087800000, 0x0000000087900000|  0%| F|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087900000| PB 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%|HS|  |TAMS 0x0000000087a00000| PB 0x0000000087a00000| Complete 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%|HC|  |TAMS 0x0000000087b00000| PB 0x0000000087b00000| Complete 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%|HC|  |TAMS 0x0000000087c00000| PB 0x0000000087c00000| Complete 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%|HC|  |TAMS 0x0000000087d00000| PB 0x0000000087d00000| Complete 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087e00000| PB 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000087f00000| PB 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| O|  |TAMS 0x0000000088000000| PB 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088100000| PB 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| O|  |TAMS 0x0000000088200000| PB 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| O|  |TAMS 0x0000000088300000| PB 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| O|  |TAMS 0x0000000088400000| PB 0x0000000088400000| Untracked 
| 133|0x0000000088500000, 0x0000000088500000, 0x0000000088600000|  0%| F|  |TAMS 0x0000000088500000| PB 0x0000000088500000| Untracked 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| O|  |TAMS 0x0000000088600000| PB 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%| O|  |TAMS 0x0000000088700000| PB 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088800000, 0x0000000088900000|  0%| F|  |TAMS 0x0000000088800000| PB 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088900000| PB 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088a00000| PB 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088b00000| PB 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088c00000| PB 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| O|  |TAMS 0x0000000088d00000| PB 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%|HS|  |TAMS 0x0000000088e00000| PB 0x0000000088e00000| Complete 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%|HC|  |TAMS 0x0000000088f00000| PB 0x0000000088f00000| Complete 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| O|  |TAMS 0x0000000089000000| PB 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089100000| PB 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089200000| PB 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| O|  |TAMS 0x0000000089300000| PB 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| O|  |TAMS 0x0000000089400000| PB 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%| O|  |TAMS 0x0000000089500000| PB 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| O|  |TAMS 0x0000000089600000| PB 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%| O|  |TAMS 0x0000000089700000| PB 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089800000| PB 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089900000, 0x0000000089a00000|  0%| F|  |TAMS 0x0000000089900000| PB 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089a00000, 0x0000000089b00000|  0%| F|  |TAMS 0x0000000089a00000| PB 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089b00000| PB 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089c00000| PB 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| O|  |TAMS 0x0000000089d00000| PB 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089e00000| PB 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| O|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| O|  |TAMS 0x000000008a000000| PB 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%| O|  |TAMS 0x000000008a100000| PB 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%| O|  |TAMS 0x000000008a200000| PB 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%| O|  |TAMS 0x000000008a300000| PB 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a400000, 0x000000008a500000|  0%| F|  |TAMS 0x000000008a400000| PB 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a500000, 0x000000008a600000|  0%| F|  |TAMS 0x000000008a500000| PB 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a600000, 0x000000008a700000|  0%| F|  |TAMS 0x000000008a600000| PB 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a700000, 0x000000008a800000|  0%| F|  |TAMS 0x000000008a700000| PB 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a800000, 0x000000008a900000|  0%| F|  |TAMS 0x000000008a800000| PB 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008a900000, 0x000000008aa00000|  0%| F|  |TAMS 0x000000008a900000| PB 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008aa00000, 0x000000008ab00000|  0%| F|  |TAMS 0x000000008aa00000| PB 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ab00000, 0x000000008ac00000|  0%| F|  |TAMS 0x000000008ab00000| PB 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ac00000, 0x000000008ad00000|  0%| F|  |TAMS 0x000000008ac00000| PB 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ad00000, 0x000000008ae00000|  0%| F|  |TAMS 0x000000008ad00000| PB 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008ae00000, 0x000000008af00000|  0%| F|  |TAMS 0x000000008ae00000| PB 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008af00000, 0x000000008b000000|  0%| F|  |TAMS 0x000000008af00000| PB 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b000000, 0x000000008b100000|  0%| F|  |TAMS 0x000000008b000000| PB 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b100000, 0x000000008b200000|  0%| F|  |TAMS 0x000000008b100000| PB 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%| O|  |TAMS 0x000000008b200000| PB 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%| S|CS|TAMS 0x000000008b300000| PB 0x000000008b300000| Complete 
| 180|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%| S|CS|TAMS 0x000000008b400000| PB 0x000000008b400000| Complete 
| 181|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%| S|CS|TAMS 0x000000008b500000| PB 0x000000008b500000| Complete 
| 182|0x000000008b600000, 0x000000008b700000, 0x000000008b700000|100%| S|CS|TAMS 0x000000008b600000| PB 0x000000008b600000| Complete 
| 183|0x000000008b700000, 0x000000008b800000, 0x000000008b800000|100%| S|CS|TAMS 0x000000008b700000| PB 0x000000008b700000| Complete 
| 184|0x000000008b800000, 0x000000008b900000, 0x000000008b900000|100%| S|CS|TAMS 0x000000008b800000| PB 0x000000008b800000| Complete 
| 185|0x000000008b900000, 0x000000008ba00000, 0x000000008ba00000|100%| S|CS|TAMS 0x000000008b900000| PB 0x000000008b900000| Complete 
| 186|0x000000008ba00000, 0x000000008bb00000, 0x000000008bb00000|100%| S|CS|TAMS 0x000000008ba00000| PB 0x000000008ba00000| Complete 
| 187|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%| S|CS|TAMS 0x000000008bb00000| PB 0x000000008bb00000| Complete 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%| S|CS|TAMS 0x000000008bc00000| PB 0x000000008bc00000| Complete 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| S|CS|TAMS 0x000000008bd00000| PB 0x000000008bd00000| Complete 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| S|CS|TAMS 0x000000008be00000| PB 0x000000008be00000| Complete 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| S|CS|TAMS 0x000000008bf00000| PB 0x000000008bf00000| Complete 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%| S|CS|TAMS 0x000000008c000000| PB 0x000000008c000000| Complete 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%| S|CS|TAMS 0x000000008c100000| PB 0x000000008c100000| Complete 
| 194|0x000000008c200000, 0x000000008c200000, 0x000000008c300000|  0%| F|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c300000, 0x000000008c400000|  0%| F|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c400000, 0x000000008c500000|  0%| F|  |TAMS 0x000000008c400000| PB 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c500000, 0x000000008c600000|  0%| F|  |TAMS 0x000000008c500000| PB 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c600000, 0x000000008c700000|  0%| F|  |TAMS 0x000000008c600000| PB 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c700000, 0x000000008c800000|  0%| F|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c800000, 0x000000008c900000|  0%| F|  |TAMS 0x000000008c800000| PB 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008c900000, 0x000000008ca00000|  0%| F|  |TAMS 0x000000008c900000| PB 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008ca00000, 0x000000008cb00000|  0%| F|  |TAMS 0x000000008ca00000| PB 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cb00000, 0x000000008cc00000|  0%| F|  |TAMS 0x000000008cb00000| PB 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cc00000, 0x000000008cd00000|  0%| F|  |TAMS 0x000000008cc00000| PB 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008cd00000, 0x000000008ce00000|  0%| F|  |TAMS 0x000000008cd00000| PB 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008ce00000, 0x000000008cf00000|  0%| F|  |TAMS 0x000000008ce00000| PB 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008cf00000, 0x000000008d000000|  0%| F|  |TAMS 0x000000008cf00000| PB 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d000000, 0x000000008d100000|  0%| F|  |TAMS 0x000000008d000000| PB 0x000000008d000000| Untracked 
| 209|0x000000008d100000, 0x000000008d100000, 0x000000008d200000|  0%| F|  |TAMS 0x000000008d100000| PB 0x000000008d100000| Untracked 
| 210|0x000000008d200000, 0x000000008d200000, 0x000000008d300000|  0%| F|  |TAMS 0x000000008d200000| PB 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d300000, 0x000000008d400000|  0%| F|  |TAMS 0x000000008d300000| PB 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d400000, 0x000000008d500000|  0%| F|  |TAMS 0x000000008d400000| PB 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d500000, 0x000000008d600000|  0%| F|  |TAMS 0x000000008d500000| PB 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d600000, 0x000000008d700000|  0%| F|  |TAMS 0x000000008d600000| PB 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d700000, 0x000000008d800000|  0%| F|  |TAMS 0x000000008d700000| PB 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d800000, 0x000000008d900000|  0%| F|  |TAMS 0x000000008d800000| PB 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008d900000, 0x000000008da00000|  0%| F|  |TAMS 0x000000008d900000| PB 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008da00000, 0x000000008db00000|  0%| F|  |TAMS 0x000000008da00000| PB 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008db00000, 0x000000008dc00000|  0%| F|  |TAMS 0x000000008db00000| PB 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dc00000, 0x000000008dd00000|  0%| F|  |TAMS 0x000000008dc00000| PB 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008dd00000, 0x000000008de00000|  0%| F|  |TAMS 0x000000008dd00000| PB 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008de00000, 0x000000008df00000|  0%| F|  |TAMS 0x000000008de00000| PB 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008df00000, 0x000000008e000000|  0%| F|  |TAMS 0x000000008df00000| PB 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e000000, 0x000000008e100000|  0%| F|  |TAMS 0x000000008e000000| PB 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e100000, 0x000000008e200000|  0%| F|  |TAMS 0x000000008e100000| PB 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e200000, 0x000000008e300000|  0%| F|  |TAMS 0x000000008e200000| PB 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e300000, 0x000000008e400000|  0%| F|  |TAMS 0x000000008e300000| PB 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e400000, 0x000000008e500000|  0%| F|  |TAMS 0x000000008e400000| PB 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e500000, 0x000000008e600000|  0%| F|  |TAMS 0x000000008e500000| PB 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e600000, 0x000000008e700000|  0%| F|  |TAMS 0x000000008e600000| PB 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e700000, 0x000000008e800000|  0%| F|  |TAMS 0x000000008e700000| PB 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e800000, 0x000000008e900000|  0%| F|  |TAMS 0x000000008e800000| PB 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008e900000, 0x000000008ea00000|  0%| F|  |TAMS 0x000000008e900000| PB 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008ea00000, 0x000000008eb00000|  0%| F|  |TAMS 0x000000008ea00000| PB 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008eb00000, 0x000000008ec00000|  0%| F|  |TAMS 0x000000008eb00000| PB 0x000000008eb00000| Untracked 
| 236|0x000000008ec00000, 0x000000008ec00000, 0x000000008ed00000|  0%| F|  |TAMS 0x000000008ec00000| PB 0x000000008ec00000| Untracked 
| 237|0x000000008ed00000, 0x000000008ed00000, 0x000000008ee00000|  0%| F|  |TAMS 0x000000008ed00000| PB 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ee00000, 0x000000008ef00000|  0%| F|  |TAMS 0x000000008ee00000| PB 0x000000008ee00000| Untracked 
| 239|0x000000008ef00000, 0x000000008ef00000, 0x000000008f000000|  0%| F|  |TAMS 0x000000008ef00000| PB 0x000000008ef00000| Untracked 
| 240|0x000000008f000000, 0x000000008f000000, 0x000000008f100000|  0%| F|  |TAMS 0x000000008f000000| PB 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f100000, 0x000000008f200000|  0%| F|  |TAMS 0x000000008f100000| PB 0x000000008f100000| Untracked 
| 242|0x000000008f200000, 0x000000008f200000, 0x000000008f300000|  0%| F|  |TAMS 0x000000008f200000| PB 0x000000008f200000| Untracked 
| 243|0x000000008f300000, 0x000000008f300000, 0x000000008f400000|  0%| F|  |TAMS 0x000000008f300000| PB 0x000000008f300000| Untracked 
| 244|0x000000008f400000, 0x000000008f400000, 0x000000008f500000|  0%| F|  |TAMS 0x000000008f400000| PB 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f500000, 0x000000008f600000|  0%| F|  |TAMS 0x000000008f500000| PB 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f600000, 0x000000008f700000|  0%| F|  |TAMS 0x000000008f600000| PB 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f700000, 0x000000008f800000|  0%| F|  |TAMS 0x000000008f700000| PB 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f800000, 0x000000008f900000|  0%| F|  |TAMS 0x000000008f800000| PB 0x000000008f800000| Untracked 
| 249|0x000000008f900000, 0x000000008f900000, 0x000000008fa00000|  0%| F|  |TAMS 0x000000008f900000| PB 0x000000008f900000| Untracked 
| 250|0x000000008fa00000, 0x000000008fa00000, 0x000000008fb00000|  0%| F|  |TAMS 0x000000008fa00000| PB 0x000000008fa00000| Untracked 
| 251|0x000000008fb00000, 0x000000008fb00000, 0x000000008fc00000|  0%| F|  |TAMS 0x000000008fb00000| PB 0x000000008fb00000| Untracked 
| 252|0x000000008fc00000, 0x000000008fc00000, 0x000000008fd00000|  0%| F|  |TAMS 0x000000008fc00000| PB 0x000000008fc00000| Untracked 
| 253|0x000000008fd00000, 0x000000008fd00000, 0x000000008fe00000|  0%| F|  |TAMS 0x000000008fd00000| PB 0x000000008fd00000| Untracked 
| 254|0x000000008fe00000, 0x000000008fe00000, 0x000000008ff00000|  0%| F|  |TAMS 0x000000008fe00000| PB 0x000000008fe00000| Untracked 
| 255|0x000000008ff00000, 0x000000008ff00000, 0x0000000090000000|  0%| F|  |TAMS 0x000000008ff00000| PB 0x000000008ff00000| Untracked 
| 256|0x0000000090000000, 0x0000000090000000, 0x0000000090100000|  0%| F|  |TAMS 0x0000000090000000| PB 0x0000000090000000| Untracked 
| 257|0x0000000090100000, 0x0000000090100000, 0x0000000090200000|  0%| F|  |TAMS 0x0000000090100000| PB 0x0000000090100000| Untracked 
| 258|0x0000000090200000, 0x0000000090200000, 0x0000000090300000|  0%| F|  |TAMS 0x0000000090200000| PB 0x0000000090200000| Untracked 
| 259|0x0000000090300000, 0x0000000090312d00, 0x0000000090400000|  7%| E|  |TAMS 0x0000000090300000| PB 0x0000000090300000| Complete 
| 260|0x0000000090400000, 0x0000000090500000, 0x0000000090500000|100%| E|CS|TAMS 0x0000000090400000| PB 0x0000000090400000| Complete 
| 261|0x0000000090500000, 0x0000000090600000, 0x0000000090600000|100%| E|CS|TAMS 0x0000000090500000| PB 0x0000000090500000| Complete 
| 262|0x0000000090600000, 0x0000000090700000, 0x0000000090700000|100%| E|CS|TAMS 0x0000000090600000| PB 0x0000000090600000| Complete 
| 263|0x0000000090700000, 0x0000000090800000, 0x0000000090800000|100%| E|CS|TAMS 0x0000000090700000| PB 0x0000000090700000| Complete 
| 264|0x0000000090800000, 0x0000000090900000, 0x0000000090900000|100%| E|CS|TAMS 0x0000000090800000| PB 0x0000000090800000| Complete 
| 265|0x0000000090900000, 0x0000000090a00000, 0x0000000090a00000|100%| E|CS|TAMS 0x0000000090900000| PB 0x0000000090900000| Complete 
| 266|0x0000000090a00000, 0x0000000090b00000, 0x0000000090b00000|100%| E|CS|TAMS 0x0000000090a00000| PB 0x0000000090a00000| Complete 
| 267|0x0000000090b00000, 0x0000000090c00000, 0x0000000090c00000|100%| E|CS|TAMS 0x0000000090b00000| PB 0x0000000090b00000| Complete 
| 268|0x0000000090c00000, 0x0000000090d00000, 0x0000000090d00000|100%| E|CS|TAMS 0x0000000090c00000| PB 0x0000000090c00000| Complete 
| 269|0x0000000090d00000, 0x0000000090e00000, 0x0000000090e00000|100%| E|CS|TAMS 0x0000000090d00000| PB 0x0000000090d00000| Complete 
| 270|0x0000000090e00000, 0x0000000090f00000, 0x0000000090f00000|100%| E|CS|TAMS 0x0000000090e00000| PB 0x0000000090e00000| Complete 
| 271|0x0000000090f00000, 0x0000000091000000, 0x0000000091000000|100%| E|CS|TAMS 0x0000000090f00000| PB 0x0000000090f00000| Complete 
| 272|0x0000000091000000, 0x0000000091100000, 0x0000000091100000|100%| E|CS|TAMS 0x0000000091000000| PB 0x0000000091000000| Complete 
| 273|0x0000000091100000, 0x0000000091200000, 0x0000000091200000|100%| E|CS|TAMS 0x0000000091100000| PB 0x0000000091100000| Complete 
| 274|0x0000000091200000, 0x0000000091300000, 0x0000000091300000|100%| E|CS|TAMS 0x0000000091200000| PB 0x0000000091200000| Complete 
| 275|0x0000000091300000, 0x0000000091400000, 0x0000000091400000|100%| E|CS|TAMS 0x0000000091300000| PB 0x0000000091300000| Complete 
| 276|0x0000000091400000, 0x0000000091500000, 0x0000000091500000|100%| E|CS|TAMS 0x0000000091400000| PB 0x0000000091400000| Complete 
| 277|0x0000000091500000, 0x0000000091600000, 0x0000000091600000|100%| E|CS|TAMS 0x0000000091500000| PB 0x0000000091500000| Complete 
| 278|0x0000000091600000, 0x0000000091700000, 0x0000000091700000|100%| E|CS|TAMS 0x0000000091600000| PB 0x0000000091600000| Complete 
| 279|0x0000000091700000, 0x0000000091800000, 0x0000000091800000|100%| E|CS|TAMS 0x0000000091700000| PB 0x0000000091700000| Complete 
| 280|0x0000000091800000, 0x0000000091900000, 0x0000000091900000|100%| E|CS|TAMS 0x0000000091800000| PB 0x0000000091800000| Complete 
| 281|0x0000000091900000, 0x0000000091a00000, 0x0000000091a00000|100%| E|CS|TAMS 0x0000000091900000| PB 0x0000000091900000| Complete 
| 282|0x0000000091a00000, 0x0000000091b00000, 0x0000000091b00000|100%| E|CS|TAMS 0x0000000091a00000| PB 0x0000000091a00000| Complete 
| 283|0x0000000091b00000, 0x0000000091c00000, 0x0000000091c00000|100%| E|CS|TAMS 0x0000000091b00000| PB 0x0000000091b00000| Complete 
| 284|0x0000000091c00000, 0x0000000091d00000, 0x0000000091d00000|100%| E|CS|TAMS 0x0000000091c00000| PB 0x0000000091c00000| Complete 
| 285|0x0000000091d00000, 0x0000000091e00000, 0x0000000091e00000|100%| E|CS|TAMS 0x0000000091d00000| PB 0x0000000091d00000| Complete 
| 286|0x0000000091e00000, 0x0000000091f00000, 0x0000000091f00000|100%| E|CS|TAMS 0x0000000091e00000| PB 0x0000000091e00000| Complete 
| 287|0x0000000091f00000, 0x0000000092000000, 0x0000000092000000|100%| S|CS|TAMS 0x0000000091f00000| PB 0x0000000091f00000| Complete 
| 437|0x000000009b500000, 0x000000009b600000, 0x000000009b600000|100%| E|CS|TAMS 0x000000009b500000| PB 0x000000009b500000| Complete 
| 438|0x000000009b600000, 0x000000009b700000, 0x000000009b700000|100%| E|CS|TAMS 0x000000009b600000| PB 0x000000009b600000| Complete 
| 439|0x000000009b700000, 0x000000009b800000, 0x000000009b800000|100%| E|CS|TAMS 0x000000009b700000| PB 0x000000009b700000| Complete 
| 440|0x000000009b800000, 0x000000009b900000, 0x000000009b900000|100%| E|CS|TAMS 0x000000009b800000| PB 0x000000009b800000| Complete 
| 441|0x000000009b900000, 0x000000009ba00000, 0x000000009ba00000|100%| E|CS|TAMS 0x000000009b900000| PB 0x000000009b900000| Complete 
| 442|0x000000009ba00000, 0x000000009bb00000, 0x000000009bb00000|100%| E|CS|TAMS 0x000000009ba00000| PB 0x000000009ba00000| Complete 
| 443|0x000000009bb00000, 0x000000009bc00000, 0x000000009bc00000|100%| E|CS|TAMS 0x000000009bb00000| PB 0x000000009bb00000| Complete 
| 444|0x000000009bc00000, 0x000000009bd00000, 0x000000009bd00000|100%| E|CS|TAMS 0x000000009bc00000| PB 0x000000009bc00000| Complete 
| 445|0x000000009bd00000, 0x000000009be00000, 0x000000009be00000|100%| E|CS|TAMS 0x000000009bd00000| PB 0x000000009bd00000| Complete 
| 446|0x000000009be00000, 0x000000009bf00000, 0x000000009bf00000|100%| E|CS|TAMS 0x000000009be00000| PB 0x000000009be00000| Complete 
| 511|0x000000009ff00000, 0x00000000a0000000, 0x00000000a0000000|100%| E|CS|TAMS 0x000000009ff00000| PB 0x000000009ff00000| Complete 
|2036|0x00000000ff400000, 0x00000000ff421c60, 0x00000000ff500000| 13%| O|  |TAMS 0x00000000ff400000| PB 0x00000000ff400000| Untracked 
|2037|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| O|  |TAMS 0x00000000ff500000| PB 0x00000000ff500000| Untracked 
|2038|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| O|  |TAMS 0x00000000ff600000| PB 0x00000000ff600000| Untracked 
|2039|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| E|CS|TAMS 0x00000000ff700000| PB 0x00000000ff700000| Complete 
|2040|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| E|CS|TAMS 0x00000000ff800000| PB 0x00000000ff800000| Complete 
|2041|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| O|  |TAMS 0x00000000ff900000| PB 0x00000000ff900000| Untracked 
|2042|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| O|  |TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Untracked 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| O|  |TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Untracked 
|2044|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| E|CS|TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Complete 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| O|  |TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Untracked 
|2046|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| E|CS|TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Complete 
|2047|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| O|  |TAMS 0x00000000fff00000| PB 0x00000000fff00000| Untracked 

Card table byte_map: [0x00000223ffac0000,0x00000223ffec0000] _byte_map_base: 0x00000223ff6c0000

Marking Bits: (CMBitMap*) 0x00000223ecf6f7a0
 Bits: [0x0000022380000000, 0x0000022382000000)

Polling page: 0x00000223ebd20000

Metaspace:

Usage:
  Non-class:    130.32 MB used.
      Class:     19.94 MB used.
       Both:    150.26 MB used.

Virtual space:
  Non-class space:      192.00 MB reserved,     131.56 MB ( 69%) committed,  3 nodes.
      Class space:        1.00 GB reserved,      21.12 MB (  2%) committed,  1 nodes.
             Both:        1.19 GB reserved,     152.69 MB ( 13%) committed. 

Chunk freelists:
   Non-Class:  12.36 MB
       Class:  10.69 MB
        Both:  23.05 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 217.12 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 4770.
num_arena_deaths: 0.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 2439.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 9.
num_chunks_taken_from_freelist: 11365.
num_chunk_merges: 9.
num_chunk_splits: 7497.
num_chunks_enlarged: 4760.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=11929Kb max_used=11929Kb free=108070Kb
 bounds [0x00000223f7c40000, 0x00000223f87f0000, 0x00000223ff170000]
CodeHeap 'profiled nmethods': size=120000Kb used=31790Kb max_used=31790Kb free=88209Kb
 bounds [0x00000223f0170000, 0x00000223f2080000, 0x00000223f76a0000]
CodeHeap 'non-nmethods': size=5760Kb used=3058Kb max_used=3137Kb free=2701Kb
 bounds [0x00000223f76a0000, 0x00000223f79c0000, 0x00000223f7c40000]
 total_blobs=17365 nmethods=16229 adapters=1040
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 45.625 Thread 0x0000022384cb3790 nmethod 18755 0x00000223f206a490 code [0x00000223f206a640, 0x00000223f206a778]
Event: 45.625 Thread 0x0000022384cb3790 18769       3       org.jetbrains.kotlin.metadata.ProtoBuf$Function::getContextReceiverTypeCount (10 bytes)
Event: 45.625 Thread 0x0000022384cb3790 nmethod 18769 0x00000223f206a810 code [0x00000223f206a9c0, 0x00000223f206ab90]
Event: 45.625 Thread 0x0000022384cb3790 18780       3       org.jetbrains.kotlin.analysis.decompiler.stub.ClsStubBuilderContextKt::child (84 bytes)
Event: 45.626 Thread 0x0000022384cb3790 nmethod 18780 0x00000223f206ac90 code [0x00000223f206b100, 0x00000223f206cd80]
Event: 45.626 Thread 0x0000022384cb3790 18800       3       org.jetbrains.kotlin.metadata.ProtoBuf$Type::getArgumentCount (10 bytes)
Event: 45.626 Thread 0x0000022384cb3790 nmethod 18800 0x00000223f206d910 code [0x00000223f206dac0, 0x00000223f206dc90]
Event: 45.626 Thread 0x0000022384cb3790 18764       3       org.jetbrains.kotlin.metadata.ProtoBuf$Contract::getDefaultInstance (4 bytes)
Event: 45.626 Thread 0x0000022384cb3790 nmethod 18764 0x00000223f108cb10 code [0x00000223f108cca0, 0x00000223f108cd98]
Event: 45.626 Thread 0x0000022384cb3790 18749       3       org.jetbrains.kotlin.analysis.decompiler.stub.CallableClsStubBuilder$callableStub$2::invoke (12 bytes)
Event: 45.627 Thread 0x0000022384cb3790 nmethod 18749 0x00000223f206dd90 code [0x00000223f206df40, 0x00000223f206e118]
Event: 45.627 Thread 0x0000022384cb3790 18750       3       org.jetbrains.kotlin.analysis.decompiler.stub.CallableClsStubBuilder::<init> (112 bytes)
Event: 45.628 Thread 0x00000223917d59d0 nmethod 18798 0x00000223f87dc090 code [0x00000223f87dc240, 0x00000223f87dc4c0]
Event: 45.628 Thread 0x00000223917d59d0 18803       4       org.jetbrains.kotlin.metadata.ProtoBuf$QualifiedNameTable::getQualifiedName (14 bytes)
Event: 45.629 Thread 0x0000022384cb3790 nmethod 18750 0x00000223f206e210 code [0x00000223f206e7a0, 0x00000223f20710c8]
Event: 45.629 Thread 0x0000022384cb3790 18802   !   3       org.jetbrains.kotlin.metadata.ProtoBuf$Type::<init> (875 bytes)
Event: 45.629 Thread 0x00000223917d59d0 nmethod 18803 0x00000223f87dc690 code [0x00000223f87dc860, 0x00000223f87dcb18]
Event: 45.629 Thread 0x00000223917d59d0 18799       4       org.jetbrains.kotlin.protobuf.SmallSortedMap$EmptySet$2::iterator (4 bytes)
Event: 45.630 Thread 0x00000223917d59d0 nmethod 18799 0x00000223f87dcd90 code [0x00000223f87dcf20, 0x00000223f87dcfb0]
Event: 45.630 Thread 0x00000223917d59d0 18761       4       org.jetbrains.kotlin.protobuf.CodedOutputStream::flush (12 bytes)

GC Heap History (20 events):
Event: 10.264 GC heap before
{Heap before GC invocations=27 (full 0):
 garbage-first heap   total 94208K, used 92061K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 25 young (25600K), 4 survivors (4096K)
 Metaspace       used 70128K, committed 71808K, reserved 1114112K
  class space    used 9427K, committed 10304K, reserved 1048576K
}
Event: 10.273 GC heap after
{Heap after GC invocations=28 (full 0):
 garbage-first heap   total 315392K, used 76955K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 70128K, committed 71808K, reserved 1114112K
  class space    used 9427K, committed 10304K, reserved 1048576K
}
Event: 12.232 GC heap before
{Heap before GC invocations=28 (full 0):
 garbage-first heap   total 315392K, used 266395K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 184 young (188416K), 4 survivors (4096K)
 Metaspace       used 86967K, committed 88896K, reserved 1179648K
  class space    used 11551K, committed 12544K, reserved 1048576K
}
Event: 12.237 GC heap after
{Heap after GC invocations=29 (full 0):
 garbage-first heap   total 315392K, used 90761K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 16 young (16384K), 16 survivors (16384K)
 Metaspace       used 86967K, committed 88896K, reserved 1179648K
  class space    used 11551K, committed 12544K, reserved 1048576K
}
Event: 13.554 GC heap before
{Heap before GC invocations=29 (full 0):
 garbage-first heap   total 315392K, used 266889K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 184 young (188416K), 16 survivors (16384K)
 Metaspace       used 97363K, committed 99392K, reserved 1179648K
  class space    used 13134K, committed 14144K, reserved 1048576K
}
Event: 13.562 GC heap after
{Heap after GC invocations=30 (full 0):
 garbage-first heap   total 315392K, used 104263K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 21 young (21504K), 21 survivors (21504K)
 Metaspace       used 97363K, committed 99392K, reserved 1179648K
  class space    used 13134K, committed 14144K, reserved 1048576K
}
Event: 14.957 GC heap before
{Heap before GC invocations=30 (full 0):
 garbage-first heap   total 315392K, used 271175K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 184 young (188416K), 21 survivors (21504K)
 Metaspace       used 102939K, committed 105088K, reserved 1179648K
  class space    used 13925K, committed 14976K, reserved 1048576K
}
Event: 14.970 GC heap after
{Heap after GC invocations=31 (full 0):
 garbage-first heap   total 315392K, used 117205K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 22 young (22528K), 22 survivors (22528K)
 Metaspace       used 102939K, committed 105088K, reserved 1179648K
  class space    used 13925K, committed 14976K, reserved 1048576K
}
Event: 16.442 GC heap before
{Heap before GC invocations=31 (full 0):
 garbage-first heap   total 315392K, used 275925K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 177 young (181248K), 22 survivors (22528K)
 Metaspace       used 112848K, committed 115072K, reserved 1179648K
  class space    used 15381K, committed 16448K, reserved 1048576K
}
Event: 16.455 GC heap after
{Heap after GC invocations=32 (full 0):
 garbage-first heap   total 315392K, used 132058K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 17 young (17408K), 17 survivors (17408K)
 Metaspace       used 112848K, committed 115072K, reserved 1179648K
  class space    used 15381K, committed 16448K, reserved 1048576K
}
Event: 23.703 GC heap before
{Heap before GC invocations=33 (full 0):
 garbage-first heap   total 315392K, used 282586K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 158 young (161792K), 17 survivors (17408K)
 Metaspace       used 121745K, committed 124160K, reserved 1179648K
  class space    used 16484K, committed 17664K, reserved 1048576K
}
Event: 23.712 GC heap after
{Heap after GC invocations=34 (full 0):
 garbage-first heap   total 315392K, used 143600K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 121745K, committed 124160K, reserved 1179648K
  class space    used 16484K, committed 17664K, reserved 1048576K
}
Event: 42.802 GC heap before
{Heap before GC invocations=34 (full 0):
 garbage-first heap   total 315392K, used 284912K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 146 young (149504K), 14 survivors (14336K)
 Metaspace       used 127852K, committed 130368K, reserved 1179648K
  class space    used 17232K, committed 18496K, reserved 1048576K
}
Event: 42.811 GC heap after
{Heap after GC invocations=35 (full 0):
 garbage-first heap   total 315392K, used 157729K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 127852K, committed 130368K, reserved 1179648K
  class space    used 17232K, committed 18496K, reserved 1048576K
}
Event: 42.934 GC heap before
{Heap before GC invocations=35 (full 0):
 garbage-first heap   total 315392K, used 171041K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 26 young (26624K), 11 survivors (11264K)
 Metaspace       used 129465K, committed 131968K, reserved 1179648K
  class space    used 17358K, committed 18624K, reserved 1048576K
}
Event: 42.939 GC heap after
{Heap after GC invocations=36 (full 0):
 garbage-first heap   total 315392K, used 161333K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 129465K, committed 131968K, reserved 1179648K
  class space    used 17358K, committed 18624K, reserved 1048576K
}
Event: 44.567 GC heap before
{Heap before GC invocations=37 (full 0):
 garbage-first heap   total 315392K, used 292405K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 124 young (126976K), 4 survivors (4096K)
 Metaspace       used 148047K, committed 150528K, reserved 1179648K
  class space    used 19822K, committed 21056K, reserved 1048576K
}
Event: 44.573 GC heap after
{Heap after GC invocations=38 (full 0):
 garbage-first heap   total 315392K, used 173556K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 148047K, committed 150528K, reserved 1179648K
  class space    used 19822K, committed 21056K, reserved 1048576K
}
Event: 44.944 GC heap before
{Heap before GC invocations=38 (full 0):
 garbage-first heap   total 315392K, used 286196K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 122 young (124928K), 14 survivors (14336K)
 Metaspace       used 148657K, committed 151104K, reserved 1179648K
  class space    used 19849K, committed 21056K, reserved 1048576K
}
Event: 44.955 GC heap after
{Heap after GC invocations=39 (full 0):
 garbage-first heap   total 318464K, used 186503K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 16 young (16384K), 16 survivors (16384K)
 Metaspace       used 148657K, committed 151104K, reserved 1179648K
  class space    used 19849K, committed 21056K, reserved 1048576K
}

Dll operation events (3 events):
Event: 0.023 Loaded shared library D:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.049 Loaded shared library D:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 1.068 Loaded shared library D:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 45.622 Thread 0x00000223919578a0 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000223f87b8db8 relative=0x0000000000000178
Event: 45.622 Thread 0x00000223919578a0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000223f87b8db8 method=org.jetbrains.kotlin.analysis.decompiler.stub.ClsStubBuildingKt$createModifierListStub$1.invoke(Lorg/jetbrains/kotlin/lexer/KtModifierKeywordToken;)Ljava/lang/Boolean; @
Event: 45.622 Thread 0x00000223919578a0 DEOPT PACKING pc=0x00000223f87b8db8 sp=0x000000b5e85fc420
Event: 45.622 Thread 0x00000223919578a0 DEOPT UNPACKING pc=0x00000223f76f46a2 sp=0x000000b5e85fc340 mode 2
Event: 45.622 Thread 0x00000223919578a0 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000223f87b8db8 relative=0x0000000000000178
Event: 45.622 Thread 0x00000223919578a0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000223f87b8db8 method=org.jetbrains.kotlin.analysis.decompiler.stub.ClsStubBuildingKt$createModifierListStub$1.invoke(Lorg/jetbrains/kotlin/lexer/KtModifierKeywordToken;)Ljava/lang/Boolean; @
Event: 45.622 Thread 0x00000223919578a0 DEOPT PACKING pc=0x00000223f87b8db8 sp=0x000000b5e85fc420
Event: 45.622 Thread 0x00000223919578a0 DEOPT UNPACKING pc=0x00000223f76f46a2 sp=0x000000b5e85fc340 mode 2
Event: 45.622 Thread 0x00000223919578a0 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000223f87b8db8 relative=0x0000000000000178
Event: 45.622 Thread 0x00000223919578a0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000223f87b8db8 method=org.jetbrains.kotlin.analysis.decompiler.stub.ClsStubBuildingKt$createModifierListStub$1.invoke(Lorg/jetbrains/kotlin/lexer/KtModifierKeywordToken;)Ljava/lang/Boolean; @
Event: 45.622 Thread 0x00000223919578a0 DEOPT PACKING pc=0x00000223f87b8db8 sp=0x000000b5e85fc420
Event: 45.622 Thread 0x00000223919578a0 DEOPT UNPACKING pc=0x00000223f76f46a2 sp=0x000000b5e85fc340 mode 2
Event: 45.629 Thread 0x00000223919578a0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000223f87d955c relative=0x0000000000001f5c
Event: 45.629 Thread 0x000002239194eed0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000223f87d955c relative=0x0000000000001f5c
Event: 45.629 Thread 0x000002239194eed0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000223f87d955c method=com.intellij.psi.stubs.StubList.addStub(Lcom/intellij/psi/stubs/StubBase;Lcom/intellij/psi/stubs/StubBase;Lcom/intellij/psi/stubs/IStubElementType;)V @ 21 c2
Event: 45.629 Thread 0x00000223919578a0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000223f87d955c method=com.intellij.psi.stubs.StubList.addStub(Lcom/intellij/psi/stubs/StubBase;Lcom/intellij/psi/stubs/StubBase;Lcom/intellij/psi/stubs/IStubElementType;)V @ 21 c2
Event: 45.629 Thread 0x000002239194eed0 DEOPT PACKING pc=0x00000223f87d955c sp=0x000000b5e72fd670
Event: 45.629 Thread 0x00000223919578a0 DEOPT PACKING pc=0x00000223f87d955c sp=0x000000b5e85fd0a0
Event: 45.629 Thread 0x000002239194eed0 DEOPT UNPACKING pc=0x00000223f76f46a2 sp=0x000000b5e72fd640 mode 2
Event: 45.629 Thread 0x00000223919578a0 DEOPT UNPACKING pc=0x00000223f76f46a2 sp=0x000000b5e85fd070 mode 2

Classes loaded (20 events):
Event: 43.544 Loading class java/util/TreeMap$NavigableSubMap$SubMapIterator done
Event: 43.544 Loading class java/util/TreeMap$NavigableSubMap$SubMapKeyIterator done
Event: 43.734 Loading class java/util/zip/ZipFile$ZipEntryIterator
Event: 43.734 Loading class java/util/zip/ZipFile$ZipEntryIterator done
Event: 44.379 Loading class java/nio/charset/IllegalCharsetNameException
Event: 44.379 Loading class java/nio/charset/IllegalCharsetNameException done
Event: 44.379 Loading class sun/nio/cs/UTF_32BE
Event: 44.380 Loading class sun/nio/cs/UTF_32BE done
Event: 44.380 Loading class sun/nio/cs/UTF_32LE
Event: 44.380 Loading class sun/nio/cs/UTF_32LE done
Event: 44.380 Loading class sun/nio/cs/MS1251
Event: 44.380 Loading class sun/nio/cs/MS1251 done
Event: 44.386 Loading class java/beans/PropertyChangeSupport
Event: 44.386 Loading class java/beans/PropertyChangeSupport done
Event: 44.387 Loading class java/beans/PropertyChangeSupport$PropertyChangeListenerMap
Event: 44.387 Loading class java/beans/ChangeListenerMap
Event: 44.387 Loading class java/beans/ChangeListenerMap done
Event: 44.387 Loading class java/beans/PropertyChangeSupport$PropertyChangeListenerMap done
Event: 45.403 Loading class java/util/regex/Pattern$Bound
Event: 45.404 Loading class java/util/regex/Pattern$Bound done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 45.309 Thread 0x00000223919578a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009b61cf28}> (0x000000009b61cf28) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.311 Thread 0x00000223919578a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009b61e2f0}> (0x000000009b61e2f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.311 Thread 0x000002239194eed0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffebe5c8}> (0x00000000ffebe5c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.313 Thread 0x000002239194eed0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffebf880}> (0x00000000ffebf880) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.314 Thread 0x00000223919578a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009b61f5a8}> (0x000000009b61f5a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.315 Thread 0x000002239194eed0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffec0658}> (0x00000000ffec0658) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.315 Thread 0x00000223919578a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009b620380}> (0x000000009b620380) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.316 Thread 0x000002239194eed0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffec1440}> (0x00000000ffec1440) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.316 Thread 0x00000223919578a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009b621168}> (0x000000009b621168) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.319 Thread 0x000002239194eed0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffec2c68}> (0x00000000ffec2c68) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.319 Thread 0x00000223919578a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009b622990}> (0x000000009b622990) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.319 Thread 0x00000223919578a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009b623288}> (0x000000009b623288) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.319 Thread 0x000002239194eed0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffec3560}> (0x00000000ffec3560) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.326 Thread 0x00000223919578a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000009b627520}> (0x000000009b627520) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.327 Thread 0x000002239194eed0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ffec77f8}> (0x00000000ffec77f8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 45.614 Thread 0x000002239194eed0 Implicit null exception at 0x00000223f87cb324 to 0x00000223f87cb9d8
Event: 45.614 Thread 0x00000223919578a0 Implicit null exception at 0x00000223f87cb324 to 0x00000223f87cb9d8
Event: 45.614 Thread 0x000002239194eed0 Implicit null exception at 0x00000223f87c6388 to 0x00000223f87c84d0
Event: 45.629 Thread 0x000002239194eed0 Implicit null exception at 0x00000223f87d7660 to 0x00000223f87d9538
Event: 45.629 Thread 0x00000223919578a0 Implicit null exception at 0x00000223f87d7660 to 0x00000223f87d9538

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 44.374 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 44.374 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 44.382 Executing VM operation: ICBufferFull
Event: 44.382 Executing VM operation: ICBufferFull done
Event: 44.382 Executing VM operation: ICBufferFull
Event: 44.382 Executing VM operation: ICBufferFull done
Event: 44.410 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 44.410 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 44.551 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 44.552 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 44.567 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 44.573 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 44.755 Executing VM operation: ICBufferFull
Event: 44.755 Executing VM operation: ICBufferFull done
Event: 44.944 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 44.955 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 45.346 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 45.347 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 45.610 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 45.611 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f0f1b790
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f0fe6610
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f0fe7590
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f0feb510
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f104cd90
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f1051610
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f1053590
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f1056c10
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f10bd810
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f10bed10
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f10c4210
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f1114890
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f1117110
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f1175a10
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f117d290
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f11d8890
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f11d9290
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f128b390
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f1579790
Event: 43.025 Thread 0x0000022384beb8b0 flushing  nmethod 0x00000223f1580790

Events (20 events):
Event: 42.778 Thread 0x0000022391f5a3d0 Thread added: 0x000002238d0d3050
Event: 42.778 Thread 0x0000022391f5a3d0 Thread added: 0x000002238d0d3d70
Event: 42.778 Thread 0x0000022391f5a3d0 Thread added: 0x000002238d0d08f0
Event: 42.778 Thread 0x0000022391f5a3d0 Thread added: 0x000002238d0d0f80
Event: 42.778 Thread 0x0000022391f5a3d0 Thread added: 0x000002238d0d0260
Event: 42.814 Thread 0x00000223919564f0 Thread exited: 0x00000223919564f0
Event: 42.814 Thread 0x0000022391957210 Thread exited: 0x0000022391957210
Event: 43.356 Thread 0x000002239157e720 Thread exited: 0x000002239157e720
Event: 43.396 Thread 0x000002238fbad4a0 Thread exited: 0x000002238fbad4a0
Event: 43.400 Thread 0x0000022391f5c4a0 Thread added: 0x000002238f2aa130
Event: 43.406 Thread 0x0000022384cb3790 Thread added: 0x00000223902436a0
Event: 43.440 Thread 0x0000022391f59d40 Thread added: 0x000002238f2abb70
Event: 43.586 Thread 0x00000223902436a0 Thread added: 0x0000022390244440
Event: 43.863 Thread 0x0000022391f59d40 Thread added: 0x00000223916cc930
Event: 44.170 Thread 0x00000223902436a0 Thread exited: 0x00000223902436a0
Event: 44.170 Thread 0x0000022390244440 Thread exited: 0x0000022390244440
Event: 44.343 Thread 0x0000022384cb3790 Thread added: 0x00000223917d59d0
Event: 44.527 Thread 0x0000022384cb00f0 Thread added: 0x000002238d9fab60
Event: 45.333 Thread 0x000002238d9fab60 Thread exited: 0x000002238d9fab60
Event: 45.583 Thread 0x0000022384cb3790 Thread added: 0x00000223925d2c20


Dynamic libraries:
0x00007ff6c60f0000 - 0x00007ff6c60fa000 	D:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffe61990000 - 0x00007ffe61ba7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffe60ba0000 - 0x00007ffe60c64000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe5ed80000 - 0x00007ffe5f13a000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe5f310000 - 0x00007ffe5f421000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe5ab90000 - 0x00007ffe5aca6000 	C:\WINDOWS\SYSTEM32\winahframe64.dll
0x00007ffe617a0000 - 0x00007ffe6194e000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe5f2e0000 - 0x00007ffe5f306000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe60fa0000 - 0x00007ffe60fc9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe5ec60000 - 0x00007ffe5ed7b000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe5f430000 - 0x00007ffe5f4ca000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe60930000 - 0x00007ffe609e2000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe60af0000 - 0x00007ffe60b97000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe60140000 - 0x00007ffe601e7000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe5f2b0000 - 0x00007ffe5f2d8000 	C:\WINDOWS\System32\bcrypt.dll
0x00000223e82a0000 - 0x00000223e83b4000 	C:\WINDOWS\System32\RPCRT4.dll
0x00000223e83d0000 - 0x00000223e8575000 	C:\WINDOWS\System32\ole32.dll
0x00007ffe613c0000 - 0x00007ffe6174f000 	C:\WINDOWS\System32\combase.dll
0x00007ffe45a80000 - 0x00007ffe45a98000 	D:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffe39470000 - 0x00007ffe3948b000 	D:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffe5d890000 - 0x00007ffe5d89a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe59a00000 - 0x00007ffe59c92000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4541_none_2710d1c57384c085\COMCTL32.dll
0x00007ffe607f0000 - 0x00007ffe60821000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe59e80000 - 0x00007ffe5a726000 	C:\WINDOWS\SYSTEM32\winahdcore64.dll
0x00007ffe600e0000 - 0x00007ffe6013e000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffe5f860000 - 0x00007ffe600d8000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffe61100000 - 0x00007ffe611d7000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe601f0000 - 0x00007ffe60261000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe60270000 - 0x00007ffe60370000 	C:\WINDOWS\System32\COMDLG32.dll
0x00000223e9e90000 - 0x00000223e9f8a000 	C:\WINDOWS\System32\shcore.dll
0x00007ffe59e10000 - 0x00007ffe59e2e000 	C:\WINDOWS\SYSTEM32\MPR.dll
0x00007ffe59d60000 - 0x00007ffe59dc9000 	C:\WINDOWS\SYSTEM32\OLEACC.dll
0x00007ffe59dd0000 - 0x00007ffe59e04000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe5f610000 - 0x00007ffe5f68b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe574b0000 - 0x00007ffe5771d000 	C:\WINDOWS\SYSTEM32\dtframe64.dll
0x00007ffe56f70000 - 0x00007ffe56fa2000 	C:\WINDOWS\SYSTEM32\TIjtDrvd64.dll
0x00007ffe56ec0000 - 0x00007ffe56f67000 	C:\WINDOWS\SYSTEM32\winspool.drv
0x00007ffe56d90000 - 0x00007ffe56eb7000 	C:\WINDOWS\SYSTEM32\dtsframe64.dll
0x00007ffe5e190000 - 0x00007ffe5e1f9000 	C:\WINDOWS\SYSTEM32\mswsock.dll
0x00007ffe610f0000 - 0x00007ffe610f8000 	C:\WINDOWS\System32\psapi.dll
0x00007ffe568e0000 - 0x00007ffe56bf3000 	C:\WINDOWS\SYSTEM32\windlgctl64.dll
0x00007ffe568a0000 - 0x00007ffe568a7000 	C:\WINDOWS\SYSTEM32\MSIMG32.dll
0x00007ffe566e0000 - 0x00007ffe5689a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.22621.4541_none_57fbc4aace173a53\gdiplus.dll
0x00007ffe5bcf0000 - 0x00007ffe5bda1000 	C:\WINDOWS\SYSTEM32\UxTheme.dll
0x00007ffe5bf50000 - 0x00007ffe5bf7b000 	C:\WINDOWS\SYSTEM32\dwmapi.dll
0x00007ffe4abf0000 - 0x00007ffe4abfc000 	C:\WINDOWS\SYSTEM32\WinUsb.dll
0x00007ffe4ab60000 - 0x00007ffe4abea000 	C:\WINDOWS\SYSTEM32\mf.dll
0x00007ffe4a7c0000 - 0x00007ffe4ab5b000 	C:\WINDOWS\SYSTEM32\MFCORE.DLL
0x00000223eb7a0000 - 0x00000223ebc14000 	C:\WINDOWS\System32\setupapi.dll
0x00007ffe5c810000 - 0x00007ffe5d118000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffe5c6d0000 - 0x00007ffe5c80f000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ffe561d0000 - 0x00007ffe56332000 	C:\WINDOWS\SYSTEM32\TMailHook64.dll
0x00007ffe55ce0000 - 0x00007ffe55f27000 	C:\WINDOWS\SYSTEM32\winncap364.dll
0x00007ffe4e840000 - 0x00007ffe4e99a000 	\\?\SbpLkFjfawAulIbgvklRhwwvwYP\GLOBAL??\C:\WINDOWS\system32\tmumh\20019\AddOn\8.55.0.1333\TmUmEvt64.dll
0x0000000065c60000 - 0x0000000065dde000 	\\?\SbpLkFjfawAulIbgvklRhwwvwYP\GLOBAL??\C:\WINDOWS\system32\tmumh\20019\TmMon\2.9.0.1176\tmmon64.dll
0x00007ffe3b120000 - 0x00007ffe3b12c000 	D:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffe1f4f0000 - 0x00007ffe1f57d000 	D:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffda7a00000 - 0x00007ffda868b000 	D:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffe5e280000 - 0x00007ffe5e2cd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffe5e260000 - 0x00007ffe5e273000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffe5dca0000 - 0x00007ffe5dcb8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe399e0000 - 0x00007ffe399ea000 	D:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffe4ca40000 - 0x00007ffe4cc72000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe4c7e0000 - 0x00007ffe4c812000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe46240000 - 0x00007ffe4624e000 	D:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffe39a00000 - 0x00007ffe39a20000 	D:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffe394a0000 - 0x00007ffe394b8000 	D:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffe5eb90000 - 0x00007ffe5ebbb000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffe46230000 - 0x00007ffe46240000 	D:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffe598c0000 - 0x00007ffe599f6000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffe30b40000 - 0x00007ffe30b56000 	D:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffe54210000 - 0x00007ffe54220000 	D:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffe21fa0000 - 0x00007ffe21fc7000 	C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffdf50e0000 - 0x00007ffdf5224000 	C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ffe54200000 - 0x00007ffe54209000 	D:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffe463f0000 - 0x00007ffe463fb000 	D:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffe5e470000 - 0x00007ffe5e48b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffe5dc00000 - 0x00007ffe5dc35000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffe5e300000 - 0x00007ffe5e328000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffe5e460000 - 0x00007ffe5e46c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffe5d270000 - 0x00007ffe5d29d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x0000022384ff0000 - 0x0000022384ff9000 	C:\WINDOWS\System32\NSI.dll
0x00007ffe46330000 - 0x00007ffe46339000 	D:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffe32590000 - 0x00007ffe32598000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffe5d2a0000 - 0x00007ffe5d3a2000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffe568d0000 - 0x00007ffe568da000 	C:\Windows\System32\rasadhlp.dll
0x00007ffe5b060000 - 0x00007ffe5b0e3000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffe359a0000 - 0x00007ffe359b7000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffe35980000 - 0x00007ffe3599b000 	C:\WINDOWS\system32\pnrpnsp.dll
0x00007ffe35960000 - 0x00007ffe35971000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffe4bb80000 - 0x00007ffe4bb95000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffe35930000 - 0x00007ffe35957000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffe5da70000 - 0x00007ffe5da7c000 	C:\WINDOWS\SYSTEM32\secur32.dll
0x00007ffe5df70000 - 0x00007ffe5dfb3000 	C:\WINDOWS\SYSTEM32\SSPICLI.DLL
0x00007ffe3b420000 - 0x00007ffe3b43e000 	C:\Users\<USER>\AppData\Local\Temp\native-platform2089211818585859786dir\native-platform.dll
0x00007ffe5af70000 - 0x00007ffe5af8f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffe3b3f0000 - 0x00007ffe3b3f7000 	D:\Program Files\Android\Android Studio\jbr\bin\rmi.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4541_none_2710d1c57384c085;C:\WINDOWS\WinSxS\amd64_microsoft.windows.gdiplus_6595b64144ccf1df_1.1.22621.4541_none_57fbc4aace173a53;\\?\SbpLkFjfawAulIbgvklRhwwvwYP\GLOBAL??\C:\WINDOWS\system32\tmumh\20019\AddOn\8.55.0.1333;\\?\SbpLkFjfawAulIbgvklRhwwvwYP\GLOBAL??\C:\WINDOWS\system32\tmumh\20019\TmMon\2.9.0.1176;D:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Users\<USER>\.gradle\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64;C:\Users\<USER>\AppData\Local\Temp\native-platform2089211818585859786dir

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.9-bin\90cnw93cvbtalezasaz0blq0a\gradle-8.9\lib\agents\gradle-instrumentation-agent-8.9.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.9
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.9-bin\90cnw93cvbtalezasaz0blq0a\gradle-8.9\lib\gradle-daemon-main-8.9.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Users\<USER>\.jdks\jbr-17.0.14
PATH=C:\Users\<USER>\.jdks\jbr-17.0.12\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Local\Programs\oh-my-posh\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\tools\bin;C:\Program Files\PowerShell\7\;C:\Program Files\nodejs\;C:\Program Files\Microsoft VS Code\bin;C:\Program Files\wkhtmltopdf\bin;C:\Program Files\Git\cmd;C:\Program Files\Git\usr\bin;C:\Program Files\Git\bin;C:\Users\<USER>\.local\bin;C:\Users\<USER>\.jdks\jbr-17.0.12\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files\Git\cmd;C:\Program Files\PowerShell\7\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\Android\Sdk\tools\bin;D:\Program Files\Huawei\DevEco Studio\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links;C:\Users\<USER>\AppData\Local\Programs\Trae\bin;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Programs\AIShell;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Warp;
USERNAME=w.feng
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 28, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 736728K (2% of 33444172K total physical memory with 8008876K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 47478K
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 36291K
Loader bootstrap                                                                       : 31445K
Loader java.net.URLClassLoader                                                         : 24635K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 12045K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 550K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 513K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 326K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 235K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 189K
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 152K
Loader sun.reflect.misc.MethodUtil                                                     : 2952B

Classes loaded by more than one classloader:
Class Program                                                                         : loaded 8 times (x 68B)
Class Build_gradle                                                                    : loaded 4 times (x 126B)
Class com.google.common.collect.ImmutableList                                         : loaded 3 times (x 202B)
Class com.google.common.collect.ImmutableSet$EmptySetBuilderImpl                      : loaded 3 times (x 72B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 3 times (x 72B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 3 times (x 146B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 3 times (x 107B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 3 times (x 81B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 3 times (x 86B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 3 times (x 147B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 3 times (x 108B)
Class com.google.common.base.CharMatcher$1                                            : loaded 3 times (x 109B)
Class com.google.common.base.CharMatcher                                              : loaded 3 times (x 107B)
Class com.google.common.base.Preconditions                                            : loaded 3 times (x 67B)
Class com.google.common.collect.Hashing                                               : loaded 3 times (x 67B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 3 times (x 65B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 3 times (x 107B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 3 times (x 107B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 3 times (x 109B)
Class com.google.common.collect.RegularImmutableMap$BucketOverflowException           : loaded 3 times (x 78B)
Class com.google.common.base.CharMatcher$SingleWidth                                  : loaded 3 times (x 108B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 3 times (x 73B)
Class com.google.common.base.CharMatcher$Whitespace                                   : loaded 3 times (x 108B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 3 times (x 142B)
Class com.google.common.base.Splitter$1$1                                             : loaded 3 times (x 82B)
Class com.google.common.base.CharMatcher$JavaLowerCase                                : loaded 3 times (x 107B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 3 times (x 66B)
Class com.google.common.base.CharMatcher$JavaIsoControl                               : loaded 3 times (x 108B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 3 times (x 67B)
Class com.google.common.base.CharMatcher$Digit                                        : loaded 3 times (x 108B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 3 times (x 117B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 3 times (x 121B)
Class com.google.common.base.AbstractIterator$1                                       : loaded 3 times (x 67B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 3 times (x 78B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 3 times (x 65B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 3 times (x 109B)
Class com.google.common.base.Predicate                                                : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMap                                          : loaded 3 times (x 116B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 3 times (x 107B)
Class com.google.common.collect.Sets                                                  : loaded 3 times (x 67B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 3 times (x 108B)
Class com.google.common.base.CharMatcher$Ascii                                        : loaded 3 times (x 108B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 3 times (x 108B)
Class com.google.common.collect.ImmutableSet                                          : loaded 3 times (x 141B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 3 times (x 203B)
Class com.google.common.base.AbstractIterator                                         : loaded 3 times (x 76B)
Class com.google.common.math.IntMath$1                                                : loaded 3 times (x 67B)
Class com.google.common.base.CharMatcher$Invisible                                    : loaded 3 times (x 108B)
Class com.google.common.collect.Sets$SetView                                          : loaded 3 times (x 134B)
Class com.google.common.collect.Maps$8                                                : loaded 3 times (x 78B)
Class Build_gradle$1                                                                  : loaded 3 times (x 71B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 3 times (x 65B)
Class com.google.common.base.CharMatcher$JavaUpperCase                                : loaded 3 times (x 107B)
Class com.google.common.base.CharMatcher$None                                         : loaded 3 times (x 108B)
Class com.google.common.collect.Maps                                                  : loaded 3 times (x 67B)
Class com.google.common.collect.ImmutableBiMapFauxverideShim                          : loaded 3 times (x 116B)
Class com.google.common.base.Function                                                 : loaded 3 times (x 66B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 3 times (x 65B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 3 times (x 81B)
Class com.google.common.base.CharMatcher$JavaDigit                                    : loaded 3 times (x 107B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 3 times (x 81B)
Class com.google.common.math.IntMath                                                  : loaded 3 times (x 67B)
Class com.google.common.base.CommonPattern                                            : loaded 3 times (x 70B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 3 times (x 107B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 3 times (x 144B)
Class com.google.common.base.CharMatcher$JavaLetter                                   : loaded 3 times (x 107B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 3 times (x 121B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 3 times (x 205B)
Class com.google.common.collect.Lists                                                 : loaded 3 times (x 67B)
Class com.google.common.base.CharMatcher$Any                                          : loaded 3 times (x 108B)
Class com.google.common.collect.Sets$1                                                : loaded 3 times (x 135B)
Class com.google.common.base.Splitter                                                 : loaded 3 times (x 68B)
Class com.google.common.collect.ImmutableSet$CachingAsList                            : loaded 3 times (x 143B)
Class com.google.common.base.Converter                                                : loaded 3 times (x 86B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 3 times (x 121B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 3 times (x 108B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 3 times (x 108B)
Class com.google.common.collect.Sets$2                                                : loaded 3 times (x 135B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 3 times (x 80B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 3 times (x 72B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 3 times (x 139B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 3 times (x 142B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 3 times (x 121B)
Class com.google.common.base.JdkPattern                                               : loaded 3 times (x 71B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 3 times (x 66B)
Class com.google.common.base.NullnessCasts                                            : loaded 3 times (x 67B)
Class com.google.common.collect.Sets$3                                                : loaded 3 times (x 135B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 3 times (x 78B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 3 times (x 75B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 3 times (x 147B)
Class com.google.common.math.MathPreconditions                                        : loaded 3 times (x 67B)
Class com.google.common.collect.BiMap                                                 : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 3 times (x 77B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 3 times (x 146B)
Class com.google.common.collect.Sets$4                                                : loaded 3 times (x 135B)
Class com.google.common.base.CharMatcher$And                                          : loaded 3 times (x 108B)
Class com.google.common.base.CharMatcher$JavaLetterOrDigit                            : loaded 3 times (x 107B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 3 times (x 203B)
Class com.google.common.base.CharMatcher$RangesMatcher                                : loaded 3 times (x 108B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 3 times (x 212B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 3 times (x 77B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 3 times (x 76B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 3 times (x 72B)
Class com.google.common.base.Splitter$1                                               : loaded 3 times (x 73B)
Class com.google.common.collect.MapDifference                                         : loaded 3 times (x 66B)
Class kotlin.collections.ArraysKt___ArraysJvmKt                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__CollectionsKt                                 : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__RegexExtensionsJVMKt                                     : loaded 2 times (x 67B)
Class it.unimi.dsi.fastutil.ints.IntIterator                                          : loaded 2 times (x 66B)
Class com.google.common.io.ByteStreams                                                : loaded 2 times (x 67B)
Class net.rubygrapefruit.platform.internal.Platform$Posix                             : loaded 2 times (x 77B)
Class com.google.common.collect.ComparisonChain$1                                     : loaded 2 times (x 77B)
Class com.google.common.collect.AllEqualOrdering                                      : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 77B)
Class kotlin.sequences.SequencesKt__SequencesKt$generateSequence$2                    : loaded 2 times (x 75B)
Class kotlin.text.Regex$Companion                                                     : loaded 2 times (x 67B)
Class kotlin._Assertions                                                              : loaded 2 times (x 67B)
Class com.google.common.reflect.Invokable                                             : loaded 2 times (x 99B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 74B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 78B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class kotlin.reflect.jvm.internal.KClassImpl$data$1                                   : loaded 2 times (x 75B)
Class it.unimi.dsi.fastutil.longs.LongSet                                             : loaded 2 times (x 66B)
Class [Lkotlin.coroutines.intrinsics.CoroutineSingletons;                             : loaded 2 times (x 65B)
Class it.unimi.dsi.fastutil.objects.ReferenceCollection                               : loaded 2 times (x 66B)
Class kotlin.jvm.internal.FunctionReference                                           : loaded 2 times (x 118B)
Class kotlin.jvm.internal.FunctionBase                                                : loaded 2 times (x 66B)
Class kotlin.enums.EnumEntriesList                                                    : loaded 2 times (x 219B)
Class kotlin.reflect.KTypeParameter                                                   : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableRangeSet$AsSet                               : loaded 2 times (x 295B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 71B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 2 times (x 93B)
Class org.jetbrains.kotlin.config.LanguageFeature                                     : loaded 2 times (x 76B)
Class kotlin.collections.builders.ListBuilder$Companion                               : loaded 2 times (x 67B)
Class kotlin.collections.builders.ListBuilder                                         : loaded 2 times (x 203B)
Class kotlin.collections.CollectionsKt__IteratorsJVMKt                                : loaded 2 times (x 67B)
Class com.google.common.collect.BaseImmutableMultimap                                 : loaded 2 times (x 119B)
Class com.google.common.base.Joiner                                                   : loaded 2 times (x 75B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 67B)
Class kotlin.reflect.KProperty0                                                       : loaded 2 times (x 66B)
Class kotlin.Result                                                                   : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.config.LanguageOrApiVersion                                : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 77B)
Class it.unimi.dsi.fastutil.ints.IntCollection                                        : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$5                                               : loaded 2 times (x 76B)
Class [Lorg.objectweb.asm.Attribute;                                                  : loaded 2 times (x 65B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 65B)
Class com.google.common.collect.Platform                                              : loaded 2 times (x 67B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class kotlin.reflect.KClass                                                           : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 110B)
Class kotlin.sequences.TransformingSequence$iterator$1                                : loaded 2 times (x 75B)
Class kotlin.Pair                                                                     : loaded 2 times (x 68B)
Class kotlin.reflect.KProperty1                                                       : loaded 2 times (x 66B)
Class com.google.common.collect.HashBiMap$BiEntry                                     : loaded 2 times (x 79B)
Class com.google.common.base.Ascii                                                    : loaded 2 times (x 67B)
Class com.google.common.collect.SingletonImmutableBiMap                               : loaded 2 times (x 139B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 85B)
Class kotlin.sequences.GeneratorSequence$iterator$1                                   : loaded 2 times (x 75B)
Class kotlin.reflect.KProperty2                                                       : loaded 2 times (x 66B)
Class kotlin.io.CloseableKt                                                           : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__MutableCollectionsKt                          : loaded 2 times (x 67B)
Class com.google.common.reflect.TypeToken$SimpleTypeToken                             : loaded 2 times (x 69B)
Class com.google.common.io.CharSource$StringCharSource                                : loaded 2 times (x 82B)
Class com.google.common.io.ByteSource$ByteArrayByteSource                             : loaded 2 times (x 81B)
Class net.rubygrapefruit.platform.internal.WindowsProcessLauncher                     : loaded 2 times (x 72B)
Class com.google.common.collect.AbstractSetMultimap                                   : loaded 2 times (x 170B)
Class com.google.common.collect.Sets$ImprovedAbstractSet                              : loaded 2 times (x 131B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 2 times (x 73B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 138B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 2 times (x 119B)
Class kotlin.reflect.jvm.internal.CacheByClass                                        : loaded 2 times (x 69B)
Class it.unimi.dsi.fastutil.ints.IntSpliterator                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.config.MavenComparableVersion$IntegerItem                  : loaded 2 times (x 76B)
Class kotlin.reflect.KCallable                                                        : loaded 2 times (x 66B)
Class net.rubygrapefruit.platform.internal.Platform$MacOs64Bit                        : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableRangeSet$1                                   : loaded 2 times (x 205B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper$1           : loaded 2 times (x 72B)
Class kotlin.text.CharsKt                                                             : loaded 2 times (x 67B)
Class it.unimi.dsi.fastutil.objects.ObjectSpliterator                                 : loaded 2 times (x 66B)
Class com.google.common.base.Optional                                                 : loaded 2 times (x 76B)
Class com.google.common.collect.Range                                                 : loaded 2 times (x 83B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 66B)
Class com.google.common.collect.Iterators                                             : loaded 2 times (x 67B)
Class kotlin.text.DelimitedRangesSequence$iterator$1                                  : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 77B)
Class net.rubygrapefruit.platform.internal.Platform$Linux32Bit                        : loaded 2 times (x 79B)
Class com.google.common.collect.ExplicitOrdering                                      : loaded 2 times (x 111B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.config.MavenComparableVersion                              : loaded 2 times (x 74B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 77B)
Class net.rubygrapefruit.platform.Native                                              : loaded 2 times (x 67B)
Class net.rubygrapefruit.platform.NativeIntegration                                   : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSortedSetFauxverideShim                      : loaded 2 times (x 143B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class kotlin.jvm.JvmStatic                                                            : loaded 2 times (x 66B)
Class kotlin.reflect.jvm.internal.ReflectionFactoryImpl                               : loaded 2 times (x 87B)
Class it.unimi.dsi.fastutil.ints.IntArraySet                                          : loaded 2 times (x 231B)
Class kotlin.collections.MapsKt__MapsJVMKt                                            : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt___ArraysKt$asSequence$$inlined$Sequence$1           : loaded 2 times (x 71B)
Class kotlin.collections.EmptyList                                                    : loaded 2 times (x 170B)
Class com.google.common.io.ByteArrayDataInput                                         : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableBiMap$Builder                                : loaded 2 times (x 78B)
Class [Lnet.rubygrapefruit.platform.internal.FunctionResult$Failure;                  : loaded 2 times (x 65B)
Class com.google.common.collect.HashMultimapGwtSerializationDependencies              : loaded 2 times (x 170B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet$1                     : loaded 2 times (x 78B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 102B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 78B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class it.unimi.dsi.fastutil.longs.LongIterable                                        : loaded 2 times (x 66B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class kotlin.collections.builders.ListBuilderKt                                       : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 130B)
Class com.google.common.base.Objects                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 73B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class kotlin.reflect.jvm.internal.ClassValueCache                                     : loaded 2 times (x 70B)
Class kotlin.ResultKt                                                                 : loaded 2 times (x 67B)
Class com.google.common.base.Absent                                                   : loaded 2 times (x 76B)
Class com.google.common.collect.NullsFirstOrdering                                    : loaded 2 times (x 111B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIterator                                      : loaded 2 times (x 78B)
Class kotlin.Result$Failure                                                           : loaded 2 times (x 68B)
Class kotlin.collections.ArraysKt__ArraysKt                                           : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$SafeAtomicHelper               : loaded 2 times (x 75B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 168B)
Class com.google.common.collect.ObjectArrays                                          : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 2 times (x 92B)
Class kotlin.reflect.jvm.internal.KotlinReflectionInternalError                       : loaded 2 times (x 78B)
Class [Lorg.objectweb.asm.AnnotationVisitor;                                          : loaded 2 times (x 65B)
Class it.unimi.dsi.fastutil.objects.ObjectBidirectionalIterator                       : loaded 2 times (x 66B)
Class kotlin.collections.SetsKt__SetsJVMKt                                            : loaded 2 times (x 67B)
Class kotlin.Function                                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class kotlin.reflect.jvm.internal.KClassifierImpl                                     : loaded 2 times (x 66B)
Class kotlin.reflect.KMutableProperty                                                 : loaded 2 times (x 66B)
Class it.unimi.dsi.fastutil.ints.IntOpenHashSet                                       : loaded 2 times (x 235B)
Class kotlin.internal.ProgressionUtilKt                                               : loaded 2 times (x 67B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class kotlin.jvm.internal.PropertyReference                                           : loaded 2 times (x 111B)
Class org.jetbrains.kotlin.config.ApiVersion                                          : loaded 2 times (x 82B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 77B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.cli.common.CompilerSystemProperties                        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.config.LanguageVersion$Companion                           : loaded 2 times (x 67B)
Class com.google.common.io.Files$2                                                    : loaded 2 times (x 72B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 68B)
Class ijresolvers1_6vjy58gqenqkcdbfqfeideg52                                          : loaded 2 times (x 178B)
Class [Lkotlin.reflect.KAnnotatedElement;                                             : loaded 2 times (x 65B)
Class com.google.common.base.Present                                                  : loaded 2 times (x 77B)
Class net.rubygrapefruit.platform.internal.FunctionResult                             : loaded 2 times (x 73B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection$WrappedIterator: loaded 2 times (x 80B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 66B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class kotlin.jvm.internal.Reflection                                                  : loaded 2 times (x 67B)
Class kotlin.jvm.JvmClassMappingKt                                                    : loaded 2 times (x 67B)
Class kotlin.ranges.ClosedRange                                                       : loaded 2 times (x 66B)
Class kotlin.reflect.KProperty                                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.cli.common.messages.MessageCollector                       : loaded 2 times (x 66B)
Class com.google.common.reflect.Invokable$ConstructorInvokable                        : loaded 2 times (x 103B)
Class net.rubygrapefruit.platform.internal.DefaultProcessLauncher                     : loaded 2 times (x 71B)
Class net.rubygrapefruit.platform.NativeIntegrationLinkageException                   : loaded 2 times (x 78B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Multimap                                              : loaded 2 times (x 66B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class kotlin.collections.IntIterator                                                  : loaded 2 times (x 78B)
Class kotlin.reflect.jvm.internal.ComputableClassValue                                : loaded 2 times (x 76B)
Class kotlin.text.StringsKt__StringsJVMKt                                             : loaded 2 times (x 67B)
Class com.google.common.reflect.TypeToken                                             : loaded 2 times (x 69B)
Class com.google.common.base.ExtraObjectsMethodsForWeb                                : loaded 2 times (x 67B)
Class it.unimi.dsi.fastutil.longs.LongCollection                                      : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__IndentKt                                                 : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt                                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt___CollectionsKt$asSequence$$inlined$Sequence$1 : loaded 2 times (x 71B)
Class com.google.common.io.ByteSink                                                   : loaded 2 times (x 72B)
Class net.rubygrapefruit.platform.internal.jni.NativeLibraryFunctions                 : loaded 2 times (x 67B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 68B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 2 times (x 203B)
Class Settings_gradle$1                                                               : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 78B)
Class kotlin.reflect.KFunction                                                        : loaded 2 times (x 66B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class com.google.common.io.Files$FileByteSource                                       : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 2 times (x 204B)
Class [Lkotlin.reflect.KDeclarationContainer;                                         : loaded 2 times (x 65B)
Class kotlin.collections.MapsKt__MapsKt                                               : loaded 2 times (x 67B)
Class kotlin.jvm.internal.Lambda                                                      : loaded 2 times (x 71B)
Class it.unimi.dsi.fastutil.objects.ObjectIterable                                    : loaded 2 times (x 66B)
Class com.google.common.io.CharSource$CharSequenceCharSource                          : loaded 2 times (x 82B)
Class net.rubygrapefruit.platform.internal.Platform$Windows                           : loaded 2 times (x 75B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 70B)
Class com.google.common.util.concurrent.AbstractFuture$AtomicHelper                   : loaded 2 times (x 74B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 68B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 75B)
Class com.google.common.collect.Iterators$1                                           : loaded 2 times (x 77B)
Class kotlin.Metadata                                                                 : loaded 2 times (x 66B)
Class kotlin.collections.AbstractMutableSet                                           : loaded 2 times (x 138B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class kotlin.UninitializedPropertyAccessException                                     : loaded 2 times (x 78B)
Class com.google.common.collect.NaturalOrdering                                       : loaded 2 times (x 111B)
Class com.google.common.reflect.TypeToken$1                                           : loaded 2 times (x 106B)
Class com.google.common.cache.LocalCache$ComputingValueReference                      : loaded 2 times (x 92B)
Class kotlin.reflect.KProperty$Getter                                                 : loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt___SequencesKt$filterNotNull$1                      : loaded 2 times (x 74B)
Class com.google.common.reflect.TypeToken$2                                           : loaded 2 times (x 106B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 150B)
Class com.google.common.io.CharSource$ConcatenatedCharSource                          : loaded 2 times (x 82B)
Class kotlin.SafePublicationLazyImpl                                                  : loaded 2 times (x 72B)
Class it.unimi.dsi.fastutil.objects.ObjectIterator                                    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.cli.jvm.compiler.KotlinCoreEnvironment                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.utils.DescriptionAware                                     : loaded 2 times (x 66B)
Class [Lcom.google.common.base.Supplier;                                              : loaded 2 times (x 65B)
Class net.rubygrapefruit.platform.internal.LibraryDef                                 : loaded 2 times (x 68B)
Class com.google.common.collect.SortedIterable                                        : loaded 2 times (x 66B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 65B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 70B)
Class com.google.common.collect.Iterators$4                                           : loaded 2 times (x 78B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 168B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class it.unimi.dsi.fastutil.ints.IntIterable                                          : loaded 2 times (x 66B)
Class it.unimi.dsi.fastutil.ints.IntSet                                               : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 68B)
Class com.google.common.collect.Iterators$5                                           : loaded 2 times (x 78B)
Class kotlin.reflect.jvm.internal.KDeclarationContainerImpl                           : loaded 2 times (x 80B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsKt                         : loaded 2 times (x 67B)
Class kotlin.sequences.Sequence                                                       : loaded 2 times (x 66B)
Class Build_gradle$1$2$1                                                              : loaded 2 times (x 75B)
Class com.google.common.io.Closer$SuppressingSuppressor                               : loaded 2 times (x 71B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper             : loaded 2 times (x 74B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 71B)
Class kotlin.collections.CollectionsKt__CollectionsJVMKt                              : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__StringNumberConversionsJVMKt                             : loaded 2 times (x 67B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class net.rubygrapefruit.platform.internal.Platform$Window32Bit                       : loaded 2 times (x 75B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 79B)
Class kotlin.sequences.SequencesKt                                                    : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IteratorsKt                                   : loaded 2 times (x 67B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 74B)
Class com.google.common.collect.ComparatorOrdering                                    : loaded 2 times (x 111B)
Class kotlin.jvm.internal.ArrayIteratorKt                                             : loaded 2 times (x 67B)
Class kotlin.reflect.KProperty$Accessor                                               : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 66B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMutableSet                                         : loaded 2 times (x 66B)
Class kotlin.text.StringsKt                                                           : loaded 2 times (x 67B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 102B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 79B)
Class com.google.common.collect.Iterators$9                                           : loaded 2 times (x 77B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 135B)
Class kotlin.reflect.jvm.internal.CachesKt$CACHE_FOR_NULLABLE_BASE_CLASSIFIERS$1      : loaded 2 times (x 74B)
Class kotlin.LazyKt__LazyJVMKt                                                        : loaded 2 times (x 67B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 73B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 71B)
Class [Lkotlin.Pair;                                                                  : loaded 2 times (x 65B)
Class kotlin.jvm.internal.markers.KMutableList                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.cli.common.CompilerSystemProperties$value$1                : loaded 2 times (x 121B)
Class kotlin.text.StringsKt__StringNumberConversionsKt                                : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.config.LanguageVersion;                                  : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableRangeSet                                     : loaded 2 times (x 111B)
Class com.google.common.collect.UsingToStringOrdering                                 : loaded 2 times (x 110B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class [Lkotlin.sequences.Sequence;                                                    : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.cli.common.CompilerSystemProperties$value$2                : loaded 2 times (x 121B)
Class [Lorg.jetbrains.kotlin.config.LanguageOrApiVersion;                             : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableSortedSet                                    : loaded 2 times (x 295B)
Class kotlin.text.StringsKt__AppendableKt                                             : loaded 2 times (x 67B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 65B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class kotlin.reflect.jvm.internal.CachesKt$K_CLASS_CACHE$1                            : loaded 2 times (x 74B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class com.google.common.collect.ImmutableMap$SerializedForm                           : loaded 2 times (x 69B)
Class net.rubygrapefruit.platform.internal.Platform$Linux                             : loaded 2 times (x 79B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 95B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class kotlin.reflect.jvm.internal.CachesKt$CACHE_FOR_GENERIC_CLASSIFIERS$1            : loaded 2 times (x 74B)
Class it.unimi.dsi.fastutil.ints.IntListIterator                                      : loaded 2 times (x 66B)
Class kotlin.jvm.internal.CallableReference                                           : loaded 2 times (x 102B)
Class com.google.common.collect.Ordering                                              : loaded 2 times (x 110B)
Class com.google.common.collect.RegularImmutableSortedSet                             : loaded 2 times (x 295B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet                       : loaded 2 times (x 133B)
Class net.rubygrapefruit.platform.internal.Platform                                   : loaded 2 times (x 75B)
Class kotlin.jvm.internal.markers.KMutableMap                                         : loaded 2 times (x 66B)
Class kotlin.ranges.IntRange                                                          : loaded 2 times (x 89B)
Class net.rubygrapefruit.platform.NativeIntegrationUnavailableException               : loaded 2 times (x 78B)
Class [Lcom.google.common.collect.HashBiMap$BiEntry;                                  : loaded 2 times (x 65B)
Class com.google.common.collect.Iterators$10                                          : loaded 2 times (x 77B)
Class ijresolvers1_6vjy58gqenqkcdbfqfeideg52$_run_closure1                            : loaded 2 times (x 135B)
Class kotlin.sequences.SequencesKt___SequencesKt$flatMap$2                            : loaded 2 times (x 121B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class kotlin.enums.EnumEntries                                                        : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMultimap                                     : loaded 2 times (x 143B)
Class com.google.common.base.Predicates                                               : loaded 2 times (x 67B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 78B)
Class kotlin.jvm.internal.markers.KMutableListIterator                                : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.cli.common.CompilerSystemProperties;                     : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 78B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 2 times (x 208B)
Class kotlin.reflect.jvm.internal.KDeclarationContainerImpl$Companion                 : loaded 2 times (x 67B)
Class com.google.common.collect.ReverseNaturalOrdering                                : loaded 2 times (x 110B)
Class com.android.sdklib.AndroidVersion                                               : loaded 2 times (x 71B)
Class org.gradle.kotlin.dsl.TestImplementationConfigurationAccessorsKt                : loaded 2 times (x 67B)
Class net.rubygrapefruit.platform.internal.Platform$Unsupported                       : loaded 2 times (x 75B)
Class net.rubygrapefruit.platform.internal.Platform$FreeBSD32Bit                      : loaded 2 times (x 78B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class kotlin.jvm.internal.PropertyReference0                                          : loaded 2 times (x 123B)
Class kotlin.sequences.EmptySequence                                                  : loaded 2 times (x 74B)
Class it.unimi.dsi.fastutil.Hash                                                      : loaded 2 times (x 66B)
Class net.rubygrapefruit.platform.internal.Platform$Unix                              : loaded 2 times (x 78B)
Class com.google.common.collect.ComparisonChain                                       : loaded 2 times (x 76B)
Class com.google.common.base.Charsets                                                 : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 67B)
Class kotlin.jvm.internal.DefaultConstructorMarker                                    : loaded 2 times (x 67B)
Class it.unimi.dsi.fastutil.ints.IntList                                              : loaded 2 times (x 66B)
Class kotlin.jvm.internal.PropertyReference1                                          : loaded 2 times (x 123B)
Class com.google.common.io.ByteSource$ConcatenatedByteSource                          : loaded 2 times (x 81B)
Class kotlin.collections.AbstractList$Companion                                       : loaded 2 times (x 67B)
Class com.google.common.io.CharSource                                                 : loaded 2 times (x 81B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class kotlin.UnsafeLazyImpl                                                           : loaded 2 times (x 72B)
Class kotlin.jvm.internal.PropertyReference2                                          : loaded 2 times (x 123B)
Class kotlin.jvm.internal.MutablePropertyReference                                    : loaded 2 times (x 115B)
Class kotlin.sequences.SequencesKt___SequencesKt                                      : loaded 2 times (x 67B)
Class kotlin.LazyKt__LazyJVMKt$WhenMappings                                           : loaded 2 times (x 67B)
Class kotlin.LazyThreadSafetyMode                                                     : loaded 2 times (x 75B)
Class kotlin.jvm.internal.ArrayIterator                                               : loaded 2 times (x 75B)
Class it.unimi.dsi.fastutil.HashCommon                                                : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 92B)
Class kotlin.jvm.internal.SpreadBuilder                                               : loaded 2 times (x 72B)
Class kotlin.collections.EmptyIterator                                                : loaded 2 times (x 85B)
Class kotlin.text.StringsKt__RegexExtensionsKt                                        : loaded 2 times (x 67B)
Class kotlin.SynchronizedLazyImpl                                                     : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.config.LanguageVersionSettings                             : loaded 2 times (x 66B)
Class com.google.common.reflect.Invokable$MethodInvokable                             : loaded 2 times (x 103B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 67B)
Class kotlin.reflect.jvm.internal.CachesKt$K_PACKAGE_CACHE$1                          : loaded 2 times (x 74B)
Class kotlin.sequences.SequencesKt__SequencesJVMKt                                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.jvm.compiler.KotlinCoreEnvironment$Companion           : loaded 2 times (x 67B)
Class kotlin.collections.AbstractList                                                 : loaded 2 times (x 193B)
Class kotlin.collections.AbstractCollection                                           : loaded 2 times (x 113B)
Class Build_gradle$2                                                                  : loaded 2 times (x 70B)
Class com.google.common.collect.CompoundOrdering                                      : loaded 2 times (x 111B)
Class [Lorg.objectweb.asm.AnnotationWriter;                                           : loaded 2 times (x 65B)
Class com.google.common.primitives.Ints$IntConverter                                  : loaded 2 times (x 86B)
Class kotlin.Triple                                                                   : loaded 2 times (x 68B)
Class kotlin.text.Regex                                                               : loaded 2 times (x 68B)
Class kotlin.text.StringsKt__StringBuilderJVMKt                                       : loaded 2 times (x 67B)
Class kotlin.collections.ArrayAsCollection                                            : loaded 2 times (x 101B)
Class kotlin.LazyKt__LazyKt                                                           : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableRangeSet$Builder                             : loaded 2 times (x 73B)
Class com.google.common.collect.Lists$ReverseList$1                                   : loaded 2 times (x 95B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 86B)
Class com.google.common.util.concurrent.AbstractFuture$SynchronizedHelper             : loaded 2 times (x 74B)
Class kotlin.jvm.internal.Ref$ObjectRef                                               : loaded 2 times (x 68B)
Class kotlin.jvm.internal.ReflectionFactory                                           : loaded 2 times (x 87B)
Class it.unimi.dsi.fastutil.ints.AbstractIntSet                                       : loaded 2 times (x 229B)
Class kotlin.UNINITIALIZED_VALUE                                                      : loaded 2 times (x 67B)
Class com.google.common.graph.SuccessorsFunction                                      : loaded 2 times (x 66B)
Class net.rubygrapefruit.platform.internal.WrapperProcessLauncher                     : loaded 2 times (x 72B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class kotlin.jvm.internal.ClassBasedDeclarationContainer                              : loaded 2 times (x 66B)
Class kotlin.coroutines.Continuation                                                  : loaded 2 times (x 66B)
Class kotlin.ranges.RangesKt__RangesKt                                                : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt___SetsKt                                              : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt__SequencesKt                                       : loaded 2 times (x 67B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 67B)
Class org.gradle.kotlin.dsl.AndroidTestImplementationConfigurationAccessorsKt         : loaded 2 times (x 67B)
Class com.google.common.io.Files$FileByteSink                                         : loaded 2 times (x 73B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 75B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 65B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class it.unimi.dsi.fastutil.ints.IntArrays                                            : loaded 2 times (x 67B)
Class kotlin.Result$Companion                                                         : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.BaseContinuationImpl                             : loaded 2 times (x 83B)
Class kotlin.text.CharsKt__CharJVMKt                                                  : loaded 2 times (x 67B)
Class it.unimi.dsi.fastutil.Hash$Strategy                                             : loaded 2 times (x 66B)
Class kotlin.sequences.FilteringSequence                                              : loaded 2 times (x 71B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 65B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 69B)
Class kotlin.reflect.KType                                                            : loaded 2 times (x 66B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.config.ApiVersion$Companion                                : loaded 2 times (x 67B)
Class [Lkotlin.reflect.KClass;                                                        : loaded 2 times (x 65B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 2 times (x 91B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 84B)
Class com.google.common.io.Closer                                                     : loaded 2 times (x 74B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class kotlin.jvm.internal.FunctionReferenceImpl                                       : loaded 2 times (x 118B)
Class kotlin.text.DelimitedRangesSequence                                             : loaded 2 times (x 71B)
Class kotlin.collections.CollectionsKt___CollectionsJvmKt                             : loaded 2 times (x 67B)
Class com.google.common.io.ByteSource$EmptyByteSource                                 : loaded 2 times (x 81B)
Class net.rubygrapefruit.platform.internal.Platform$MacOs32Bit                        : loaded 2 times (x 78B)
Class net.rubygrapefruit.platform.internal.Platform$Linux64Bit                        : loaded 2 times (x 79B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 75B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 67B)
Class kotlin.jvm.internal.markers.KMutableIterable                                    : loaded 2 times (x 66B)
Class kotlin.Unit                                                                     : loaded 2 times (x 67B)
Class kotlin.TuplesKt                                                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.config.MavenComparableVersion$ListItem                     : loaded 2 times (x 214B)
Class kotlin.sequences.SequencesKt___SequencesJvmKt                                   : loaded 2 times (x 67B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 65B)
Class com.google.common.io.LineProcessor                                              : loaded 2 times (x 66B)
Class com.google.common.io.Closer$Suppressor                                          : loaded 2 times (x 66B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 74B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 68B)
Class Settings_gradle$1$1                                                             : loaded 2 times (x 70B)
Class kotlin.ranges.RangesKt                                                          : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.utils.DescriptionAware;                                  : loaded 2 times (x 65B)
Class [Lorg.objectweb.asm.Label;                                                      : loaded 2 times (x 65B)
Class com.google.common.collect.ForwardingSet                                         : loaded 2 times (x 147B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 73B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class kotlin.sequences.GeneratorSequence                                              : loaded 2 times (x 71B)
Class kotlin.reflect.jvm.internal.KClassImpl                                          : loaded 2 times (x 111B)
Class kotlin.sequences.FlatteningSequence$iterator$1                                  : loaded 2 times (x 75B)
Class kotlin.collections.ArraysUtilJVM                                                : loaded 2 times (x 67B)
Class kotlin.jvm.functions.Function0                                                  : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableList$SerializedForm                          : loaded 2 times (x 69B)
Class com.google.common.io.CharSource$AsByteSource                                    : loaded 2 times (x 81B)
Class net.rubygrapefruit.platform.internal.FunctionResult$Failure                     : loaded 2 times (x 75B)
Class net.rubygrapefruit.platform.internal.NativeLibraryLoader                        : loaded 2 times (x 69B)
Class com.google.common.util.concurrent.AbstractFuture$Waiter                         : loaded 2 times (x 68B)
Class Settings_gradle                                                                 : loaded 2 times (x 124B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 67B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 66B)
Class kotlin.collections.AbstractMutableList                                          : loaded 2 times (x 202B)
Class kotlin.jvm.functions.Function1                                                  : loaded 2 times (x 66B)
Class com.google.common.reflect.TypeCapture                                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.messages.GradleStyleMessageRenderer             : loaded 2 times (x 74B)
Class it.unimi.dsi.fastutil.objects.ReferenceSet                                      : loaded 2 times (x 66B)
Class com.google.common.collect.HashBiMap                                             : loaded 2 times (x 139B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 68B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 68B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 67B)
Class org.gradle.internal.agents.InstrumentingClassLoader                             : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMutableIterator                                    : loaded 2 times (x 66B)
Class com.android.SdkConstants                                                        : loaded 2 times (x 67B)
Class kotlin.sequences.TransformingSequence                                           : loaded 2 times (x 71B)
Class kotlin.KotlinNothingValueException                                              : loaded 2 times (x 78B)
Class kotlin.jvm.functions.Function2                                                  : loaded 2 times (x 66B)
Class kotlin.collections.builders.ListBuilder$Itr                                     : loaded 2 times (x 90B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 65B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 81B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.utils.exceptions.KotlinExceptionWithAttachments            : loaded 2 times (x 66B)
Class kotlin.jvm.functions.Function3                                                  : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt___MapsJvmKt                                           : loaded 2 times (x 67B)
Class net.rubygrapefruit.platform.NativeException                                     : loaded 2 times (x 78B)
Class com.google.common.collect.AbstractRangeSet                                      : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 183B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 68B)
Class kotlin.annotation.Target                                                        : loaded 2 times (x 66B)
Class com.google.common.io.ByteArrayDataOutput                                        : loaded 2 times (x 66B)
Class kotlin.ranges.IntProgression$Companion                                          : loaded 2 times (x 67B)
Class kotlin.collections.EmptySet                                                     : loaded 2 times (x 118B)
Class org.jetbrains.kotlin.config.LanguageVersion                                     : loaded 2 times (x 87B)
Class kotlin.collections.CollectionsKt___CollectionsKt                                : loaded 2 times (x 67B)
Class kotlin.collections.builders.MapBuilder                                          : loaded 2 times (x 108B)
Class kotlin.jvm.internal.MutablePropertyReference2                                   : loaded 2 times (x 133B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 168B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 78B)
Class kotlin.annotation.Retention                                                     : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class kotlin.text.StringsKt___StringsJvmKt                                            : loaded 2 times (x 67B)
Class kotlin.reflect.KDeclarationContainer                                            : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 2 times (x 93B)
Class com.google.common.io.CharSource$EmptyCharSource                                 : loaded 2 times (x 82B)
Class com.google.common.collect.ForwardingMap                                         : loaded 2 times (x 128B)
Class kotlin.reflect.jvm.internal.KTypeParameterOwnerImpl                             : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt___MapsKt                                              : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt___ArraysKt                                          : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 78B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection            : loaded 2 times (x 120B)
Class kotlin.collections.MapsKt__MapWithDefaultKt                                     : loaded 2 times (x 67B)
Class it.unimi.dsi.fastutil.objects.ObjectSet                                         : loaded 2 times (x 66B)
Class Build_gradle$1$1                                                                : loaded 2 times (x 75B)
Class com.google.common.io.Files                                                      : loaded 2 times (x 67B)
Class net.rubygrapefruit.platform.internal.NativeLibraryLocator                       : loaded 2 times (x 69B)
Class net.rubygrapefruit.platform.Process                                             : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 79B)
Class kotlin.collections.builders.MapBuilder$Companion                                : loaded 2 times (x 67B)
Class kotlin.text.StringsKt___StringsKt                                               : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__MutableCollectionsJVMKt                       : loaded 2 times (x 67B)
Class kotlin.enums.EnumEntriesKt                                                      : loaded 2 times (x 67B)
Class net.rubygrapefruit.platform.internal.Platform$FreeBSD                           : loaded 2 times (x 78B)
Class kotlin.jvm.internal.PropertyReference1Impl                                      : loaded 2 times (x 123B)
Class Build_gradle$1$2                                                                : loaded 2 times (x 75B)
Class com.google.common.reflect.TypeParameter                                         : loaded 2 times (x 68B)
Class com.google.common.collect.NullsLastOrdering                                     : loaded 2 times (x 111B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 103B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.utils.KotlinExceptionWithAttachments                       : loaded 2 times (x 90B)
Class kotlin.annotation.MustBeDocumented                                              : loaded 2 times (x 66B)
Class [Lkotlin.reflect.KClassifier;                                                   : loaded 2 times (x 65B)
Class kotlin.reflect.jvm.internal.EmptyContainerForLocal                              : loaded 2 times (x 80B)
Class [Lkotlin.LazyThreadSafetyMode;                                                  : loaded 2 times (x 65B)
Class kotlin.SafePublicationLazyImpl$Companion                                        : loaded 2 times (x 67B)
Class kotlin.jvm.internal.MutablePropertyReference1                                   : loaded 2 times (x 133B)
Class Build_gradle$1$3                                                                : loaded 2 times (x 74B)
Class com.google.common.collect.ImmutableRangeSet$ComplementRanges                    : loaded 2 times (x 203B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 68B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 78B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class it.unimi.dsi.fastutil.ints.IntSets                                              : loaded 2 times (x 67B)
Class kotlin.jvm.internal.markers.KMutableCollection                                  : loaded 2 times (x 66B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt                                       : loaded 2 times (x 67B)
Class kotlin.ranges.IntRange$Companion                                                : loaded 2 times (x 67B)
Class kotlin.collections.EmptyMap                                                     : loaded 2 times (x 105B)
Class kotlin.reflect.KAnnotatedElement                                                : loaded 2 times (x 66B)
Class kotlin.jvm.internal.Intrinsics                                                  : loaded 2 times (x 67B)
Class Build_gradle$1$4                                                                : loaded 2 times (x 74B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 104B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 110B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt__SequencesKt$flatten$1                             : loaded 2 times (x 74B)
Class kotlin.coroutines.intrinsics.CoroutineSingletons                                : loaded 2 times (x 75B)
Class kotlin.collections.MapsKt                                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IterablesKt                                   : loaded 2 times (x 67B)
Class com.android.sdklib.AndroidVersion$AndroidVersionException                       : loaded 2 times (x 78B)
Class it.unimi.dsi.fastutil.objects.ObjectCollection                                  : loaded 2 times (x 66B)
Class org.gradle.kotlin.dsl.ImplementationConfigurationAccessorsKt                    : loaded 2 times (x 67B)
Class com.google.common.io.ByteStreams$LimitedInputStream                             : loaded 2 times (x 88B)
Class net.rubygrapefruit.platform.ProcessLauncher                                     : loaded 2 times (x 66B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 102B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 65B)
Class kotlin.ranges.OpenEndRange                                                      : loaded 2 times (x 66B)
Class kotlin.text.Charsets                                                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.CompilerSystemProperties$Companion              : loaded 2 times (x 67B)
Class kotlin.KotlinNullPointerException                                               : loaded 2 times (x 79B)
Class kotlin.sequences.SequencesKt__SequencesKt$flatten$3                             : loaded 2 times (x 74B)
Class kotlin.ranges.RangesKt___RangesKt                                               : loaded 2 times (x 67B)
Class com.google.common.collect.SortedSetMultimap                                     : loaded 2 times (x 66B)
Class net.rubygrapefruit.platform.internal.Platform$FreeBSD64Bit                      : loaded 2 times (x 78B)
Class com.google.common.collect.RegularImmutableBiMap                                 : loaded 2 times (x 144B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 66B)
Class it.unimi.dsi.fastutil.ints.IntArrays$Segment                                    : loaded 2 times (x 67B)
Class it.unimi.dsi.fastutil.ints.IntBidirectionalIterator                             : loaded 2 times (x 66B)
Class kotlin.text.CharsKt__CharKt                                                     : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt                                                     : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__ReversedViewsKt                               : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt__SequenceBuilderKt                                 : loaded 2 times (x 67B)
Class com.google.common.hash.PrimitiveSink                                            : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 68B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 78B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 66B)
Class kotlin.reflect.jvm.internal.CacheByClassKt                                      : loaded 2 times (x 67B)
Class kotlin.coroutines.EmptyCoroutineContext                                         : loaded 2 times (x 73B)
Class kotlin.collections.SetsKt__SetsKt                                               : loaded 2 times (x 67B)
Class kotlin.LazyKt                                                                   : loaded 2 times (x 67B)
Class kotlin.sequences.FilteringSequence$iterator$1                                   : loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableSetMultimap                                  : loaded 2 times (x 176B)
Class com.google.common.collect.ByFunctionOrdering                                    : loaded 2 times (x 111B)
Class com.google.common.base.Joiner$1                                                 : loaded 2 times (x 76B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 69B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 79B)
Class com.google.common.collect.PeekingIterator                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.config.MavenComparableVersion$Item                         : loaded 2 times (x 66B)
Class kotlin.collections.ArraysKt__ArraysJVMKt                                        : loaded 2 times (x 67B)
Class kotlin.jvm.internal.CollectionToArray                                           : loaded 2 times (x 67B)
Class it.unimi.dsi.fastutil.Stack                                                     : loaded 2 times (x 66B)
Class com.google.common.collect.Maps$IteratorBasedAbstractMap                         : loaded 2 times (x 121B)
Class com.google.common.io.ByteSource$AsCharSource                                    : loaded 2 times (x 82B)
Class com.google.common.collect.RangeGwtSerializationDependencies                     : loaded 2 times (x 67B)
Class com.google.common.base.Joiner$2                                                 : loaded 2 times (x 75B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 101B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 65B)
Class kotlin.reflect.jvm.internal.CachesKt                                            : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__StringBuilderKt                                          : loaded 2 times (x 67B)
Class kotlin.jvm.internal.CallableReference$NoReceiver                                : loaded 2 times (x 67B)
Class com.google.common.collect.RangeSet                                              : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 79B)
Class kotlin.jvm.internal.MutablePropertyReference0                                   : loaded 2 times (x 133B)
Class kotlin.Lazy                                                                     : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class kotlin.NoWhenBranchMatchedException                                             : loaded 2 times (x 78B)
Class com.google.common.collect.CollectCollectors                                     : loaded 2 times (x 67B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 79B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.cli.common.PropertiesKt                                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.messages.MessageRenderer                        : loaded 2 times (x 66B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 78B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 79B)
Class net.rubygrapefruit.platform.internal.Platform$MacOs                             : loaded 2 times (x 78B)
Class net.rubygrapefruit.platform.internal.Platform$Window64Bit                       : loaded 2 times (x 75B)
Class com.google.common.collect.SetMultimap                                           : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 79B)
Class com.google.common.io.ByteStreams$1                                              : loaded 2 times (x 81B)
Class com.google.common.collect.ReverseOrdering                                       : loaded 2 times (x 111B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 79B)
Class kotlin.reflect.jvm.internal.CachesKt$CACHE_FOR_BASE_CLASSIFIERS$1               : loaded 2 times (x 74B)
Class kotlin.jvm.internal.markers.KMappedMarker                                       : loaded 2 times (x 66B)
Class kotlin.NotImplementedError                                                      : loaded 2 times (x 78B)
Class com.google.common.collect.HashMultimap                                          : loaded 2 times (x 170B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 79B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__StringsKt                                                : loaded 2 times (x 67B)
Class kotlin.sequences.DropTakeSequence                                               : loaded 2 times (x 66B)
Class com.google.common.io.ByteSource$SlicedByteSource                                : loaded 2 times (x 81B)
Class net.rubygrapefruit.platform.internal.jni.WindowsHandleFunctions                 : loaded 2 times (x 67B)
Class org.objectweb.asm.ClassReader                                                   : loaded 2 times (x 89B)
Class com.google.common.collect.Maps$KeySet                                           : loaded 2 times (x 133B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 79B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class kotlin.reflect.KMutableProperty0                                                : loaded 2 times (x 66B)
Class kotlin.reflect.KClassifier                                                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.util.capitalizeDecapitalize.CapitalizeDecapitalizeKt       : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.SuspendFunction                                  : loaded 2 times (x 66B)
Class kotlin.sequences.FlatteningSequence                                             : loaded 2 times (x 71B)
Class com.google.common.collect.ComparisonChain$InactiveComparisonChain               : loaded 2 times (x 76B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 79B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class kotlin.reflect.KMutableProperty1                                                : loaded 2 times (x 66B)
Class kotlin.jvm.internal.Ref$BooleanRef                                              : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt                      : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.CoroutineStackFrame                              : loaded 2 times (x 66B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 71B)
Class com.google.common.collect.ForwardingObject                                      : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 2 times (x 204B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class kotlin.reflect.KMutableProperty2                                                : loaded 2 times (x 66B)
Class kotlin.collections.CollectionsKt                                                : loaded 2 times (x 67B)
Class com.google.common.io.ByteSource                                                 : loaded 2 times (x 80B)
Class com.google.common.util.concurrent.AbstractFuture$SetFuture                      : loaded 2 times (x 71B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 66B)
Class kotlin.ranges.IntProgressionIterator                                            : loaded 2 times (x 78B)
Class kotlin.coroutines.jvm.internal.DebugProbesKt                                    : loaded 2 times (x 67B)
Class kotlin.coroutines.CoroutineContext                                              : loaded 2 times (x 66B)
Class com.google.common.collect.Lists$ReverseList                                     : loaded 2 times (x 196B)
Class org.objectweb.asm.Context                                                       : loaded 2 times (x 68B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 2 times (x 77B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)
Class it.unimi.dsi.fastutil.ints.IntSets$EmptySet                                     : loaded 2 times (x 230B)
Class com.google.common.collect.ForwardingCollection                                  : loaded 2 times (x 126B)
Class com.google.common.collect.LexicographicalOrdering                               : loaded 2 times (x 111B)
Class com.google.common.collect.Iterables                                             : loaded 2 times (x 67B)
Class kotlin.ranges.IntProgression                                                    : loaded 2 times (x 77B)
Class kotlin.jvm.KotlinReflectionNotSupportedError                                    : loaded 2 times (x 78B)
Class com.google.common.collect.Lists$RandomAccessReverseList                         : loaded 2 times (x 196B)

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
OS uptime: 33 days 17:49 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 12 processors :
  Max Mhz: 2901, Current Mhz: 2901, Mhz Limit: 2901

Memory: 4k page, system-wide physical 32660M (7806M free)
TotalPageFile size 64091M (AvailPageFile size 113M)
current process WorkingSet (physical memory assigned to process): 719M, peak: 722M
current process commit charge ("private bytes"): 758M, peak: 763M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+-13368085-b895.109) for windows-amd64 JRE (21.0.6+-13368085-b895.109), built on 2025-04-16T17:01:31Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
