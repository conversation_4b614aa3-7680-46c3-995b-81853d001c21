# RiderService SDK

## 项目概述

RiderService SDK 是一个用于 Wi-Fi P2P 连接管理的 Android 库，提供稳定可靠的设备间直连功能。

## 核心组件

### SoftP2pManagerNew 优化说明

SoftP2pManagerNew 是 SoftP2pManager 的优化版本，主要修复了连接不稳定的问题：

#### 主要优化内容

1. **简化设备发现流程**
   - 移除了过度复杂的设备稳定性检查
   - 采用即时连接策略，发现目标设备后立即尝试连接
   - 减少了不必要的延迟和等待时间

2. **优化超时配置**
   - 设备发现超时：从 15 秒减少到 8 秒
   - 连接建立超时：从 30 秒减少到 15 秒
   - 重试间隔：发现重试从 10 秒减少到 5 秒，连接重试从 5 秒减少到 3 秒

3. **改进连接处理**
   - 简化设备状态检查，允许更多状态下尝试连接
   - 优化连接失败重试逻辑
   - 移除了过度的组清理步骤

4. **增强响应速度**
   - 定期 peer 请求间隔从 2 秒减少到 1.5 秒
   - 增加 peer 请求次数从 6 次到 8 次
   - 在发现停止时立即请求 peer 列表

#### 与原版本的兼容性

- 保持了与 SoftP2pManager 相同的接口和回调机制
- 状态管理逻辑保持一致
- 所有外部依赖保持不变

## 使用方式

```kotlin
val p2pManager = SoftP2pManagerNew(object : SoftIP2pListener {
    override fun onWifiConnectSuccess() {
        // 连接成功处理
    }
    
    override fun onWifiDisconnect() {
        // 连接断开处理
    }
    
    override fun requestWifiInfo() {
        // 请求重置 Wi-Fi 信息
    }
    
    // 实现其他必要的回调方法...
})

// 启动连接
p2pManager.start(targetAddress, targetPort)

// 停止连接
p2pManager.stop()

// 销毁资源
p2pManager.destroy()
```

## 依赖要求

- Android API Level 16+
- Wi-Fi Direct 硬件支持
- 必要的权限：`ACCESS_WIFI_STATE`, `CHANGE_WIFI_STATE`, `ACCESS_FINE_LOCATION`

## 注意事项

1. 确保在使用前检查设备是否支持 Wi-Fi Direct
2. 需要在 Android 6.0+ 设备上动态申请位置权限
3. 建议在主线程中调用 start/stop 方法
4. 记得在不需要时调用 destroy() 方法释放资源 