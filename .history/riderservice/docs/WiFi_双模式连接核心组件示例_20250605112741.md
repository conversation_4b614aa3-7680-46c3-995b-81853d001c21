# WiFi双模式连接核心组件代码示例（简化版）

## ConnectionModeManager - 连接模式管理器

这个文档展示了简化版的双模式连接管理器实现，移除自动回退逻辑，由上层控制模式切换。

```kotlin
package com.link.riderservice.connection

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.link.riderservice.api.RiderService
import com.link.riderservice.ble.SoftP2pManager
import com.link.riderservice.connection.network.WiFiClientManager
import com.link.riderservice.connection.network.WiFiClientManagerListener
import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 连接模式管理器（简化版）
 * 提供清晰的API接口，模式切换由上层控制
 */
class ConnectionModeManager(
    private val listener: ConnectionModeManagerListener
) {
    private val context = RiderService.instance.getApplication().applicationContext
    private val preferences: SharedPreferences by lazy {
        context.getSharedPreferences("wifi_connection_mode", Context.MODE_PRIVATE)
    }
    
    private var currentMode: WifiConnectionMode? = null
    private var isConnecting = AtomicBoolean(false)
    private var connectionJob: Job? = null
    
    // WiFi AP客户端管理器
    private val wifiClientManager: WiFiClientManager by lazy {
        WiFiClientManager(wifiClientListener)
    }
    
    // WiFi P2P管理器（复用现有）
    private val p2pManager: SoftP2pManager by lazy {
        SoftP2pManager(p2pListener)
    }
    
    companion object {
        private const val TAG = "ConnectionModeManager"
        private const val PREF_DEFAULT_MODE = "default_mode"
        private const val CONNECTION_TIMEOUT_MS = 30000L
    }

    /**
     * 主要API：使用指定模式连接
     */
    fun connectWithMode(mode: WifiConnectionMode, context: Context) {
        if (isConnecting.get()) {
            Log.w(TAG, "Connection already in progress")
            listener.onModeConnectionFailed(mode, -1)
            return
        }
        
        Log.d(TAG, "Connecting with mode: $mode")
        isConnecting.set(true)
        currentMode = mode
        
        listener.onModeConnecting(mode)
        
        connectionJob = CoroutineScope(Dispatchers.Main).launch {
            try {
                when (mode) {
                    WifiConnectionMode.WIFI_AP_CLIENT -> {
                        connectApMode()
                    }
                    WifiConnectionMode.WIFI_P2P -> {
                        connectP2pMode()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Connection failed for mode: $mode", e)
                handleConnectionFailure(mode, -999)
            }
        }
    }
    
    /**
     * 断开当前连接
     */
    fun disconnect() {
        Log.d(TAG, "Disconnecting current mode: $currentMode")
        
        connectionJob?.cancel()
        isConnecting.set(false)
        
        when (currentMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                wifiClientManager.disconnect()
            }
            WifiConnectionMode.WIFI_P2P -> {
                p2pManager.stop()
            }
            null -> {
                // 都断开以确保清理
                wifiClientManager.disconnect()
                p2pManager.stop()
            }
        }
        
        currentMode = null
    }
    
    /**
     * 获取当前连接模式
     */
    fun getCurrentMode(): WifiConnectionMode? {
        return currentMode
    }
    
    /**
     * 获取支持的模式列表
     */
    fun getSupportedModes(): List<WifiConnectionMode> {
        return listOf(
            WifiConnectionMode.WIFI_AP_CLIENT,
            WifiConnectionMode.WIFI_P2P
        )
    }
    
    /**
     * 检查指定模式是否可用
     */
    fun isModeAvailable(mode: WifiConnectionMode): Boolean {
        return when (mode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                // 检查WiFi权限和状态
                wifiClientManager.checkPermissions() && wifiClientManager.isWifiEnabled()
            }
            WifiConnectionMode.WIFI_P2P -> {
                // 检查P2P是否可用
                p2pManager.isP2pSupported()
            }
        }
    }
    
    /**
     * 设置默认连接模式
     */
    fun setDefaultMode(mode: WifiConnectionMode) {
        preferences.edit()
            .putString(PREF_DEFAULT_MODE, mode.name)
            .apply()
        Log.d(TAG, "Default mode set to: $mode")
    }
    
    /**
     * 获取默认连接模式
     */
    fun getDefaultMode(): WifiConnectionMode {
        val modeName = preferences.getString(PREF_DEFAULT_MODE, WifiConnectionMode.WIFI_AP_CLIENT.name)
        return try {
            WifiConnectionMode.valueOf(modeName!!)
        } catch (e: Exception) {
            WifiConnectionMode.WIFI_AP_CLIENT
        }
    }
    
    /**
     * AP模式连接
     */
    private suspend fun connectApMode() {
        withTimeout(CONNECTION_TIMEOUT_MS) {
            suspendCancellableCoroutine<Unit> { continuation ->
                // 请求仪表端WiFi热点信息
                requestWifiApInfo { ssid, password ->
                    if (ssid.isNotEmpty()) {
                        wifiClientManager.connectToWifi(ssid, password)
                        // 连接结果通过wifiClientListener回调处理
                    } else {
                        continuation.resumeWith(Result.failure(Exception("Invalid WiFi info")))
                    }
                }
            }
        }
    }
    
    /**
     * P2P模式连接
     */
    private suspend fun connectP2pMode() {
        withTimeout(CONNECTION_TIMEOUT_MS) {
            suspendCancellableCoroutine<Unit> { continuation ->
                // 启动P2P连接
                p2pManager.startConnection()
                // 连接结果通过p2pListener回调处理
            }
        }
    }
    
    /**
     * WiFi客户端监听器
     */
    private val wifiClientListener = object : WiFiClientManagerListener {
        override fun onWifiConnecting(ssid: String) {
            Log.d(TAG, "WiFi connecting to: $ssid")
        }
        
        override fun onWifiConnected(ssid: String, ipAddress: String) {
            Log.d(TAG, "WiFi connected: $ssid, IP: $ipAddress")
            isConnecting.set(false)
            
            val connectionInfo = ConnectionInfo(
                mode = WifiConnectionMode.WIFI_AP_CLIENT,
                ssid = ssid,
                ipAddress = ipAddress
            )
            listener.onModeConnected(WifiConnectionMode.WIFI_AP_CLIENT, connectionInfo)
        }
        
        override fun onWifiDisconnected() {
            Log.d(TAG, "WiFi disconnected")
            if (currentMode == WifiConnectionMode.WIFI_AP_CLIENT) {
                listener.onModeDisconnected(WifiConnectionMode.WIFI_AP_CLIENT)
            }
        }
        
        override fun onWifiConnectionFailed(reason: Int) {
            Log.e(TAG, "WiFi connection failed: $reason")
            handleConnectionFailure(WifiConnectionMode.WIFI_AP_CLIENT, reason)
        }
        
        override fun onNetworkScanComplete(networks: List<android.net.wifi.ScanResult>) {
            Log.d(TAG, "Network scan completed: ${networks.size} networks found")
        }
        
        override fun requestWifiInfo() {
            // 通过消息管理器请求WiFi信息
            requestWifiApInfo { _, _ -> }
        }
        
        override fun onWifiState(opened: Boolean) {
            Log.d(TAG, "WiFi state changed: $opened")
        }
    }
    
    /**
     * P2P监听器（适配现有SoftP2pListener）
     */
    private val p2pListener = object : SoftP2pListener {
        override fun onP2pConnected(address: String) {
            Log.d(TAG, "P2P connected: $address")
            isConnecting.set(false)
            
            val connectionInfo = ConnectionInfo(
                mode = WifiConnectionMode.WIFI_P2P,
                peerAddress = address
            )
            listener.onModeConnected(WifiConnectionMode.WIFI_P2P, connectionInfo)
        }
        
        override fun onP2pDisconnected() {
            Log.d(TAG, "P2P disconnected")
            if (currentMode == WifiConnectionMode.WIFI_P2P) {
                listener.onModeDisconnected(WifiConnectionMode.WIFI_P2P)
            }
        }
        
        override fun onP2pConnectionFailed(reason: Int) {
            Log.e(TAG, "P2P connection failed: $reason")
            handleConnectionFailure(WifiConnectionMode.WIFI_P2P, reason)
        }
        
        // 其他P2P回调方法...
    }
    
    /**
     * 处理连接失败
     */
    private fun handleConnectionFailure(mode: WifiConnectionMode, reason: Int) {
        isConnecting.set(false)
        currentMode = null
        connectionJob?.cancel()
        
        Log.e(TAG, "Connection failed for mode $mode, reason: $reason")
        listener.onModeConnectionFailed(mode, reason)
    }
    
    /**
     * 请求仪表端WiFi AP信息
     */
    private fun requestWifiApInfo(callback: (String, String) -> Unit) {
        // 实现BLE消息发送，请求仪表端WiFi热点信息
        // 具体实现参考现有的消息管理器
        Log.d(TAG, "Requesting WiFi AP info from instrument")
        
        // 模拟异步回调
        CoroutineScope(Dispatchers.Main).launch {
            delay(100) // 模拟网络延迟
            // 实际实现中应该通过BLE消息获取
            callback("InstrumentAP_12345", "password123")
        }
    }
    
    /**
     * 销毁资源
     */
    fun destroy() {
        disconnect()
        connectionJob?.cancel()
        wifiClientManager.destroy()
        p2pManager.destroy()
    }
}

/**
 * 连接模式枚举
 */
enum class WifiConnectionMode {
    WIFI_AP_CLIENT,  // AP客户端模式
    WIFI_P2P         // P2P直连模式
}

/**
 * 连接信息数据类
 */
data class ConnectionInfo(
    val mode: WifiConnectionMode,
    val ssid: String = "",
    val ipAddress: String = "",
    val peerAddress: String = "",
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 连接模式管理器监听接口
 */
interface ConnectionModeManagerListener {
    /**
     * 模式连接中
     */
    fun onModeConnecting(mode: WifiConnectionMode)
    
    /**
     * 模式连接成功
     */
    fun onModeConnected(mode: WifiConnectionMode, connectionInfo: ConnectionInfo)
    
    /**
     * 模式连接断开
     */
    fun onModeDisconnected(mode: WifiConnectionMode)
    
    /**
     * 模式连接失败
     */
    fun onModeConnectionFailed(mode: WifiConnectionMode, reason: Int)
}
```

## 使用示例

### 简化的使用方式

```kotlin
class MyApplication {
    private val riderService = RiderService()
    
    /**
     * 启动连接（简化版）
     */
    fun startConnection() {
        // 1. APP开始时设置连接模式
        val preferredMode = determineConnectionMode()
        riderService.setWifiConnectionMode(preferredMode)
        
        // 2. 然后按照正常流程连接BLE
        riderService.connectToBle() // 现有的BLE连接方法
        
        // 后续的WiFi连接会自动根据设置的模式进行
    }
    
    /**
     * 用户手动切换模式
     */
    fun switchConnectionMode(newMode: WifiConnectionMode) {
        Log.d(TAG, "Switching connection mode to: $newMode")
        
        // 1. 设置新的连接模式
        riderService.setWifiConnectionMode(newMode)
        
        // 2. 如果已经连接，断开重连
        if (riderService.isConnected()) {
            riderService.disconnect()
            // 重新连接时会使用新的模式
            riderService.connectToBle()
        }
    }
    
    /**
     * 获取当前设置的模式
     */
    fun getCurrentMode(): WifiConnectionMode {
        return riderService.getWifiConnectionMode()
    }
    
    /**
     * 根据业务逻辑确定连接模式
     */
    private fun determineConnectionMode(): WifiConnectionMode {
        return when {
            // 根据用户设置
            hasUserPreference() -> getUserPreference()
            // 根据设备特性
            isOldDevice() -> WifiConnectionMode.WIFI_P2P
            // 默认使用AP模式
            else -> WifiConnectionMode.WIFI_AP_CLIENT
        }
    }
    
    private fun getUserPreference(): WifiConnectionMode {
        val prefs = getSharedPreferences("app_settings", Context.MODE_PRIVATE)
        val modeName = prefs.getString("wifi_mode", "WIFI_AP_CLIENT")
        return try {
            WifiConnectionMode.valueOf(modeName!!)
        } catch (e: Exception) {
            WifiConnectionMode.WIFI_AP_CLIENT
        }
    }
    
    private fun hasUserPreference(): Boolean {
        val prefs = getSharedPreferences("app_settings", Context.MODE_PRIVATE)
        return prefs.contains("wifi_mode")
    }
    
    private fun isOldDevice(): Boolean {
        // 检查设备型号，老设备可能P2P兼容性更好
        return Build.VERSION.SDK_INT < Build.VERSION_CODES.Q
    }
}
```

### 在现有连接流程中的集成

```kotlin
// 在现有的连接管理类中集成
class ConnectionManager {
    
    fun checkVersion(version: RiderProtocol.ProtocolVerNotification) {
        cancelConnectWithTimeout()
        if (version.major <= RiderProtocol.ProtocolVersion.MAJOR_VERSION_VALUE) {
            if (RiderBleManager.instance.isConfigConnect() && !RiderService.instance.getOnBleItemClick()) {
                // 使用保存的配置信息连接
                val sharedPref = RiderService.instance.getApplication()
                    .getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
                val hasConfig = sharedPref.contains("wifi_config")
                
                if (hasConfig && _connectionStatus.value.wifiStatus !is WifiStatus.DeviceConnected) {
                    // 直接开始WiFi连接，模式已经在APP启动时设置好了
                    requestWifiInfo(false)
                } else {
                    showDialog(mApplication.getString(R.string.error_title), 
                             mApplication.getString(R.string.config_error))
                }
            } else {
                // 正常流程，请求WiFi信息
                requestWifiInfo(false)
            }
        } else {
            protocolError(mApplication.getString(R.string.version_error))
        }
    }
}
```

### 枚举定义

```kotlin
/**
 * WiFi连接模式枚举
 */
enum class WifiConnectionMode {
    WIFI_AP_CLIENT,  // AP客户端模式 - 连接到仪表热点
    WIFI_P2P         // P2P直连模式 - 现有的P2P连接
}