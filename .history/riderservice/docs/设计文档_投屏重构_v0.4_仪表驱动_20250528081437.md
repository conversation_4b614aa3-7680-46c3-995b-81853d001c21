# RiderService SDK 重构设计文档 (投屏增强 - 仪表驱动模式)

> 版本：0.4
> 作者：AI 自动生成（请根据实际情况补充、修订）

---

## 1. 背景与目标

本文档基于 `设计文档_投屏重构_v0.3.md`，并根据用户最新澄清进行调整：**投屏模式的选择由智能仪表硬件通过发送 `NaviMode` 指令来驱动，而非由手机 App 主动发起。**

**核心目标保持不变，但实现方式有所调整:**

1.  **移除天气服务**: 彻底剥离。
2.  **API 精炼**: 提供简洁的 API，主要围绕连接管理、消息发送以及对仪表指令的响应。
3.  **仪表驱动的双投屏模式**: SDK 需能正确响应仪表发送的 `NaviMode`，并据此协调以下两种投屏方式：
    *   **镜像投屏 (Mirror Mode)**: 当仪表请求进入镜像模式时，SDK 负责请求应用层提供 `MediaProjection` 权限，然后处理屏幕捕获、编码和流式传输。
    *   **Presentation 投屏 (Presentation Mode)**: 当仪表进入支持外部显示的模式并使一个 `Display` 对象可用时，SDK 负责将此 `Display` 提供给应用层，应用层可完全自定义其内容。

本文档将阐述因此调整的架构、组件职责、关键流程以及 API 设计。

---

## 2. 现有架构回顾 (简要)

(同 v0.3 文档，痛点依然是 API 不够清晰地反映仪表驱动模式，以及 Presentation 的通用性不足)

---

## 3. 提议的新架构设计 (仪表驱动模式)

整体组件结构与 v0.3 类似，但交互和控制流发生变化。`ProjectionManager` 更多的是一个状态管理者和资源协调者，响应由仪表发起的 `NaviMode` 变化。

```mermaid
graph TD
    App[应用层 UI/逻辑] -- RiderService API (连接, 消息, stopProjection) --> RS(RiderService API Facade)
    RS -- RiderServiceCallback (连接状态, 投屏事件, Display对象) --> App

    Instrument[智能仪表硬件] -- NaviMode 指令 --> CM(ConnectionManager via BLE/Wi-Fi)
    CM -- 解析NaviMode, 通知 --> PM(ProjectionManager)
    CM -- 网络连接 --> TransportLayer[Wi-Fi P2P/AP]
    TransportLayer -- 数据通道 --> Instrument

    RS -- 连接请求 --> CM
    RS -- (可选)停止当前投屏请求 --> PM

    PM -- 使用 --> AC(AutolinkControl 底层协议)
    PM -- 使用 --> MPS(MediaProjectService 捕获/编码)

    AC -- 控制/数据 (响应仪表) --> TransportLayer
    MPS -- 视频流 --> AC
```

**核心组件职责调整 (基于仪表驱动):**

### 3.1 `RiderService.kt` (API Facade)
*   **职责**: 
    *   管理回调，分发连接、消息、以及由仪表 `NaviMode` 触发的投屏相关事件。
    *   提供连接管理 (`connectBle`, `disconnect`) 和消息发送 (`sendMessageToRiderService`) API。
    *   提供一个可选的 `stopCurrentProjection()` API，尝试通知仪表退出当前投屏状态 (如果协议支持此反向请求)，或者至少在 SDK 本地停止投屏活动并释放资源。
    *   将 `MediaProjection` 对象 (由应用在特定回调后提供) 转发给 `ProjectionManager`。
*   **主要API (详见第5节)**: `init()`, `addCallback()`, `removeCallback()`, `startBleScan()`, `connectBle()`, `disconnect()`, `setMediaProjection()`, `stopCurrentProjection() (可选)`, `sendMessageToRiderService()`。
    *   移除了主动的 `startMirrorMode()` 和 `startPresentationMode()`。

### 3.2 `ConnectionManager.kt` (连接与消息分发)
*   **职责**: 
    *   管理 BLE 和 Wi-Fi 连接。
    *   接收来自仪表的消息，特别是包含 `NaviMode` 的消息。
    *   解析 `NaviMode`，并将解析后的模式变化信息**通知给 `ProjectionManager`**。
    *   提供 Wi-Fi 数据通道给 `ProjectionManager` (间接通过 `AutolinkControl` 或类似机制) 用于视频数据传输。

### 3.3 `ProjectionManager.kt` (投屏状态与资源管理器 - 响应型)
*   **职责**: **响应由 `ConnectionManager` (源自仪表) 传递过来的 `NaviMode` 变化，并管理相应的投屏状态和资源。**
    *   **接收 `NaviMode` 更新**: 从 `ConnectionManager` 获取新的 `NaviMode`。
    *   **响应镜像模式请求 (Mirror Mode)**:
        *   当 `NaviMode` 表明应启动镜像投屏时，通过 `RiderServiceCallback.onMirrorModeRequiresMediaProjection()` 请求应用层提供 `MediaProjection`。
        *   收到 `MediaProjection` 后，启动屏幕捕获、编码和流式传输。
        *   通过 `RiderServiceCallback.onMirrorModeStarted()` 通知应用。
    *   **响应 Presentation 模式请求 (Presentation Mode)**:
        *   当 `NaviMode` 表明应进入 Presentation 模式，并且底层 (如 `AutolinkControl`) 回调一个可用的 `Display` 对象时，通过 `RiderServiceCallback.onPresentationModeDisplayAvailable(display)` 将其提供给应用。
    *   **响应退出/非投屏模式**: 
        *   当 `NaviMode` 表明退出投屏或进入非投屏模式时，停止当前的投屏活动（镜像或 Presentation）。
        *   如果是镜像，则停止捕获/编码/推流，并通过 `RiderServiceCallback.onMirrorModeStopped()` 通知应用。
        *   如果是 Presentation，则通过 `RiderServiceCallback.onPresentationModeDisplayReleased(display)` 通知应用释放 `Display`。
    *   **处理 `stopCurrentProjection()` 请求**: 如果应用调用了此方法，`ProjectionManager` 应停止当前所有投屏活动，并可选地通过 `AutolinkControl` 尝试通知仪表切换到非投屏 `NaviMode` (如果协议允许)。
    *   管理投屏资源 (编码器, `VirtualDisplay` 等)。

### 3.4 `DisplayNaviManager.kt` (重构或移除)
*   职责进一步削弱或完全被 `ProjectionManager` 和 `ConnectionManager` 吸收。

### 3.5 `AutolinkControl.kt` 和 `MediaProjectService.java` (底层实现模块)
*   职责基本不变，仍由 `ProjectionManager` 调用，但其行为更多是响应 `ProjectionManager` 根据仪表 `NaviMode` 下达的指令。

---

## 4. 关键流程重设计 (仪表驱动模式)

### 4.1 连接建立流程 (同 v0.3)
(...)
4.  当 `ConnectionManager` 确认 Wi-Fi 数据通道已建立，它会**通知 `ProjectionManager` 网络已就绪**。

### 4.2 仪表请求镜像投屏 (Mirror Mode)
1.  **仪表**: 发送一个表示"请求镜像投屏"的 `NaviMode` (例如 `MirrorNAVI`) 给手机。
2.  **`ConnectionManager`**: 接收并解析该 `NaviMode`，通知 `ProjectionManager` 新的模式为镜像模式。
3.  **`ProjectionManager`**: 
    *   确认网络已就绪。
    *   通过 `RiderServiceCallback.onMirrorModeRequiresMediaProjection()` 请求应用层提供 `MediaProjection`。
4.  **应用**: 用户授权后，获取 `MediaProjection` 对象，并调用 `RiderService.instance.setMediaProjection(mediaProjection)`。
5.  **`RiderService`**: 转发 `MediaProjection` 给 `ProjectionManager`。
6.  **`ProjectionManager`**: 
    *   使用 `MediaProjection` 启动屏幕捕获、编码和流式传输 (通过 `MediaProjectService` 和 `AutolinkControl`)。
    *   通过 `RiderServiceCallback.onMirrorModeStarted()` 通知应用镜像已开始。
7.  **仪表 (或应用调用 `stopCurrentProjection()` )**: 发送一个表示"退出投屏"或"进入非投屏模式"的 `NaviMode`。
8.  **`ConnectionManager`**: 接收并解析 `NaviMode`，通知 `ProjectionManager`。
9.  **`ProjectionManager`**: 
    *   停止屏幕捕获、编码和流式传输。
    *   释放相关资源。
    *   通过 `RiderServiceCallback.onMirrorModeStopped()` 通知应用镜像已停止。

### 4.3 仪表请求 Presentation 投屏 (Presentation Mode)
1.  **仪表**: 发送一个表示"进入 Presentation 可用模式"的 `NaviMode`。
2.  **`ConnectionManager`**: 接收并解析 `NaviMode`，通知 `ProjectionManager`。
3.  **`ProjectionManager`**: 
    *   确认网络已就绪。
    *   等待 `AutolinkControl` (或类似底层机制) 初始化并回调一个可用的外部 `Display` 对象。
4.  **`AutolinkControl`**: (当 Display 就绪时) 通过回调将 `Display` 对象给 `ProjectionManager`。
5.  **`ProjectionManager`**: 通过 `RiderServiceCallback.onPresentationModeDisplayAvailable(display: Display)` 将 `Display` 对象提供给应用层。
6.  **应用**: 
    *   接收 `Display` 对象，创建并显示 `android.app.Presentation`。
7.  **仪表 (或应用调用 `stopCurrentProjection()` )**: 发送一个表示"退出投屏"或"进入非投屏模式"的 `NaviMode`。
8.  **`ConnectionManager`**: 接收并解析 `NaviMode`，通知 `ProjectionManager`。
9.  **`ProjectionManager`**: 
    *   通过 `RiderServiceCallback.onPresentationModeDisplayReleased(display: Display)` 通知应用释放其 `Presentation` 和 `Display`。
    *   通知 `AutolinkControl` 释放外部 `Display` 资源。

### 4.4 应用请求停止当前投屏 (可选)
1.  **应用**: 调用 `RiderService.instance.stopCurrentProjection()`。
2.  **`RiderService`**: 转发请求给 `ProjectionManager.handleStopCurrentProjectionRequest()`。
3.  **`ProjectionManager`**: 
    *   停止当前正在进行的任何投屏活动 (镜像或 Presentation 的资源释放，回调应用层 `onMirrorModeStopped` 或 `onPresentationModeDisplayReleased`)。
    *   (可选，如果协议支持) 通过 `AutolinkControl` 发送一个消息给仪表，建议其切换到非投屏的 `NaviMode`。

---

## 5. API 设计 (RiderService 公共接口 - 仪表驱动版)

```kotlin
object RiderService {
    fun init(application: Application)
    fun addCallback(callback: RiderServiceCallback)
    fun removeCallback(callback: RiderServiceCallback)
    fun startBleScan()
    fun stopBleScan()
    fun connectBle(device: BleDevice)
    fun disconnect()

    /**
     * (由应用在 onMirrorModeRequiresMediaProjection 回调后调用)
     * 设置应用层获取到的 MediaProjection 对象给 SDK，用于屏幕捕获。
     * 仅当仪表请求进入镜像模式后，SDK 回调请求此对象时调用才有意义。
     * @param mediaProjection 用户授权后获取的 MediaProjection 实例。
     */
    fun setMediaProjection(mediaProjection: MediaProjection)

    /**
     * (可选 API) 请求停止当前正在进行的所有投屏活动 (镜像或 Presentation)。
     * SDK会停止本地投屏，并可能尝试通知仪表切换到非投屏模式 (具体行为依赖仪表协议)。
     * 应用应依然监听 onMirrorModeStopped 或 onPresentationModeDisplayReleased 回调进行最终的UI清理。
     */
    fun stopCurrentProjection() // 新增或调整

    fun sendMessageToRiderService(message: RiderMessage)
    fun destroy()

    // 移除了主动的 startMirrorMode() 和 startPresentationMode()
    // fun getCurrentConnectionState(): ConnectionState // 保持可选
    // fun getCachedRiderServiceConfig(): RiderServiceConfig? // 保持可选
}
```

`RiderServiceCallback` 接口保持不变 (如 v0.2/v0.3 中定义，包含 `onMirrorModeRequiresMediaProjection`, `onMirrorModeStarted`, `onMirrorModeStopped`, `onPresentationModeDisplayAvailable`, `onPresentationModeDisplayReleased`)，因为应用层仍然需要这些回调来响应由仪表驱动的投屏事件。

---

## 6. 移除天气服务

(同 v0.3 文档，保持不变)

---

## 7. 总结与后续步骤

此仪表驱动的投屏模式设计进一步明确了 SDK 的角色——作为仪表指令的响应者和资源协调者。`ProjectionManager` 根据从仪表接收到的 `NaviMode` 动态管理镜像和 Presentation 投屏，并通过回调与应用层交互。

**后续步骤**: (与 v0.3 类似，但侧重点在于实现响应 `NaviMode` 的逻辑)
1.  **代码实现**: 
    *   `ConnectionManager` 需能正确解析 `NaviMode` 并通知 `ProjectionManager`。
    *   `ProjectionManager` 实现对不同 `NaviMode` 的响应逻辑，管理资源并触发正确的 `RiderServiceCallback`。
    *   `RiderService` 更新 API，移除主动投屏方法，可能增加 `stopCurrentProjection()`。
    *   确保底层模块 (`AutolinkControl`, `MediaProjectService`) 能被 `ProjectionManager` 在新的响应式流程中正确驱动。
2.  **测试**: 重点测试不同 `NaviMode` 切换场景下的 SDK 行为和回调准确性。
3.  **文档更新**: 确保最终文档准确反映此仪表驱动模型。

---

> 本文档已根据"仪表驱动投屏模式"的澄清进行更新。 