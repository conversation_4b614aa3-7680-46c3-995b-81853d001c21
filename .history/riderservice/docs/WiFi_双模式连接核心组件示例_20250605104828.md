# WiFi双模式连接核心组件代码示例

## 1. ConnectionModeManager.kt - 连接模式管理器

```kotlin
package com.link.riderservice.connection.network

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.link.riderservice.api.RiderService
import com.link.riderservice.utils.TimeUtils
import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 连接模式管理器
 * 统一管理WiFi P2P和WiFi AP客户端两种连接模式
 */
class ConnectionModeManager(
    private val listener: ConnectionModeManagerListener
) {
    private val context = RiderService.instance.getApplication().applicationContext
    private val preferences: SharedPreferences by lazy {
        context.getSharedPreferences("wifi_connection_mode", Context.MODE_PRIVATE)
    }
    
    private var currentMode = WifiConnectionMode.AUTO
    private var targetMode = WifiConnectionMode.WIFI_AP_CLIENT // 默认AP模式
    private var isConnecting = AtomicBoolean(false)
    private var connectionJob: Job? = null
    
    // WiFi AP客户端管理器
    private val wifiClientManager: WiFiClientManager by lazy {
        WiFiClientManager(wifiClientListener)
    }
    
    // WiFi P2P管理器（复用现有）
    private val p2pManager: SoftP2pManager by lazy {
        SoftP2pManager(p2pListener)
    }
    
    companion object {
        private const val TAG = "ConnectionModeManager"
        private const val PREF_LAST_SUCCESSFUL_MODE = "last_successful_mode"
        private const val PREF_MODE_PREFERENCE = "mode_preference"
        private const val AP_TIMEOUT_MS = 15000L // AP模式连接超时
        private const val P2P_TIMEOUT_MS = 20000L // P2P模式连接超时
    }

    /**
     * 开始连接流程
     */
    fun startConnection(context: Context, forceMode: WifiConnectionMode? = null) {
        if (isConnecting.get()) {
            Log.w(TAG, "Connection already in progress")
            return
        }
        
        isConnecting.set(true)
        
        // 确定连接模式
        val selectedMode = forceMode ?: determineConnectionMode()
        currentMode = selectedMode
        
        Log.d(TAG, "Starting connection with mode: $selectedMode")
        listener.onModeSelected(selectedMode)
        
        connectionJob = CoroutineScope(Dispatchers.Main).launch {
            try {
                when (selectedMode) {
                    WifiConnectionMode.WIFI_AP_CLIENT -> {
                        attemptApConnection(context)
                    }
                    WifiConnectionMode.WIFI_P2P -> {
                        attemptP2pConnection(context)
                    }
                    WifiConnectionMode.AUTO -> {
                        attemptAutoConnection(context)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Connection failed", e)
                handleConnectionFailure(selectedMode, -999)
            }
        }
    }
    
    /**
     * 断开连接
     */
    fun disconnect() {
        Log.d(TAG, "Disconnecting current mode: $currentMode")
        
        connectionJob?.cancel()
        isConnecting.set(false)
        
        when (currentMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                wifiClientManager.disconnect()
            }
            WifiConnectionMode.WIFI_P2P -> {
                p2pManager.stop()
            }
            else -> {
                // 同时断开两种模式
                wifiClientManager.disconnect()
                p2pManager.stop()
            }
        }
    }
    
    /**
     * 自动连接模式：先尝试AP，失败后回退到P2P
     */
    private suspend fun attemptAutoConnection(context: Context) {
        Log.d(TAG, "Attempting auto connection (AP first, P2P fallback)")
        
        // 首先尝试AP模式
        val apSuccess = withTimeoutOrNull(AP_TIMEOUT_MS) {
            attemptApConnectionInternal(context)
        } ?: false
        
        if (apSuccess) {
            Log.d(TAG, "Auto connection: AP mode succeeded")
            saveSuccessfulMode(WifiConnectionMode.WIFI_AP_CLIENT)
            return
        }
        
        Log.d(TAG, "Auto connection: AP mode failed, trying P2P")
        listener.onModeSwitching(WifiConnectionMode.WIFI_AP_CLIENT, WifiConnectionMode.WIFI_P2P)
        
        // AP失败，尝试P2P模式
        val p2pSuccess = withTimeoutOrNull(P2P_TIMEOUT_MS) {
            attemptP2pConnectionInternal(context)
        } ?: false
        
        if (p2pSuccess) {
            Log.d(TAG, "Auto connection: P2P mode succeeded")
            currentMode = WifiConnectionMode.WIFI_P2P
            saveSuccessfulMode(WifiConnectionMode.WIFI_P2P)
        } else {
            Log.e(TAG, "Auto connection: Both modes failed")
            handleConnectionFailure(WifiConnectionMode.AUTO, -998)
        }
    }
    
    private suspend fun attemptApConnection(context: Context) {
        val success = withTimeoutOrNull(AP_TIMEOUT_MS) {
            attemptApConnectionInternal(context)
        } ?: false
        
        if (!success) {
            handleConnectionFailure(WifiConnectionMode.WIFI_AP_CLIENT, -997)
        }
    }
    
    private suspend fun attemptP2pConnection(context: Context) {
        val success = withTimeoutOrNull(P2P_TIMEOUT_MS) {
            attemptP2pConnectionInternal(context)
        } ?: false
        
        if (!success) {
            handleConnectionFailure(WifiConnectionMode.WIFI_P2P, -996)
        }
    }
    
    private suspend fun attemptApConnectionInternal(context: Context): Boolean {
        return suspendCancellableCoroutine { continuation ->
            listener.onModeConnecting(WifiConnectionMode.WIFI_AP_CLIENT)
            
            // 请求仪表端WiFi热点信息
            requestWifiApInfo { ssid, password ->
                if (ssid.isNotEmpty()) {
                    wifiClientManager.connectToWifi(ssid, password)
                } else {
                    continuation.resumeWith(Result.success(false))
                }
            }
        }
    }
    
    private suspend fun attemptP2pConnectionInternal(context: Context): Boolean {
        return suspendCancellableCoroutine { continuation ->
            listener.onModeConnecting(WifiConnectionMode.WIFI_P2P)
            
            // 请求仪表端P2P信息
            requestP2pInfo { address, port ->
                if (address.isNotEmpty()) {
                    p2pManager.start(address, port)
                } else {
                    continuation.resumeWith(Result.success(false))
                }
            }
        }
    }
    
    /**
     * 确定连接模式
     */
    private fun determineConnectionMode(): WifiConnectionMode {
        // 优先使用上次成功的模式
        val lastSuccessful = getLastSuccessfulMode()
        if (lastSuccessful != WifiConnectionMode.AUTO) {
            Log.d(TAG, "Using last successful mode: $lastSuccessful")
            return lastSuccessful
        }
        
        // 检查用户偏好设置
        val userPreference = getUserModePreference()
        if (userPreference != WifiConnectionMode.AUTO) {
            Log.d(TAG, "Using user preference mode: $userPreference")
            return userPreference
        }
        
        // 默认自动模式
        Log.d(TAG, "Using default auto mode")
        return WifiConnectionMode.AUTO
    }
    
    private fun handleConnectionFailure(mode: WifiConnectionMode, reason: Int) {
        isConnecting.set(false)
        Log.e(TAG, "Connection failed for mode $mode with reason $reason")
        listener.onModeConnectionFailed(mode, reason)
    }
    
    // WiFi AP客户端监听器
    private val wifiClientListener = object : WiFiClientManagerListener {
        override fun onWifiConnecting(ssid: String) {
            Log.d(TAG, "AP mode: WiFi connecting to $ssid")
        }
        
        override fun onWifiConnected(ssid: String, ipAddress: String) {
            Log.d(TAG, "AP mode: WiFi connected to $ssid, IP: $ipAddress")
            val connectionInfo = ConnectionInfo(
                mode = WifiConnectionMode.WIFI_AP_CLIENT,
                ssid = ssid,
                ipAddress = ipAddress
            )
            isConnecting.set(false)
            listener.onModeConnected(WifiConnectionMode.WIFI_AP_CLIENT, connectionInfo)
        }
        
        override fun onWifiDisconnected() {
            Log.d(TAG, "AP mode: WiFi disconnected")
            listener.onModeDisconnected(WifiConnectionMode.WIFI_AP_CLIENT)
        }
        
        override fun onWifiConnectionFailed(reason: Int) {
            Log.e(TAG, "AP mode: WiFi connection failed: $reason")
            handleConnectionFailure(WifiConnectionMode.WIFI_AP_CLIENT, reason)
        }
        
        override fun onNetworkScanComplete(networks: List<android.net.wifi.ScanResult>) {
            Log.d(TAG, "AP mode: Network scan completed, found ${networks.size} networks")
        }
        
        override fun requestWifiInfo() {
            Log.d(TAG, "AP mode: Requesting WiFi info")
            // 通过BLE请求仪表端热点信息
            requestWifiApInfo { _, _ -> }
        }
        
        override fun onWifiState(opened: Boolean) {
            Log.d(TAG, "AP mode: WiFi state changed: $opened")
        }
    }
    
    // WiFi P2P监听器
    private val p2pListener = object : SoftIP2pListener {
        override fun onWifiConnectSuccess() {
            Log.d(TAG, "P2P mode: Connection success")
            val connectionInfo = ConnectionInfo(
                mode = WifiConnectionMode.WIFI_P2P,
                ssid = "P2P_Direct",
                ipAddress = "************" // P2P默认IP
            )
            isConnecting.set(false)
            listener.onModeConnected(WifiConnectionMode.WIFI_P2P, connectionInfo)
        }
        
        override fun onWifiDisconnect() {
            Log.d(TAG, "P2P mode: Disconnected")
            listener.onModeDisconnected(WifiConnectionMode.WIFI_P2P)
        }
        
        override fun onP2pConnectSuccess() {
            Log.d(TAG, "P2P mode: P2P connect success")
        }
        
        override fun connectExist() {
            Log.d(TAG, "P2P mode: Connection already exists")
        }
        
        override fun onCancelConnect() {
            Log.d(TAG, "P2P mode: Connection cancelled")
        }
        
        override fun requestWifiInfo() {
            Log.d(TAG, "P2P mode: Requesting WiFi info")
            // 通过BLE请求仪表端P2P信息
            requestP2pInfo { _, _ -> }
        }
        
        override fun onWifiState(opened: Boolean) {
            Log.d(TAG, "P2P mode: WiFi state changed: $opened")
        }
    }
    
    /**
     * 保存成功的连接模式
     */
    private fun saveSuccessfulMode(mode: WifiConnectionMode) {
        preferences.edit()
            .putString(PREF_LAST_SUCCESSFUL_MODE, mode.name)
            .apply()
        Log.d(TAG, "Saved successful mode: $mode")
    }
    
    /**
     * 获取上次成功的连接模式
     */
    private fun getLastSuccessfulMode(): WifiConnectionMode {
        val modeName = preferences.getString(PREF_LAST_SUCCESSFUL_MODE, WifiConnectionMode.AUTO.name)
        return try {
            WifiConnectionMode.valueOf(modeName!!)
        } catch (e: Exception) {
            WifiConnectionMode.AUTO
        }
    }
    
    /**
     * 获取用户模式偏好
     */
    private fun getUserModePreference(): WifiConnectionMode {
        val modeName = preferences.getString(PREF_MODE_PREFERENCE, WifiConnectionMode.AUTO.name)
        return try {
            WifiConnectionMode.valueOf(modeName!!)
        } catch (e: Exception) {
            WifiConnectionMode.AUTO
        }
    }
    
    /**
     * 设置用户模式偏好
     */
    fun setUserModePreference(mode: WifiConnectionMode) {
        preferences.edit()
            .putString(PREF_MODE_PREFERENCE, mode.name)
            .apply()
        Log.d(TAG, "Set user mode preference: $mode")
    }
    
    private fun requestWifiApInfo(callback: (ssid: String, password: String) -> Unit) {
        // TODO: 通过BLE请求仪表端热点信息
        // 这里需要调用MessageManager发送AP模式请求
        callback("TestInstrumentAP", "testpassword")
    }
    
    private fun requestP2pInfo(callback: (address: String, port: Int) -> Unit) {
        // TODO: 通过BLE请求仪表端P2P信息
        // 这里需要调用MessageManager发送P2P模式请求
        callback("AA:BB:CC:DD:EE:FF", 30512)
    }
    
    /**
     * 获取当前连接模式
     */
    fun getCurrentMode(): WifiConnectionMode = currentMode
    
    /**
     * 强制切换到指定模式
     */
    fun switchToMode(mode: WifiConnectionMode, context: Context) {
        Log.d(TAG, "Force switching to mode: $mode")
        disconnect()
        
        // 短暂延迟后开始新连接
        CoroutineScope(Dispatchers.Main).launch {
            delay(1000)
            startConnection(context, mode)
        }
    }
    
    /**
     * 销毁资源
     */
    fun destroy() {
        disconnect()
        connectionJob?.cancel()
        wifiClientManager.destroy()
        // P2P管理器由DisplayNaviManager管理生命周期
    }
}

/**
 * 连接模式管理器监听接口
 */
interface ConnectionModeManagerListener {
    /**
     * 模式已选择
     */
    fun onModeSelected(mode: WifiConnectionMode)
    
    /**
     * 模式连接中
     */
    fun onModeConnecting(mode: WifiConnectionMode)
    
    /**
     * 模式连接成功
     */
    fun onModeConnected(mode: WifiConnectionMode, connectionInfo: ConnectionInfo)
    
    /**
     * 模式断开连接
     */
    fun onModeDisconnected(mode: WifiConnectionMode)
    
    /**
     * 模式连接失败
     */
    fun onModeConnectionFailed(mode: WifiConnectionMode, reason: Int)
    
    /**
     * 模式切换中
     */
    fun onModeSwitching(fromMode: WifiConnectionMode, toMode: WifiConnectionMode)
}
```

## 2. WifiConnectionMode.kt - 连接模式定义

```kotlin
package com.link.riderservice.connection.network

/**
 * WiFi连接模式枚举
 */
enum class WifiConnectionMode {
    /**
     * WiFi AP客户端模式
     * 手机作为客户端连接到仪表端WiFi热点
     */
    WIFI_AP_CLIENT,
    
    /**
     * WiFi P2P直连模式
     * 手机与仪表端建立P2P直连
     */
    WIFI_P2P,
    
    /**
     * 自动选择模式
     * 系统自动选择最佳连接模式
     */
    AUTO
}

/**
 * 连接信息数据类
 */
data class ConnectionInfo(
    val mode: WifiConnectionMode,
    val ssid: String,
    val ipAddress: String,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 连接模式工具类
 */
object WifiConnectionModeUtils {
    
    /**
     * 获取模式显示名称
     */
    fun getDisplayName(mode: WifiConnectionMode): String {
        return when (mode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> "WiFi热点连接"
            WifiConnectionMode.WIFI_P2P -> "WiFi直连"
            WifiConnectionMode.AUTO -> "自动选择"
        }
    }
    
    /**
     * 获取模式描述
     */
    fun getDescription(mode: WifiConnectionMode): String {
        return when (mode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> "连接到仪表端WiFi热点，稳定性较好"
            WifiConnectionMode.WIFI_P2P -> "与仪表端建立P2P直连，兼容性较好"
            WifiConnectionMode.AUTO -> "系统自动选择最佳连接模式"
        }
    }
    
    /**
     * 检查模式是否需要特殊权限
     */
    fun requiresSpecialPermissions(mode: WifiConnectionMode): Boolean {
        return when (mode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> true // 需要位置权限等
            WifiConnectionMode.WIFI_P2P -> true // 需要WiFi Direct权限
            WifiConnectionMode.AUTO -> true // 需要所有权限
        }
    }
}
```

## 3. 修改DisplayNaviManager.kt集成双模式

```kotlin
// 在DisplayNaviManager.kt中的修改部分

private val connectionModeManager: ConnectionModeManager by lazy {
    ConnectionModeManager(connectionModeListener)
}

private val connectionModeListener = object : ConnectionModeManagerListener {
    override fun onModeSelected(mode: WifiConnectionMode) {
        Log.d(TAG, "Connection mode selected: ${WifiConnectionModeUtils.getDisplayName(mode)}")
    }
    
    override fun onModeConnecting(mode: WifiConnectionMode) {
        Log.d(TAG, "Connecting with mode: ${WifiConnectionModeUtils.getDisplayName(mode)}")
        // 可以在这里更新UI状态
    }
    
    override fun onModeConnected(mode: WifiConnectionMode, connectionInfo: ConnectionInfo) {
        Log.d(TAG, "Connected with mode: ${WifiConnectionModeUtils.getDisplayName(mode)}")
        Log.d(TAG, "Connection info: SSID=${connectionInfo.ssid}, IP=${connectionInfo.ipAddress}")
        
        isWifiConnected = true
        currentConnectionMode = mode
        
        // 启动TCP服务器
        startTcpServer()
    }
    
    override fun onModeDisconnected(mode: WifiConnectionMode) {
        Log.d(TAG, "Disconnected from mode: ${WifiConnectionModeUtils.getDisplayName(mode)}")
        
        if (isWifiConnected) {
            shutdown()
        }
        isWifiConnected = false
        currentConnectionMode = null
    }
    
    override fun onModeConnectionFailed(mode: WifiConnectionMode, reason: Int) {
        Log.e(TAG, "Connection failed with mode: ${WifiConnectionModeUtils.getDisplayName(mode)}, reason: $reason")
        
        // 可以在这里触发错误处理或用户提示
        requestWifiInfo()
    }
    
    override fun onModeSwitching(fromMode: WifiConnectionMode, toMode: WifiConnectionMode) {
        Log.d(TAG, "Switching from ${WifiConnectionModeUtils.getDisplayName(fromMode)} to ${WifiConnectionModeUtils.getDisplayName(toMode)}")
    }
}

private var currentConnectionMode: WifiConnectionMode? = null

/**
 * 使用双模式管理器开始连接
 */
fun startDualModeConnection(context: Context, forceMode: WifiConnectionMode? = null) {
    Log.d(TAG, "Starting dual mode connection")
    mContext = context
    connectionModeManager.startConnection(context, forceMode)
}

/**
 * 强制切换连接模式
 */
fun switchConnectionMode(mode: WifiConnectionMode) {
    Log.d(TAG, "Switching connection mode to: ${WifiConnectionModeUtils.getDisplayName(mode)}")
    mContext?.let { context ->
        connectionModeManager.switchToMode(mode, context)
    }
}

/**
 * 获取当前连接模式
 */
fun getCurrentConnectionMode(): WifiConnectionMode? = currentConnectionMode

/**
 * 设置用户偏好的连接模式
 */
fun setPreferredConnectionMode(mode: WifiConnectionMode) {
    connectionModeManager.setUserModePreference(mode)
}

override fun shutdown() {
    Log.d(TAG, "Shutting down dual mode connection")
    connectionModeManager.disconnect()
    
    // 清理其他资源
    mTcpServerConnection?.shutdown()
    // ... 其他清理代码
}
```

## 4. 协议层支持模式协商

```kotlin
// 新增协议消息定义示例

message WifiModeNegotiationRequest {
    repeated WifiMode supported_modes = 1;
    WifiMode preferred_mode = 2;
}

message WifiModeNegotiationResponse {
    repeated WifiMode supported_modes = 1;
    WifiMode selected_mode = 2;
    bool negotiation_success = 3;
}

enum WifiMode {
    WIFI_MODE_UNKNOWN = 0;
    WIFI_P2P = 1;
    WIFI_AP = 2;
}
```

这个双模式实现方案提供了：

1. **智能模式选择**：默认AP，失败时自动回退P2P
2. **完全向后兼容**：保留所有现有P2P功能
3. **统一管理接口**：上层调用无需关心具体模式
4. **模式记忆功能**：记住成功的连接模式
5. **强制模式切换**：支持用户手动选择模式
6. **详细状态回调**：提供丰富的连接状态信息

这样的设计既保证了系统的稳定性，又增加了新的AP连接能力，是一个非常稳健的双模式支持方案。 