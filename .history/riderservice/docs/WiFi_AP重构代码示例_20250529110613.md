# WiFi AP重构核心代码示例

## 1. WiFiApManager.kt - 核心AP管理类

```kotlin
package com.link.riderservice.connection.network

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.NetworkInfo
import android.net.wifi.WifiConfiguration
import android.net.wifi.WifiManager
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import com.link.riderservice.api.RiderService
import com.link.riderservice.utils.TimeUtils
import java.lang.reflect.Method
import java.net.Inet4Address
import java.net.NetworkInterface

/**
 * WiFi AP模式管理器
 * 替换原有的SoftP2pManager，实现WiFi热点功能
 */
internal class WiFiApManager(
    private val listener: WiFiApManagerListener
) {
    private val context = RiderService.instance.getApplication().applicationContext
    private val wifiManager: WifiManager by lazy {
        context.getSystemService(Context.WIFI_SERVICE) as WifiManager
    }
    private val connectivityManager: ConnectivityManager by lazy {
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    private var currentState = ApConnectionState.IDLE
    private var hotspotReservation: WifiManager.LocalOnlyHotspotReservation? = null
    private var apConfig: WifiConfiguration? = null
    private var isApEnabled = false
    private var connectedDevices = mutableSetOf<String>()

    // 广播接收器监听WiFi状态和设备连接
    private val wifiStateReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                WifiManager.WIFI_STATE_CHANGED_ACTION -> {
                    handleWifiStateChanged(intent)
                }
                ConnectivityManager.CONNECTIVITY_ACTION -> {
                    handleConnectivityChanged()
                }
                WifiManager.WIFI_AP_STATE_CHANGED_ACTION -> {
                    handleApStateChanged(intent)
                }
            }
        }
    }

    /**
     * 启动WiFi AP模式
     */
    fun startWifiAp(ssid: String, password: String) {
        Log.d(TAG, "Starting WiFi AP: $ssid")
        
        if (currentState != ApConnectionState.IDLE) {
            Log.w(TAG, "AP already starting or started")
            return
        }

        updateState(ApConnectionState.STARTING_AP)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startLocalOnlyHotspot()
        } else {
            startLegacyHotspot(ssid, password)
        }
    }

    /**
     * Android 8.0+ 使用LocalOnlyHotspot
     */
    @RequiresApi(Build.VERSION_CODES.O)
    @SuppressLint("MissingPermission")
    private fun startLocalOnlyHotspot() {
        wifiManager.startLocalOnlyHotspot(object : WifiManager.LocalOnlyHotspotCallback() {
            override fun onStarted(reservation: WifiManager.LocalOnlyHotspotReservation) {
                super.onStarted(reservation)
                Log.d(TAG, "LocalOnlyHotspot started successfully")
                hotspotReservation = reservation
                
                val config = reservation.wifiConfiguration
                apConfig = config
                isApEnabled = true
                updateState(ApConnectionState.AP_ENABLED)
                
                listener.onApEnabled(config?.SSID ?: "", config?.preSharedKey ?: "")
                startDeviceMonitoring()
            }

            override fun onStopped() {
                super.onStopped()
                Log.d(TAG, "LocalOnlyHotspot stopped")
                handleApStopped()
            }

            override fun onFailed(reason: Int) {
                super.onFailed(reason)
                Log.e(TAG, "LocalOnlyHotspot failed: $reason")
                updateState(ApConnectionState.CONNECTION_FAILED)
                listener.onApStartFailed(reason)
            }
        }, null)
    }

    /**
     * Android 8.0以下使用反射方式
     */
    @SuppressLint("MissingPermission")
    private fun startLegacyHotspot(ssid: String, password: String) {
        try {
            val config = createHotspotConfig(ssid, password)
            apConfig = config
            
            val method: Method = wifiManager.javaClass.getMethod(
                "setWifiApEnabled", 
                WifiConfiguration::class.java, 
                Boolean::class.javaPrimitiveType
            )
            
            val result = method.invoke(wifiManager, config, true) as Boolean
            
            if (result) {
                Log.d(TAG, "Legacy hotspot started successfully")
                isApEnabled = true
                updateState(ApConnectionState.AP_ENABLED)
                listener.onApEnabled(ssid, password)
                startDeviceMonitoring()
            } else {
                Log.e(TAG, "Failed to start legacy hotspot")
                updateState(ApConnectionState.CONNECTION_FAILED)
                listener.onApStartFailed(-1)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception starting legacy hotspot", e)
            updateState(ApConnectionState.CONNECTION_FAILED)
            listener.onApStartFailed(-1)
        }
    }

    /**
     * 创建热点配置
     */
    private fun createHotspotConfig(ssid: String, password: String): WifiConfiguration {
        return WifiConfiguration().apply {
            SSID = ssid
            preSharedKey = password
            allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
            allowedProtocols.set(WifiConfiguration.Protocol.RSN)
            allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
            allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
        }
    }

    /**
     * 停止WiFi AP
     */
    fun stopWifiAp() {
        Log.d(TAG, "Stopping WiFi AP")
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            hotspotReservation?.close()
            hotspotReservation = null
        } else {
            stopLegacyHotspot()
        }
        
        handleApStopped()
    }

    @SuppressLint("MissingPermission")
    private fun stopLegacyHotspot() {
        try {
            val method: Method = wifiManager.javaClass.getMethod(
                "setWifiApEnabled", 
                WifiConfiguration::class.java, 
                Boolean::class.javaPrimitiveType
            )
            method.invoke(wifiManager, null, false)
        } catch (e: Exception) {
            Log.e(TAG, "Exception stopping legacy hotspot", e)
        }
    }

    /**
     * 获取AP模式下的IP地址
     */
    fun getApIpAddress(): String {
        return try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            for (intf in interfaces) {
                // 查找AP接口
                if (intf.name.contains("ap") || intf.name.contains("wlan1") || 
                    intf.name.contains("p2p")) {
                    for (addr in intf.inetAddresses) {
                        if (addr is Inet4Address && !addr.isLoopbackAddress) {
                            val ip = addr.hostAddress ?: ""
                            Log.d(TAG, "Found AP IP: $ip on interface: ${intf.name}")
                            return ip
                        }
                    }
                }
            }
            // 默认AP网关地址
            "************"
        } catch (e: Exception) {
            Log.e(TAG, "Error getting AP IP address", e)
            "************"
        }
    }

    /**
     * 开始监控设备连接
     */
    private fun startDeviceMonitoring() {
        updateState(ApConnectionState.WAITING_CONNECTION)
        // 定期检查连接的设备
        Thread {
            while (isApEnabled && currentState != ApConnectionState.DEVICE_CONNECTED) {
                checkConnectedDevices()
                Thread.sleep(2000) // 每2秒检查一次
            }
        }.start()
    }

    /**
     * 检查连接的设备
     */
    private fun checkConnectedDevices() {
        // 这里可以通过ARP表或其他方式检测连接的设备
        // 简化实现：检查是否有TCP连接到30512端口
        val hasConnection = checkTcpConnection()
        
        if (hasConnection && currentState == ApConnectionState.WAITING_CONNECTION) {
            updateState(ApConnectionState.DEVICE_CONNECTED)
            listener.onDeviceConnected(DeviceInfo("unknown", getApIpAddress()))
        }
    }

    private fun checkTcpConnection(): Boolean {
        // 检查是否有活动的TCP连接
        // 实际实现中可以与TcpConnection配合
        return false // 简化实现
    }

    private fun handleWifiStateChanged(intent: Intent) {
        val state = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN)
        when (state) {
            WifiManager.WIFI_STATE_DISABLED -> {
                listener.onWifiState(false)
            }
            WifiManager.WIFI_STATE_ENABLED -> {
                listener.onWifiState(true)
            }
        }
    }

    private fun handleConnectivityChanged() {
        // 处理网络连接变化
    }

    private fun handleApStateChanged(intent: Intent) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            val state = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN)
            // 处理AP状态变化
        }
    }

    private fun handleApStopped() {
        isApEnabled = false
        connectedDevices.clear()
        updateState(ApConnectionState.AP_DISABLED)
        listener.onApDisabled()
    }

    private fun updateState(newState: ApConnectionState) {
        Log.d(TAG, "State changed: $currentState -> $newState")
        currentState = newState
    }

    init {
        // 注册广播接收器
        val filter = IntentFilter().apply {
            addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)
            addAction(ConnectivityManager.CONNECTIVITY_ACTION)
            addAction(WifiManager.WIFI_AP_STATE_CHANGED_ACTION)
        }
        context.registerReceiver(wifiStateReceiver, filter)
    }

    fun destroy() {
        try {
            context.unregisterReceiver(wifiStateReceiver)
        } catch (e: Exception) {
            Log.e(TAG, "Error unregistering receiver", e)
        }
        stopWifiAp()
    }

    companion object {
        private const val TAG = "WiFiApManager"
    }
}

/**
 * AP连接状态枚举
 */
enum class ApConnectionState {
    IDLE,                    // 空闲状态
    STARTING_AP,             // 启动热点中
    AP_ENABLED,              // 热点已启用
    WAITING_CONNECTION,      // 等待设备连接
    DEVICE_CONNECTED,        // 设备已连接
    CONNECTION_FAILED,       // 连接失败
    AP_DISABLED             // 热点已禁用
}

/**
 * 设备信息数据类
 */
data class DeviceInfo(
    val macAddress: String,
    val ipAddress: String,
    val deviceName: String = "Unknown Device"
)
```

## 2. WiFiApManagerListener.kt - 监听接口

```kotlin
package com.link.riderservice.connection.network

/**
 * WiFi AP管理器事件监听接口
 */
interface WiFiApManagerListener {
    /**
     * AP热点启用成功
     * @param ssid 热点名称
     * @param password 热点密码
     */
    fun onApEnabled(ssid: String, password: String)

    /**
     * AP热点已禁用
     */
    fun onApDisabled()

    /**
     * 设备连接到热点
     * @param deviceInfo 连接的设备信息
     */
    fun onDeviceConnected(deviceInfo: DeviceInfo)

    /**
     * 设备从热点断开
     * @param deviceInfo 断开的设备信息
     */
    fun onDeviceDisconnected(deviceInfo: DeviceInfo)

    /**
     * AP启动失败
     * @param reason 失败原因
     */
    fun onApStartFailed(reason: Int)

    /**
     * 请求WiFi信息
     */
    fun requestWifiInfo()

    /**
     * WiFi状态变化
     * @param opened WiFi是否开启
     */
    fun onWifiState(opened: Boolean)
}
```

## 3. 修改DisplayNaviManager.kt

```kotlin
// 在DisplayNaviManager.kt中的修改部分

// 替换原有的P2P监听器
private val mApListener = object : WiFiApManagerListener {
    override fun onApEnabled(ssid: String, password: String) {
        Log.d(TAG, "WiFi AP enabled: $ssid")
        // 通知仪表端热点信息，让其连接
        val apInfo = AutoLinkConnect(wifiApManager.getApIpAddress())
        RiderService.instance.sendMessageToRiderService(apInfo)
    }

    override fun onApDisabled() {
        Log.d(TAG, "WiFi AP disabled")
        if (isApConnected) {
            shutdown()
        }
        isApConnected = false
    }

    override fun onDeviceConnected(deviceInfo: DeviceInfo) {
        Log.d(TAG, "Device connected to AP: ${deviceInfo.macAddress}")
        isApConnected = true
        startConnectionEstablishing()
    }

    override fun onDeviceDisconnected(deviceInfo: DeviceInfo) {
        Log.d(TAG, "Device disconnected from AP: ${deviceInfo.macAddress}")
        if (isApConnected) {
            shutdown()
        }
        isApConnected = false
    }

    override fun onApStartFailed(reason: Int) {
        Log.e(TAG, "AP start failed: $reason")
        requestWifiInfo()
    }

    override fun requestWifiInfo() {
        requestWifiInfo()
    }

    override fun onWifiState(opened: Boolean) {
        onWifiState(opened)
    }
}

// 替换P2P管理器
private val wifiApManager: WiFiApManager = WiFiApManager(mApListener)

// 修改连接方法
fun startWifiApConnection(context: Context, ssid: String, password: String) {
    Log.d(TAG, "Starting WiFi AP connection")
    mContext = context
    wifiApManager.startWifiAp(ssid, password)
}
```

## 4. 修改ConnectionManager.kt

```kotlin
// 在ConnectionManager.kt中的修改部分

/**
 * 启动WiFi AP模式连接
 */
fun startWifiApConnection(context: Context, ssid: String, password: String) {
    if (_connectionStatus.value.wifiStatus is WifiStatus.DeviceConnected) {
        Log.d(TAG, "WiFi already connected")
        return
    }
    mDisplayNaviManager.startWifiApConnection(context, ssid, password)
}

/**
 * 请求平台 Wifi 信息 - 修改为AP模式
 */
fun requestWifiInfo(isReset: Boolean = true) {
    Log.d(TAG, "requestWifiInfo ${_connectionStatus.value.btStatus}")
    if (_connectionStatus.value.btStatus is BleStatus.DeviceConnected) {
        ModuleInject.messageManager.queueOutgoing(
            RiderProtocol.NaviMessageId.MSG_WIFI_INFO_REQUEST_VALUE,
            RiderProtocol.WifiInfoRequest
                .newBuilder()
                .setWifiMode(RiderProtocol.WifiMode.WIFI_AP) // 改为AP模式
                .setIsResetWifi(isReset).build()
        )
    }
}
```

## 5. 修改RiderService.kt

```kotlin
// 在RiderService.kt中的修改部分

private fun handleWifiInfo(msg: ByteArray?) {
    Log.d("connect analysis:", "wifi info response (AP mode)::${TimeUtils.getCurrentTimeStr()}")
    val wifiInfo = RiderProtocol.WifiInfoNotification.parseFrom(msg)
    
    // AP模式下，使用协议中的信息作为热点配置
    val apSsid = wifiInfo.name ?: "RiderService_AP"
    val apPassword = wifiInfo.password ?: "12345678"
    
    mConnectionManager.startWifiApConnection(
        getApplication().applicationContext,
        apSsid,
        apPassword
    )
}
```

## 6. 权限配置 - 修改AndroidManifest.xml

```xml
<!-- 在AndroidManifest.xml中添加AP相关权限 -->

<!-- 原有WiFi权限保持不变 -->
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />

<!-- AP模式新增权限 -->
<uses-permission android:name="android.permission.WRITE_SETTINGS" />

<!-- 网络状态权限 -->
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
```

## 7. 测试验证代码

```kotlin
/**
 * WiFi AP功能测试类
 */
class WiFiApManagerTest {
    
    fun testApCreation() {
        val apManager = WiFiApManager(object : WiFiApManagerListener {
            override fun onApEnabled(ssid: String, password: String) {
                Log.d("Test", "AP enabled: $ssid")
                assert(ssid.isNotEmpty())
                assert(password.isNotEmpty())
            }
            
            override fun onApStartFailed(reason: Int) {
                Log.e("Test", "AP start failed: $reason")
                assert(false) { "AP should start successfully" }
            }
            
            // ... 其他回调实现
        })
        
        apManager.startWifiAp("TestAP", "testpassword")
        
        // 等待AP启动
        Thread.sleep(5000)
        
        val ipAddress = apManager.getApIpAddress()
        assert(ipAddress.isNotEmpty()) { "Should have valid IP address" }
        
        apManager.stopWifiAp()
    }
}
``` 