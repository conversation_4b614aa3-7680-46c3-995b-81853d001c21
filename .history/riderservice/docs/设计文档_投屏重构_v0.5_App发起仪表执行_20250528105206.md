# RiderService SDK 重构设计文档 (投屏增强 - 应用发起，仪表执行)

> 版本：0.5
> 作者：AI 自动生成（请根据实际情况补充、修订）

---

## 1. 背景与目标

本文档基于 `设计文档_投屏重构_v0.4_仪表驱动.md`，并根据用户最新关键澄清进行调整：**投屏模式的启动由应用层通过 SDK 发起一个 `NaviMode` 指令给仪表，仪表根据此指令负责关闭现有投屏（若有）并开启新的投屏模式。SDK随后响应仪表执行后的状态变化，并协调资源和回调应用层。**

这是一种"应用发起，仪表执行，SDK响应并协调"的混合模式。

**核心目标保持不变，但实现方式和API再次调整:**

1.  **移除天气服务**: 彻底剥离。
2.  **API 精炼**: 提供简洁的 API，应用层通过指定 `NaviMode` 来请求投屏模式变更。
3.  **应用发起、仪表执行的双投屏模式**:
    *   **应用请求**: 应用调用 SDK 的 API (如 `requestNaviMode(NaviMode.MIRROR_TYPE)`)。
    *   **SDK 发送指令**: SDK 将此 `NaviMode` 发送给仪表。
    *   **仪表执行**: 仪表关闭旧模式，开启新模式。
    *   **SDK 响应与协调**:
        *   **镜像投屏 (Mirror Mode)**: 当仪表指示可以开始镜像时 (或 SDK 根据 `NaviMode` 预期可以开始时)，SDK 请求应用层提供 `MediaProjection`，然后处理屏幕捕获、编码和流式传输。
        *   **Presentation 投屏 (Presentation Mode)**: 当仪表使一个外部 `Display` 对象可用时，SDK 将此 `Display` 提供给应用层。

本文档将阐述因此调整的架构、组件职责、关键流程以及 API 设计。

---

## 2. 现有架构回顾 (简要)

(痛点在于需要清晰体现"应用发起，仪表执行"的流程，并提供相应的API)

---

## 3. 提议的新架构设计 (应用发起，仪表执行)

整体组件结构依然相似，但 `RiderService` API 和 `ProjectionManager` 的触发机制调整。

```mermaid
graph TD
    App[应用层 UI/逻辑] -- RiderService API (requestNaviMode, setMediaProjection, stopProjection) --> RS(RiderService API Facade)
    RS -- RiderServiceCallback (连接状态, 投屏事件, Display对象) --> App

    RS -- 发送NaviMode指令 --> CM(ConnectionManager)
    CM -- NaviMode指令 --> Instrument[智能仪表硬件]

    Instrument -- 状态变化 (如Display可用) --> AC(AutolinkControl 底层协议)
    AC -- Display对象/状态 --> PM(ProjectionManager)

    CM -- 网络连接 --> TransportLayer[Wi-Fi P2P/AP]
    TransportLayer -- 数据通道 --> Instrument

    RS -- 投屏请求(携带NaviMode) --> PM

    PM -- 使用 --> AC
    PM -- 使用 --> MPS(MediaProjectService 捕获/编码)

    MPS -- 视频流 --> AC
```

**核心组件职责调整:**

### 3.1 `RiderService.kt` (API Facade)
*   **职责**:
    *   提供 API 允许应用层通过指定 `NaviMode` 来请求切换投屏模式，例如 `requestNaviMode(targetNaviMode: YourNaviModeEnum)`。
    *   将应用层请求的 `targetNaviMode` 通知给 `ProjectionManager` (用于记录期望状态) 和 `ConnectionManager` (用于发送给仪表)。
    *   管理回调，分发连接、消息、以及由仪表执行 `NaviMode` 后触发的投屏相关事件。
    *   提供 `setMediaProjection()` API。
    *   提供 `stopCurrentProjection(naviModeForStop: YourNaviModeEnum)` API，同样通过发送 `NaviMode` 给仪表来停止。
*   **主要API (详见第5节)**: `init()`, `addCallback()`, `removeCallback()`, `startBleScan()`, `connectBle()`, `disconnect()`, `requestNaviMode()`, `setMediaProjection()`, `stopCurrentProjection()`, `sendMessageToRiderService()`。

### 3.2 `ConnectionManager.kt` (连接与指令发送)
*   **职责**:
    *   管理 BLE 和 Wi-Fi 连接。
    *   负责将 `RiderService` 传递过来的 `NaviMode` 指令发送给智能仪表。
    *   (可选) 接收仪表对 `NaviMode` 指令的 ACK 或其他响应，并可能通知 `ProjectionManager`。

### 3.3 `ProjectionManager.kt` (投屏状态与资源管理器)
*   **职责**:
    *   从 `RiderService` 接收应用层期望的 `targetNaviMode`，并记录此期望状态。
    *   **等待并响应仪表执行 `NaviMode` 后的实际状态变化** (这些状态变化通常由底层如 `AutolinkControl` 回调上来)。
    *   **响应镜像模式**: 当接收到 `targetNaviMode` 是镜像类型，并且仪表端已准备好 (可能通过 `AutolinkControl` 的某个信号，或无显式信号但达到预设条件)，则通过 `RiderServiceCallback.onMirrorModeRequiresMediaProjection()` 请求 `MediaProjection`。获取后启动捕获与推流。
    *   **响应 Presentation 模式**: 当接收到 `targetNaviMode` 是 Presentation 类型，并且 `AutolinkControl` 回调一个可用的 `Display` 对象时，通过 `RiderServiceCallback.onPresentationModeDisplayAvailable(display)` 将其提供给应用。
    *   **响应停止指令或意外的模式结束**: 当通过 `stopCurrentProjection` 清理，或仪表因其他原因停止当前模式时，停止活动，释放资源，并回调 `onMirrorModeStopped` 或 `onPresentationModeDisplayReleased`。
    *   管理投屏资源。

### 3.4 `DisplayNaviManager.kt` (重构或移除)
*   职责进一步削弱。

### 3.5 `AutolinkControl.kt` 和 `MediaProjectService.java` (底层实现模块)
*   `AutolinkControl` 负责与仪表通信，发送 `NaviMode` (由 `ConnectionManager` 触发)，并负责监听仪表端 `Display` 的可用性并回调给 `ProjectionManager`。
*   `MediaProjectService` 仍负责捕获与编码。

---

## 4. 关键流程重设计 (应用发起，仪表执行)

### 4.1 连接建立流程 (同 v0.3/v0.4)
(...)

### 4.2 应用请求镜像投屏 (Mirror Mode)
1.  **应用**: 调用 `RiderService.instance.requestNaviMode(NaviMode.MIRROR_TYPE_EXAMPLE)`。
2.  **`RiderService`**:
    *   通知 `ProjectionManager`：应用期望进入 `NaviMode.MIRROR_TYPE_EXAMPLE`。
    *   通过 `ConnectionManager` 将 `NaviMode.MIRROR_TYPE_EXAMPLE` 发送给仪表。
3.  **`ConnectionManager`**: 发送 `NaviMode` 给仪表。
4.  **仪表**: 接收 `NaviMode`。关闭当前投屏 (若有)，准备进入镜像模式。 (仪表可能回复 ACK，或不回复，SDK 继续等待后续步骤)。
5.  **`ProjectionManager`**: (由于已记录期望模式为镜像)
    *   等待条件满足 (例如，仪表发回特定状态，或简单地假设仪表在收到指令后会很快准备好)。
    *   确认网络已就绪。
    *   通过 `RiderServiceCallback.onMirrorModeRequiresMediaProjection()` 请求应用层 `MediaProjection`。
6.  **应用**: 用户授权，获取 `MediaProjection`，调用 `RiderService.instance.setMediaProjection(mediaProjection)`。
7.  **`RiderService`**: 转发 `MediaProjection` 给 `ProjectionManager`。
8.  **`ProjectionManager`**:
    *   使用 `MediaProjection` 启动屏幕捕获、编码和流式传输。
    *   通过 `RiderServiceCallback.onMirrorModeStarted()` 通知应用。

### 4.3 应用请求 Presentation 投屏 (Presentation Mode)
1.  **应用**: 调用 `RiderService.instance.requestNaviMode(NaviMode.PRESENTATION_TYPE_EXAMPLE)`。
2.  **`RiderService`**:
    *   通知 `ProjectionManager`：应用期望进入 `NaviMode.PRESENTATION_TYPE_EXAMPLE`。
    *   通过 `ConnectionManager` 将 `NaviMode.PRESENTATION_TYPE_EXAMPLE` 发送给仪表。
3.  **`ConnectionManager`**: 发送 `NaviMode` 给仪表。
4.  **仪表**: 接收 `NaviMode`。关闭当前投屏 (若有)，准备 Presentation 模式，使外部 `Display` 可用。
5.  **`AutolinkControl`**: (当仪表使 `Display` 就绪时) 检测到 `Display` 可用，通过回调将 `Display` 对象给 `ProjectionManager`。
6.  **`ProjectionManager`**: (由于已记录期望模式为 Presentation，并收到了 `Display` 对象)
    *   确认网络已就绪。
    *   通过 `RiderServiceCallback.onPresentationModeDisplayAvailable(display: Display)` 将 `Display` 提供给应用层。
7.  **应用**: 接收 `Display` 对象，创建并显示 `android.app.Presentation`。

### 4.4 应用请求停止当前投屏
1.  **应用**: 调用 `RiderService.instance.stopCurrentProjection(NaviMode.DEFAULT_IDLE_EXAMPLE)`。
2.  **`RiderService`**:
    *   通知 `ProjectionManager`：应用请求停止，并将进入 `NaviMode.DEFAULT_IDLE_EXAMPLE`。
    *   通过 `ConnectionManager` 将 `NaviMode.DEFAULT_IDLE_EXAMPLE` 发送给仪表。
3.  **`ConnectionManager`**: 发送 `NaviMode` 给仪表。
4.  **仪表**: 接收 `NaviMode`，关闭当前投屏。
5.  **`ProjectionManager`**:
    *   停止当前投屏活动 (镜像则停止捕获/推流，Presentation 则准备释放 Display)。
    *   释放相关资源。
    *   通过 `RiderServiceCallback.onMirrorModeStopped()` 或 `onPresentationModeDisplayReleased(display)` 通知应用。

---

## 5. API 设计 (RiderService 公共接口 - 应用发起版)

```kotlin
// 假设有一个 NaviMode 的枚举或常量定义，应用层和SDK内部都清楚其含义
// enum class NaviModeType { MIRROR_DEFAULT, PRESENTATION_NAV, IDLE, ... }

object RiderService {
    fun init(application: Application)
    fun addCallback(callback: RiderServiceCallback)
    fun removeCallback(callback: RiderServiceCallback)
    fun startBleScan()
    fun stopBleScan()
    fun connectBle(device: BleDevice)
    fun disconnect()

    /**
     * 请求仪表切换到指定的 NaviMode。
     * SDK 会将此 NaviMode 发送给仪表。仪表执行后，相关的投屏事件会通过 RiderServiceCallback 回调。
     * @param targetNaviMode 应用期望仪表进入的 NaviMode。
     */
    fun requestNaviMode(targetNaviMode: YourNaviModeEnumType) // YourNaviModeEnumType 是代表NaviMode的类型

    /**
     * (由应用在 onMirrorModeRequiresMediaProjection 回调后调用)
     * 设置 MediaProjection 对象给 SDK。
     */
    fun setMediaProjection(mediaProjection: MediaProjection)

    /**
     * 请求停止当前所有投屏活动，并让仪表切换到指定的 NaviMode (通常是空闲或默认模式)。
     * @param naviModeForStop 应用期望仪表在停止投屏后进入的 NaviMode。
     */
    fun stopCurrentProjection(naviModeForStop: YourNaviModeEnumType)

    fun sendMessageToRiderService(message: RiderMessage)
    fun destroy()
}
```

`RiderServiceCallback` 接口保持不变，应用层依然通过它来响应投屏生命周期的各个阶段。

---

## 6. 移除天气服务

(同 v0.3/v0.4 文档，保持不变)

---

## 7. 总结与后续步骤

此"应用发起，仪表执行"模型更加符合用户描述的交互方式。SDK 提供 API 给应用层以请求模式变更 (通过 `NaviMode`)，然后发送指令给仪表，并响应仪表执行后的状态。

**后续步骤**:
1.  **代码实现**:
    *   `RiderService` 实现 `requestNaviMode` 和 `stopCurrentProjection`，负责通知 `ProjectionManager` 期望状态，并通过 `ConnectionManager` 发送 `NaviMode`。
    *   `ProjectionManager` 根据期望状态以及底层 `AutolinkControl` 的回调来触发正确的应用层回调和资源管理。
    *   `ConnectionManager` 实现发送 `NaviMode` 给仪表。
2.  **测试**: 重点测试应用发起不同 `NaviMode` 后，仪表正确响应，以及 SDK 正确处理后续状态和回调的场景。
3.  **文档更新**: 确保所有文档准确反映此模型。

---

> 本文档已根据"应用发起，仪表执行"的投屏模式澄清进行更新。 