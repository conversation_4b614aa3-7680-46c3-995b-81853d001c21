# RiderService SDK 设计文档

![版本](https://img.shields.io/badge/版本-1.0-blue)
![状态](https://img.shields.io/badge/状态-最新-green)

---

## 目录

- [项目概述](#项目概述)
- [系统架构](#系统架构)
- [核心模块](#核心模块)
- [连接机制](#连接机制)
- [投屏系统](#投屏系统)
- [通信协议](#通信协议)
- [API 设计](#api-设计)
- [典型流程](#典型流程)
- [集成指南](#集成指南)
- [错误处理](#错误处理)
- [技术规范](#技术规范)

---

## 项目概述

### 核心目标

RiderService SDK 是一套面向 **Android 手机与两轮车智能仪表** 的完整通信解决方案，提供双向通信、导航投屏和数据服务功能。

### 核心特性

- **双重连接**: BLE + Wi-Fi AP 模式的稳定连接方案
- **双投屏模式**: 支持镜像投屏和 Presentation 投屏
- **应用发起**: 应用主导的投屏模式切换机制
- **安全认证**: 内置激活校验和合规检查
- **消息同步**: 通知、导航等业务数据实时传输
- **简化集成**: 统一、简洁的 API 设计

### 项目结构

```
RiderService_SDK/
├── app/                    # 示例应用
└── riderservice/           # SDK 核心
    ├── src/main/java/      # 业务逻辑（Kotlin/Java）
    ├── src/main/cpp/       # Native 实现（C++）
    ├── src/main/proto/     # Protocol Buffers 定义
    └── docs/               # 文档
```

---

## 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "应用层"
        App[Android应用]
        AppCallback[RiderServiceCallback]
    end
    
    subgraph "API层"
        RiderServiceAPI[RiderService单例]
        ModuleInject[ModuleInject依赖注入]
    end
    
    subgraph "管理层"
        ConnectionManager[ConnectionManager连接管理器]
        MessageManager[MessageManager消息管理器]
    end
    
    subgraph "BLE连接层"
        RiderBleManager[RiderBleManager]
        BleManagerImpl[BleManagerImpl]
        BleScanner[BLE扫描器]
    end
    
    subgraph "Wi-Fi连接层"
        DisplayNaviManager[DisplayNaviManager显示导航管理器]
        SoftP2pManager[SoftP2pManager软AP管理器]
        TcpConnection[TCP连接]
    end
    
    subgraph "投屏层"
        AutolinkControl[AutolinkControl]
        Transport[Transport传输层]
        VideoEncoder[视频编码器]
        LockScreenPresentation[LockScreenPresentation]
    end
    
    subgraph "协议层"
        ProtocolBuffers[Protocol Buffers]
        NaviMessage[NaviMessage消息封装]
    end
    
    subgraph "硬件设备"
        Device[智能仪表设备]
    end

    App --> RiderServiceAPI
    RiderServiceAPI --> AppCallback
    RiderServiceAPI --> ModuleInject
    
    ModuleInject --> ConnectionManager
    ModuleInject --> MessageManager
    
    ConnectionManager --> RiderBleManager
    ConnectionManager --> DisplayNaviManager
    
    RiderBleManager --> BleManagerImpl
    RiderBleManager --> BleScanner
    
    DisplayNaviManager --> SoftP2pManager
    DisplayNaviManager --> AutolinkControl
    DisplayNaviManager --> TcpConnection
    
    AutolinkControl --> Transport
    AutolinkControl --> VideoEncoder
    AutolinkControl --> LockScreenPresentation
    
    MessageManager --> ProtocolBuffers
    MessageManager --> NaviMessage
    
    BleManagerImpl --> Device
    TcpConnection --> Device
    Transport --> Device
```

### 核心模块交互流程

```mermaid
sequenceDiagram
    participant App as Android应用
    participant API as RiderService API
    participant CM as ConnectionManager
    participant BLE as RiderBleManager
    participant MSG as MessageManager
    participant WIFI as DisplayNaviManager
    participant AL as AutolinkControl
    participant Device as 智能仪表

    Note over App, Device: 1. 初始化和扫描阶段
    App ->> API: init(application)
    API ->> CM: 初始化连接管理器
    App ->> API: addCallback(callback)
    App ->> API: startBleScan()
    API ->> CM: startScan()
    CM ->> BLE: startScan()
    BLE ->> App: onScanResult(devices)
    
    Note over App, Device: 2. BLE连接建立阶段
    App ->> API: connectBle(device)
    API ->> CM: connectBle(device)
    CM ->> BLE: connect(device)
    BLE ->> Device: BLE连接请求
    Device ->> BLE: 连接成功
    BLE ->> CM: onDeviceConnected()
    CM ->> App: onConnectStatusChange(BLE_CONNECTED)
    
    Note over App, Device: 3. 协议握手和配置阶段
    BLE ->> MSG: onDeviceReady()
    MSG ->> Device: MSG_CONFIG_REQUEST
    Device ->> MSG: MSG_CONFIG_NOTIFICATION
    MSG ->> CM: handleConfig()
    CM ->> App: onConfigChange(config)
    
    Note over App, Device: 4. Wi-Fi连接建立阶段
    CM ->> Device: MSG_WIFI_INFO_REQUEST
    Device ->> CM: MSG_WIFI_INFO_NOTIFICATION(SSID,密码)
    CM ->> WIFI: connectWifi(info)
    WIFI ->> Device: 连接AP网络
    WIFI ->> WIFI: 启动TCP服务器(30512)
    WIFI ->> Device: 发送SDK IP地址(通过BLE)
    Device ->> WIFI: TCP连接请求
    WIFI ->> CM: onDeviceConnected()
    CM ->> App: onConnectStatusChange(WIFI_CONNECTED)
    
    Note over App, Device: 5. 投屏模式准备阶段
    WIFI ->> AL: start()
    AL ->> WIFI: onVideoChannelReady()
    WIFI ->> App: onVideoChannelReady()
```

### 投屏模式状态机

```mermaid
stateDiagram-v2
    [*] --> Idle: 初始状态
    
    Idle --> BLE_Scanning: startBleScan()
    BLE_Scanning --> BLE_Connecting: connectBle()
    BLE_Connecting --> BLE_Connected: 连接成功
    BLE_Connected --> WiFi_Connecting: 获取AP信息
    WiFi_Connecting --> FullyConnected: WiFi连接成功
    
    FullyConnected --> MirrorMode_Preparing: sendNaviModeStart(MIRROR_NAVI)
    MirrorMode_Preparing --> MirrorMode_WaitingPermission: 仪表端准备完成
    MirrorMode_WaitingPermission --> MirrorMode_Active: setMediaProjection()
    
    FullyConnected --> ScreenMode_Preparing: sendNaviModeStart(SCREEN_NAVI)
    ScreenMode_Preparing --> ScreenMode_Active: onDisplayInitialized()
    
    FullyConnected --> CruiseMode_Preparing: sendNaviModeStart(CRUISE_NAVI) 
    CruiseMode_Preparing --> CruiseMode_Active: onDisplayInitialized()
    
    MirrorMode_Active --> FullyConnected: sendNaviModeStop()
    ScreenMode_Active --> FullyConnected: sendNaviModeStop()
    CruiseMode_Active --> FullyConnected: sendNaviModeStop()
    
    FullyConnected --> Disconnected: disconnect()
    BLE_Connected --> Disconnected: 连接异常
    WiFi_Connecting --> Disconnected: 连接失败
    MirrorMode_Active --> Disconnected: 连接断开
    ScreenMode_Active --> Disconnected: 连接断开
    CruiseMode_Active --> Disconnected: 连接断开
    
    Disconnected --> [*]: 清理资源
```

### 消息流转架构

```mermaid
graph TB
    subgraph "应用层消息"
        AppMsg[业务消息]
        NaviInfo[导航信息]
        NotificationInfo[通知信息]
        WeatherInfo[天气信息]
    end
    
    subgraph "SDK消息管理"
        MessageManager[MessageManager]
        OutgoingQueue[发送队列]
        IncomingQueue[接收队列]
        ProtoBufCodec[ProtoBuf编解码器]
    end
    
    subgraph "传输层"
        BLEChannel[BLE通道]
        WiFiChannel[WiFi通道]
    end
    
    subgraph "协议处理"
        FragmentHandler[分片处理器]
        MessageRouter[消息路由器]
    end
    
    subgraph "设备端"
        DeviceProtocol[设备协议栈]
    end

    AppMsg --> MessageManager
    NaviInfo --> MessageManager
    NotificationInfo --> MessageManager
    WeatherInfo --> MessageManager
    
    MessageManager --> ProtoBufCodec
    ProtoBufCodec --> OutgoingQueue
    OutgoingQueue --> FragmentHandler
    
    FragmentHandler --> BLEChannel
    FragmentHandler --> WiFiChannel
    
    BLEChannel --> DeviceProtocol
    WiFiChannel --> DeviceProtocol
    
    DeviceProtocol --> BLEChannel
    DeviceProtocol --> WiFiChannel
    
    BLEChannel --> MessageRouter
    WiFiChannel --> MessageRouter
    MessageRouter --> IncomingQueue
    IncomingQueue --> ProtoBufCodec
    ProtoBufCodec --> MessageManager
    MessageManager --> AppMsg
```

### 投屏技术架构

```mermaid
graph TB
    subgraph "镜像投屏(MirrorMode)"
        MediaProjection[MediaProjection权限]
        ScreenCapture[屏幕捕获]
        H264Encoder[H264硬件编码器]
        RTPTransport[RTP传输]
    end
    
    subgraph "Presentation投屏(ScreenNavi/CruiseNavi)"
        VirtualDisplay[虚拟显示器]
        PresentationView[Presentation界面]
        CustomContent[自定义内容]
    end
    
    subgraph "AutolinkControl核心"
        DisplayManager[显示管理器]
        VideoChannel[视频通道]
        ControlProtocol[控制协议]
    end
    
    subgraph "网络传输"
        TCPSocket[TCP Socket]
        WiFiAP[WiFi AP连接]
        VideoStream[视频流传输]
    end
    
    MediaProjection --> ScreenCapture
    ScreenCapture --> H264Encoder
    H264Encoder --> RTPTransport
    
    VirtualDisplay --> PresentationView
    PresentationView --> CustomContent
    
    DisplayManager --> VirtualDisplay
    VideoChannel --> RTPTransport
    ControlProtocol --> DisplayManager
    
    RTPTransport --> VideoStream
    VideoStream --> TCPSocket
    TCPSocket --> WiFiAP
    
    WiFiAP --> Device[智能仪表]
```

---

## 核心模块

### RiderService (API 门面)

> **单一入口，统一管理**

**主要职责：**

- 对外暴露统一、简洁的API接口
- 管理回调注册与事件分发
- 协调各子模块间的交互
- 处理Protocol Buffers消息路由

**核心API：**

```kotlin
object RiderService {
    // 基础功能
    fun init(application: Application)
    fun addCallback(callback: RiderServiceCallback)
    fun removeCallback(callback: RiderServiceCallback)

    // 连接管理
    fun startBleScan()
    fun stopBleScan()
    fun connect(device: BleDevice)
    fun disconnect(isManual: Boolean = true)
    fun getConnectStatus(): Connection
    fun getCurrentConnectDevice(): BleDevice?

    // 投屏控制
    fun sendNaviModeStart(naviMode: NaviMode)
    fun sendNaviModeStop(naviMode: NaviMode)
    fun setMediaProjection(mediaProjection: MediaProjection)
    fun requestLockScreenDisplay()

    // 消息通信
    fun sendMessageToRiderService(message: RiderMessage)
    fun sendNaviModeChange(naviMode: NaviMode)
    fun sendNaviModeChangeResponse(result: NaviModeChangeResult)
}
```

### ConnectionManager (连接管理器)

> **统一连接管理，双通道协调**

**主要职责：**

- BLE 设备扫描、连接、状态管理
- Wi-Fi AP 模式连接建立与维护
- 网络状态监控与异常处理
- 协议握手与版本校验
- 统一回调事件分发

**关键特性：**

- **双连接管理**：同时管理BLE和Wi-Fi连接状态
- **自动重连**：BLE断开后自动重新扫描连接
- **状态同步**：通过StateFlow响应式管理连接状态
- **异常处理**：网络异常时的降级和恢复策略

### RiderBleManager (BLE管理器)

> **BLE连接专项管理**

**主要职责：**

- BLE设备扫描与过滤（基于UUID和厂商数据）
- BLE连接建立与维护
- BLE数据传输和特征值通知
- BLE连接状态监控

**技术特点：**

- **智能扫描**：防止过度扫描的时间控制机制
- **设备过滤**：基于Service UUID(0x00ff00)和厂商ID(0x0AE7)
- **连接重试**：支持连接失败后的自动重试
- **权限管理**：Android 12+的BLE权限适配

### DisplayNaviManager (显示导航管理器)

> **投屏功能核心控制器**

**主要职责：**

- Wi-Fi AP连接管理（替代P2P模式）
- TCP服务器建立（端口30512）
- AutolinkControl协调和管理
- Display生命周期管理
- 视频传输通道建立

**工作流程：**

1. **Wi-Fi连接**：连接仪表提供的AP网络
2. **TCP服务**：启动TCP服务器等待仪表连接
3. **IP通知**：通过BLE通知仪表SDK的IP地址
4. **AutoLink启动**：建立投屏协议连接
5. **Display管理**：创建和销毁虚拟显示器

### MessageManager (消息管理器)

> **协议消息处理中心**

**主要职责：**

- Protocol Buffers 消息序列化/反序列化
- 消息分片与重组（大消息分片传输）
- 发送和接收队列管理
- 消息路由与回调分发

**消息处理机制：**

- **分片支持**：大于MTU的消息自动分片
- **队列管理**：使用ConcurrentLinkedDeque保证线程安全
- **消息路由**：根据消息ID路由到对应处理器
- **错误处理**：消息解析错误的容错机制

---

## 连接机制

### BLE 连接

**连接特点：**

- 快速配对与协议握手
- 安全的控制信令传输
- 低功耗维持连接
- 支持Android 12+新权限模型

**设备识别：**

```kotlin
// 设备过滤条件
private val FILTER_SERVICE_UUID = ParcelUuid(UUID.fromString("0000ff00-0000-1000-8000-00805f9b34fb"))
private val MANUFACTURE_ID = 0x0AE7
private val MANUFACTURE_DATA = byteArrayOf(0x72, 0x6C)
```

**用途：**

- 设备发现与配对
- Wi-Fi 凭证交换
- 控制指令传输（NaviMode切换）
- 心跳与状态监控
- IP地址通知

### Wi-Fi AP 模式

**架构变更：** `P2P模式` → `AP模式`

**连接流程：**

```mermaid
sequenceDiagram
    participant SDK as SDK
    participant Device as 仪表
    Note over SDK, Device: 1. BLE连接已建立
    SDK ->> Device: MSG_WIFI_INFO_REQUEST
    Device ->> SDK: MSG_WIFI_INFO_NOTIFICATION(SSID+密码)
    SDK ->> SDK: 连接仪表AP网络
    SDK ->> SDK: 获取自身IP地址
    SDK ->> SDK: 启动TCP服务器(端口30512)
    SDK ->> Device: 通过BLE发送SDK的IP地址
    Device ->> SDK: TCP连接请求
    SDK ->> Device: TCP连接建立
    Note over SDK, Device: 2. 数据通道建立完成
```

**关键特性：**

- **SDK作为TCP服务端**：在端口30512监听连接
- **动态IP通知**：通过BLE将SDK的IP地址发送给仪表
- **仪表重试机制**：连接失败时自动重试
- **无自动重连**：连接断开后不自动重连，由应用层决定
- **软AP管理**：SoftP2pManager负责AP连接管理

---

## 投屏系统

### 投屏模式架构

基于实际代码分析，SDK支持以下投屏模式：

| 模式 | 协议名称 | 描述 | 技术实现 |
|------|----------|------|----------|
| **SimpleNavi** | SIMPLE_NAVI | 简易导航模式 | 仅BLE数据传输 |
| **ScreenNavi** | SCREEN_NAVI | 竖屏投屏导航 | 虚拟Display + Presentation |
| **CruiseNavi** | CRUISE_NAVI | 横屏投屏导航 | 虚拟Display + Presentation |
| **MirrorNavi** | MIRROR_NAVI | 镜像投屏模式 | MediaProjection + H264编码 |
| **LockScreenNavi** | LOCK_SCREEN_NAVI | 锁屏导航模式 | LockScreenPresentation |

### 镜像投屏 (MirrorNavi)

**适用场景：** 完整屏幕内容投屏，适合地图导航、多媒体展示

**实现流程：**

```mermaid
sequenceDiagram
    participant App as 应用
    participant SDK as SDK
    participant AL as AutolinkControl
    participant Device as 仪表
    App ->> SDK: sendNaviModeStart(NaviMode.MirrorNAVI)
    SDK ->> Device: MSG_NAVI_MODE_START(MIRROR_NAVI)
    Device ->> SDK: MSG_NAVI_MODE_START_RESPONSE
    SDK ->> AL: 准备镜像模式
    AL ->> App: onRequestMediaProjection()
    App ->> App: 用户授权MediaProjection
    App ->> SDK: setMediaProjection(mediaProjection)
    SDK ->> AL: 开始屏幕捕获
    AL ->> AL: H264硬件编码
    AL ->> Device: RTP视频流传输
    AL ->> App: onMirrorStart()
```

**技术特点：**

- **MediaProjection API**：Android系统级屏幕捕获
- **H264硬件编码**：高效压缩，低延迟
- **RTP传输协议**：稳定的视频流传输
- **动态参数调整**：根据网络状况自适应

### Presentation投屏 (ScreenNavi/CruiseNavi)

**适用场景：** 自定义内容显示，适合专门的仪表界面设计

**实现流程：**

```mermaid
sequenceDiagram
    participant App as 应用
    participant SDK as SDK
    participant AL as AutolinkControl
    participant Device as 仪表
    App ->> SDK: sendNaviModeStart(NaviMode.ScreenNavi)
    SDK ->> Device: MSG_NAVI_MODE_START(SCREEN_NAVI)
    Device ->> SDK: MSG_NAVI_MODE_START_RESPONSE
    SDK ->> AL: 准备Presentation模式
    AL ->> AL: 创建虚拟Display
    AL ->> App: onDisplayInitialized(display)
    App ->> App: 创建自定义Presentation
    App ->> App: presentation.show()
    Note over App, Device: 应用完全控制显示内容
```

**模式区别：**

- **ScreenNavi**：竖屏方向，适合导航界面
- **CruiseNavi**：横屏方向，适合巡航显示

**技术特点：**

- **虚拟Display**：AutolinkControl创建的Display对象
- **原生Presentation**：使用Android标准Presentation API
- **完全自定义**：应用可自由设计界面内容
- **双向控制**：支持仪表端的触控交互

### 锁屏投屏 (LockScreenNavi)

**适用场景：** 设备锁屏状态下的导航显示

**技术特点：**

- **LockScreenPresentation**：专门的锁屏展示组件
- **系统级权限**：可在锁屏状态下显示
- **简化界面**：适合锁屏场景的简洁设计

### 投屏模式切换机制

**切换逻辑：**

```kotlin
// 智能切换逻辑（避免不必要的Display重建）
needChangeDisplay = when {
    mNaviMode == NaviMode.Default && naviMode == NaviMode.CruiseNAVI -> false
    mNaviMode == NaviMode.CruiseNAVI && naviMode == NaviMode.Default -> false  
    mNaviMode == NaviMode.MirrorNAVI && naviMode == NaviMode.MirrorNAVI -> false
    else -> true
}
```

**特点：**

- **无缝切换**：相同模式间切换无需重建Display
- **资源优化**：避免不必要的编码器重启
- **状态同步**：模式状态在SDK和仪表间同步

---

## 通信协议

### Protocol Buffers 消息定义

**核心消息类型：**

| 消息ID                         | 方向     | 描述           | 用途         |
|------------------------------|--------|--------------|------------|
| `MSG_WIFI_INFO_REQUEST`      | SDK→仪表 | 请求Wi-Fi AP信息 | 获取连接凭证     |
| `MSG_WIFI_INFO_NOTIFICATION` | 仪表→SDK | 返回AP凭证       | 提供SSID/密码  |
| `MSG_NAVI_INFO`              | SDK→仪表 | 导航信息         | 实时路况、转向指引  |
| `MSG_ANDROID_NOTIFICATION`   | SDK→仪表 | 通知同步         | 来电、短信、应用消息 |
| `MSG_CONFIG_NOTIFICATION`    | 仪表→SDK | 配置信息         | 仪表功能配置     |
| `MSG_NAVI_MODE_CHANGE`       | 双向     | 模式切换         | 投屏模式控制     |

### 消息传输通道

```mermaid
graph LR
    subgraph "BLE通道"
        A[控制信令] --> B[NaviMode]
        A --> C[配置信息]
        A --> D[心跳检测]
    end

    subgraph "Wi-Fi通道"
        E[业务数据] --> F[导航信息]
        E --> G[通知消息]
        E --> H[视频流]
    end
```

---

## API 设计

### 核心接口

```kotlin
interface RiderServiceCallback {
    // 连接状态回调
    fun onScanResult(devices: List<BleDevice>)
    fun onBleConnectionStateChanged(status: BleConnectionStatus, device: BluetoothDevice?)
    fun onWifiConnectionStateChanged(status: WifiConnectionStatus)

    // 镜像投屏回调
    fun onMirrorModeRequiresMediaProjection()
    fun onMirrorModeStarted()
    fun onMirrorModeStopped()

    // Presentation投屏回调
    fun onPresentationModeDisplayAvailable(display: Display)
    fun onPresentationModeDisplayReleased(display: Display)

    // 其他回调
    fun onRiderServiceConfigChanged(config: RiderServiceConfig)
    fun onDialogShow(title: String, message: String)
}
```

### 业务数据模型

```kotlin
// 导航信息
data class NaviInfo(
    val curStep: Int,                    // 当前步骤
    val curStepRetainDistance: Int,      // 剩余距离
    val nextRoadName: String,            // 下一路段名称
    val iconType: Int,                   // 转向图标类型
    val totalDistance: Int,              // 总距离
    val totalTime: Int                   // 总时间
)

// 通知信息
data class AndroidNotification(
    val packageName: String,             // 应用包名
    val title: String,                   // 通知标题
    val content: String,                 // 通知内容
    val timestamp: Long                  // 时间戳
)
```

---

## 典型流程

### 设备连接流程

```kotlin
// 1. 初始化SDK
RiderService.instance.init(application)
RiderService.instance.addCallback(callback)

// 2. 扫描设备
RiderService.instance.startBleScan()

// 3. 连接设备（在onScanResult回调中选择）
RiderService.instance.connectBle(selectedDevice)

// 4. 等待连接完成（通过回调通知）
```

### 镜像投屏流程

```kotlin
// 1. 请求镜像模式
RiderService.instance.requestNaviMode(NaviModeType.MIRROR)

// 2. 处理权限请求（在onMirrorModeRequiresMediaProjection回调中）
override fun onMirrorModeRequiresMediaProjection() {
    val intent = mediaProjectionManager.createScreenCaptureIntent()
    startActivityForResult(intent, REQUEST_CODE_MEDIA_PROJECTION)
}

// 3. 设置MediaProjection（在onActivityResult中）
override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    if (requestCode == REQUEST_CODE_MEDIA_PROJECTION && resultCode == RESULT_OK) {
        val mediaProjection = mediaProjectionManager.getMediaProjection(resultCode, data)
        RiderService.instance.setMediaProjection(mediaProjection)
    }
}

// 4. 投屏开始（通过onMirrorModeStarted回调通知）
```

### Presentation投屏流程

```kotlin
// 1. 请求Presentation模式
RiderService.instance.requestNaviMode(NaviModeType.PRESENTATION)

// 2. 处理Display可用（在回调中）
override fun onPresentationModeDisplayAvailable(display: Display) {
    // 创建自定义Presentation
    val presentation = MyCustomPresentation(this, display)
    presentation.show()
}

// 3. 处理Display释放（在回调中）
override fun onPresentationModeDisplayReleased(display: Display) {
    presentation?.dismiss()
    presentation = null
}
```

---

## 集成指南

### Gradle 配置

```kotlin
// settings.gradle.kts
include(":app", ":riderservice")

// app/build.gradle.kts
dependencies {
    implementation(project(":riderservice"))
    // 或远程依赖
    // implementation("com.link:riderservice:1.0.0")
}
```

### 权限配置

```xml
<!-- AndroidManifest.xml -->
<uses-permission android:name="android.permission.BLUETOOTH" /><uses-permission
android:name="android.permission.BLUETOOTH_ADMIN" /><uses-permission
android:name="android.permission.ACCESS_FINE_LOCATION" /><uses-permission
android:name="android.permission.ACCESS_WIFI_STATE" /><uses-permission
android:name="android.permission.CHANGE_WIFI_STATE" /><uses-permission
android:name="android.permission.INTERNET" />

    <!-- Android 12+ 蓝牙权限 -->
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" /><uses-permission
android:name="android.permission.BLUETOOTH_CONNECT" />
```

### 基本集成示例

```kotlin
class MainActivity : AppCompatActivity() {

    private val riderServiceCallback = object : RiderServiceCallback {
        override fun onBleConnectionStateChanged(
            status: BleConnectionStatus,
            device: BluetoothDevice?
        ) {
            when (status) {
                BleConnectionStatus.CONNECTED -> {
                    showToast("设备连接成功")
                }
                BleConnectionStatus.DISCONNECTED -> {
                    showToast("设备连接断开")
                }
            }
        }

        override fun onMirrorModeRequiresMediaProjection() {
            requestMediaProjectionPermission()
        }

        // ... 其他回调实现
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // 初始化SDK
        RiderService.instance.init(application)
        RiderService.instance.addCallback(riderServiceCallback)
    }

    override fun onDestroy() {
        RiderService.instance.removeCallback(riderServiceCallback)
        super.onDestroy()
    }
}
```

---

## 错误处理

### 连接错误

| 错误类型      | 原因          | 处理策略     |
|-----------|-------------|----------|
| BLE扫描失败   | 权限不足、蓝牙未开启  | 引导用户开启权限 |
| BLE连接超时   | 设备距离过远、信号干扰 | 提示用户靠近设备 |
| Wi-Fi连接失败 | AP信息错误、网络异常 | 重新获取AP凭证 |
| TCP连接失败   | 端口被占用、防火墙阻拦 | 检查网络配置   |

### 投屏错误

| 错误类型                | 原因         | 处理策略      |
|---------------------|------------|-----------|
| MediaProjection权限拒绝 | 用户拒绝授权     | 引导用户重新授权  |
| 编码器初始化失败            | 硬件不支持、资源不足 | 降低编码参数    |
| Display获取失败         | 仪表端异常、协议错误 | 重试或切换模式   |
| 网络传输中断              | 网络不稳定、连接断开 | 自动重连或提示用户 |

### 错误码定义

```kotlin
enum class RiderServiceError(val code: Int, val message: String) {
    // 连接错误 (1xxx)
    BLE_PERMISSION_DENIED(1001, "蓝牙权限被拒绝"),
    BLE_CONNECTION_TIMEOUT(1002, "BLE连接超时"),
    WIFI_CONNECTION_FAILED(1003, "Wi-Fi连接失败"),

    // 投屏错误 (2xxx)
    MEDIA_PROJECTION_DENIED(2001, "屏幕投射权限被拒绝"),
    ENCODER_INIT_FAILED(2002, "编码器初始化失败"),
    DISPLAY_NOT_AVAILABLE(2003, "外部显示器不可用"),

    // 协议错误 (3xxx)
    PROTOCOL_VERSION_MISMATCH(3001, "协议版本不匹配"),
    MESSAGE_PARSE_ERROR(3002, "消息解析错误"),
    DEVICE_NOT_SUPPORTED(3003, "设备不支持")
}
```

---

## 技术规范

### 性能指标

| 指标        | 目标值       | 备注       |
|-----------|-----------|----------|
| BLE连接时间   | < 3秒      | 从扫描到连接完成 |
| Wi-Fi连接时间 | < 5秒      | AP模式连接建立 |
| 投屏启动时间    | < 2秒      | 从请求到开始推流 |
| 视频延迟      | < 200ms   | 端到端延迟    |
| 视频帧率      | 30fps     | 镜像模式     |
| 视频分辨率     | 1920x1080 | 最大支持分辨率  |

### 兼容性要求

| 项目        | 要求             | 备注                |
|-----------|----------------|-------------------|
| Android版本 | ≥ API 21 (5.0) | 核心功能支持            |
| 投屏功能      | ≥ API 24 (7.0) | MediaProjection需求 |
| 蓝牙版本      | BLE 4.0+       | 兼容性考虑             |
| Wi-Fi标准   | 802.11n+       | 保证传输速度            |

### 资源占用

| 资源    | 限制       | 说明          |
|-------|----------|-------------|
| 内存使用  | < 100MB  | 包含编码缓冲区     |
| CPU占用 | < 20%    | 镜像模式下       |
| 网络带宽  | < 10Mbps | 1080p@30fps |
| 电池消耗  | < 5%/小时  | 相对基准测试      |

---

## 后续规划

### 短期目标 (v1.1)

- [ ] 增强错误处理和重连机制
- [ ] 优化视频编码参数和传输协议
- [ ] 完善单元测试和集成测试
- [ ] 提供详细的API文档和示例

### 中期目标 (v1.5)

- [ ] 支持多设备同时连接
- [ ] 增加端到端加密
- [ ] 实现云端设备管理
- [ ] 支持音频传输

### 长期目标 (v2.0)

- [ ] 跨平台支持（iOS、Flutter）
- [ ] AI辅助的智能投屏
- [ ] 5G网络优化
- [ ] AR/VR集成支持

---

> **文档维护**：本文档将随SDK版本更新而持续维护，确保内容的准确性和时效性。
>
> **技术支持**：如有疑问或建议，请通过Issue或邮件联系维护团队。

---

**文档版本**: 1.0  
**最后更新**: 2024年  
**维护团队**: RiderService SDK 开发团队 