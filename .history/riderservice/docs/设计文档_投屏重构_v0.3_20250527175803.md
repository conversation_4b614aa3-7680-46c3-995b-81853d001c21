# RiderService SDK 重构设计文档 (投屏增强)

> 版本：0.3
> 作者：AI 自动生成（请根据实际情况补充、修订）

---

## 1. 背景与目标

本文档基于原始 `设计文档.md` (版本 0.2)，针对用户提出的代码重构需求进行深化设计，特别是围绕**移除天气服务、简化API、以及提供两种清晰的投屏方式（镜像模式 Mirror Mode 和 Presentation 模式）**。

**核心目标:**

1.  **移除天气服务**: 彻底从 SDK 中剥离天气相关的所有 API、实现和依赖。
2.  **API 精炼**: 提供更少、更通俗易懂的公共 API 接口，降低应用层集成复杂度。
3.  **双投屏模式**: SDK 需明确支持并提供独立的 API 来控制以下两种投屏方式：
    *   **镜像投屏 (Mirror Mode)**: SDK 内部处理大部分屏幕捕获、编码和流式传输逻辑。应用层负责在 SDK 请求时获取 `MediaProjection` 权限，并控制投屏的开始与结束。
    *   **Presentation 投屏 (Presentation Mode)**: SDK 负责从仪表端获取或协商出一个可用的外部 `Display` 对象，并将其提供给应用层。应用层获得此 `Display` 对象后，可以完全自定义其上显示的内容（例如，通过创建标准的 `android.app.Presentation` 实例），并控制自定义演示的开始与结束。

本文档将详细阐述为达成以上目标所需的架构调整、核心组件职责变化、关键流程以及新的 API 设计。

---

## 2. 现有架构回顾 (简要)

（此部分内容可参考 `设计文档.md` v0.2，此处不再赘述，仅强调与本次重构相关的关键点）

*   **`RiderService`**: API 入口，但部分投屏 API 不够清晰，Presentation 模式支持不明确。
*   **`ConnectionManager`**: 管理 BLE 和 Wi-Fi 连接，同时也代理了部分投屏控制逻辑给 `DisplayNaviManager`。
*   **`DisplayNaviManager`**: 包含 Wi-Fi P2P 管理和 `AutolinkControl` 的使用，是当前投屏功能的主要实现区域。
*   **`AutolinkControl`**: 底层投屏协议 (Autolink) 的封装，使用 `MediaProjectService` 进行屏幕捕获和编码，并通过 `ALIntegration` 与仪表通信，能获取到底层 `Display` 对象。

**主要痛点**: 投屏模式 API 不清晰，Presentation 模式未作为通用功能暴露，职责划分有待优化。

---

## 3. 提议的新架构设计

为了实现清晰的双投屏模式并优化职责，提议引入新的核心组件 `ProjectionManager`，并调整现有组件的角色。

```mermaid
graph TD
    App[应用层 UI/逻辑] -- RiderService API --> RS(RiderService API Facade)
    RS -- RiderServiceCallback --> App

    RS -- 连接请求 --> CM(ConnectionManager)
    RS -- 投屏请求 --> PM(ProjectionManager)

    CM -- 网络状态 --> PM
    CM -- 网络连接 --> TransportLayer[Wi-Fi P2P/AP]
    TransportLayer -- 数据通道 --> Instrument[智能仪表硬件]

    PM -- 使用 --> AC(AutolinkControl 底层协议)
    PM -- 使用 --> MPS(MediaProjectService 捕获/编码)

    AC -- 控制/数据 --> TransportLayer
    MPS -- 视频流 --> AC

    Instrument -- BLE 控制信令 --> CM
```

**核心组件职责调整:**

### 3.1 `RiderService.kt` (API Facade)
*   **职责**: 作为 SDK 对应用层暴露的唯一、统一、简洁的接口。
    *   管理 `RiderServiceCallback` 的注册与回调分发。
    *   将所有连接相关的请求转发给 `ConnectionManager`。
    *   将所有投屏相关的请求（两种模式的启停、`MediaProjection` 的设置）转发给 `ProjectionManager`。
*   **主要API ( conceptual, 详见第5节 API 设计 )**: `init()`, `addCallback()`, `removeCallback()`, `startBleScan()`, `connectBle()`, `disconnect()`, `startMirrorMode()`, `setMediaProjection()`, `stopMirrorMode()`, `startPresentationMode()`, `stopPresentationMode()`, `sendMessageToRiderService()`。

### 3.2 `ConnectionManager.kt` (连接管理器)
*   **职责**: 专注于网络连接的建立、维护和状态管理。
    *   管理 BLE 扫描、连接、断开和状态回调。
    *   管理 Wi-Fi (P2P 或 AP 模式) 的连接建立、断开和状态回调。
    *   当 Wi-Fi 数据通道准备就绪或断开时，通知 `ProjectionManager`，以便其判断是否可以进行投屏操作。
    *   **不再直接参与**投屏的启动/停止或 `MediaProjection` 对象的传递。

### 3.3 `ProjectionManager.kt` (新增 - 投屏管理器)
*   **职责**: **SDK内部所有投屏相关逻辑的中央处理单元。**
    *   接收并处理来自 `RiderService` 的镜像投屏和 Presentation 投屏的启动/停止请求。
    *   **镜像投屏 (Mirror Mode) 管理**:
        *   通过 `RiderServiceCallback.onMirrorModeRequiresMediaProjection()` 请求应用层提供 `MediaProjection` 对象。
        *   接收应用层通过 `RiderService.setMediaProjection()` 传递过来的 `MediaProjection` 对象。
        *   管理 `MediaProjectService` (或其等效逻辑) 的生命周期，负责屏幕捕获、H.264 视频编码以及将编码后的视频数据通过 `AutolinkControl` (或直接通过 `ConnectionManager` 提供的网络通道) 发送给仪表。
        *   与仪表端进行镜像投屏相关的协议交互 (例如，通知仪表开始/停止接收视频流，屏幕参数协商等，这部分可能仍依赖 `AutolinkControl`)。
        *   通过 `RiderServiceCallback.onMirrorModeStarted()` 和 `onMirrorModeStopped()` 回调通知应用层镜像投屏的启停状态。
    *   **Presentation 投屏 (Presentation Mode) 管理**:
        *   当收到 `startPresentationMode()` 请求后，通过 `AutolinkControl` (或类似机制) 与仪表端进行协商，请求或激活一个外部 `Display`。
        *   一旦成功获取到 `Display` 对象 (通常通过 `AutolinkControl` 的 `onDisplayInitialized` 回调)，则通过 `RiderServiceCallback.onPresentationModeDisplayAvailable(display)` 将此 `Display` 对象提供给应用层。
        *   当收到 `stopPresentationMode()` 请求或连接断开时，通知仪表释放该 `Display`，并通过 `RiderServiceCallback.onPresentationModeDisplayReleased(display)` 通知应用层。
    *   **状态管理**: 内部维护当前投屏状态 (如：未投屏、镜像模式运行中、Presentation模式准备中、Presentation模式运行中)，确保不同模式间的正确切换和资源管理。
    *   **资源管理**: 负责投屏过程中涉及的特定资源，如编码器实例、`VirtualDisplay` 等的创建与释放。

### 3.4 `DisplayNaviManager.kt` (重构或移除)
*   其原有的大部分关于投屏生命周期、`AutolinkControl` 使用、`MediaProjection` 处理的职责将**迁移到 `ProjectionManager`**。
*   若其还保留 Wi-Fi P2P 连接管理的独立逻辑，可以考虑将其相关功能更紧密地整合进 `ConnectionManager`，以简化层级。理想情况下，`ProjectionManager` 不直接依赖 `DisplayNaviManager`，而是通过 `ConnectionManager` 获取网络就绪状态。

### 3.5 `AutolinkControl.kt` 和 `MediaProjectService.java` (底层实现模块)
*   **职责**: 作为 `ProjectionManager` 使用的底层服务模块。
    *   `AutolinkControl`: 继续封装与仪表硬件进行具体投屏协议（如 Autolink）通信的细节。例如，发送控制命令 (切换模式、屏幕参数)、接收仪表状态、实际的视频/数据包的封装与发送 (如果协议要求特定封装的话)、以及关键的 `onDisplayInitialized` 回调触发机制。
    *   `MediaProjectService`: (或其核心逻辑) 负责具体的屏幕捕获 (使用 `MediaProjection` 创建 `VirtualDisplay`) 和视频编码 (如 H.264)。其输出的编码帧将交给 `ProjectionManager` (再由 `ProjectionManager` 通过 `AutolinkControl` 或直接的网络通道发送)。

---

## 4. 关键流程重设计

### 4.1 连接建立流程 (与之前类似，但强调与 ProjectionManager 的交互)
1.  应用调用 `RiderService.startBleScan()` -> `ConnectionManager` 处理。
2.  应用选择设备，调用 `RiderService.connectBle()` -> `ConnectionManager` 处理 BLE 连接和认证。
3.  BLE 连接成功后，`ConnectionManager` 自动发起 Wi-Fi 连接流程 (请求信息，连接P2P/AP)。
4.  当 `ConnectionManager` 确认 Wi-Fi 数据通道已建立 (socket connected)，它会**通知 `ProjectionManager` 网络已就绪**。

### 4.2 镜像投屏 (Mirror Mode) 流程
1.  **应用**: 调用 `RiderService.instance.startMirrorMode()`。
2.  **`RiderService`**: 转发请求给 `ProjectionManager.startMirrorMode()`。
3.  **`ProjectionManager`**: 
    *   检查 `ConnectionManager` 确认网络是否就绪。
    *   若就绪，通过 `RiderServiceCallback.onMirrorModeRequiresMediaProjection()` 请求应用层提供 `MediaProjection` 权限。
4.  **应用**: 用户授权后，获取 `MediaProjection` 对象。
5.  **应用**: 调用 `RiderService.instance.setMediaProjection(mediaProjection)`。
6.  **`RiderService`**: 转发 `MediaProjection` 对象给 `ProjectionManager.onMediaProjectionReady(mediaProjection)`。
7.  **`ProjectionManager`**: 
    *   使用 `MediaProjection` 初始化 `MediaProjectService` (或等效模块) 进行屏幕捕获和视频编码。
    *   通过 `AutolinkControl` (或直接的网络接口) 通知仪表进入镜像模式，并开始发送视频数据流。
    *   成功启动后，通过 `RiderServiceCallback.onMirrorModeStarted()` 通知应用。
8.  **应用 (或用户操作)**: 调用 `RiderService.instance.stopMirrorMode()`。
9.  **`RiderService`**: 转发请求给 `ProjectionManager.stopMirrorMode()`。
10. **`ProjectionManager`**: 
    *   停止 `MediaProjectService` 的捕获和编码。
    *   通过 `AutolinkControl` 通知仪表停止镜像模式。
    *   释放相关资源 (编码器, `VirtualDisplay` 等)。
    *   通过 `RiderServiceCallback.onMirrorModeStopped()` 通知应用。

### 4.3 Presentation 投屏 (Presentation Mode) 流程
1.  **应用**: 调用 `RiderService.instance.startPresentationMode()`。
2.  **`RiderService`**: 转发请求给 `ProjectionManager.startPresentationMode()`。
3.  **`ProjectionManager`**: 
    *   检查 `ConnectionManager` 确认网络是否就绪。
    *   若就绪，通过 `AutolinkControl` 向仪表请求或协商一个外部 `Display`。
4.  **`AutolinkControl`**: (当仪表准备好外部 Display 后) 调用其内部回调，最终触发 `ProjectionManager` 内的逻辑，使其获得 `Display` 对象。
5.  **`ProjectionManager`**: 通过 `RiderServiceCallback.onPresentationModeDisplayAvailable(display: Display)` 将获取到的 `Display` 对象回调给应用层。
6.  **应用**: 
    *   接收到 `Display` 对象。
    *   创建 `android.app.Presentation` 实例 (或其他自定义显示逻辑)，并使用此 `Display`。
    *   调用 `presentation.show()`。
7.  **应用 (或用户操作)**: 调用 `RiderService.instance.stopPresentationMode()`。
8.  **`RiderService`**: 转发请求给 `ProjectionManager.stopPresentationMode()`。
9.  **`ProjectionManager`**: 
    *   通过 `AutolinkControl` 通知仪表释放或关闭外部 `Display`。
    *   释放与此 `Display` 相关的本地资源。
    *   通过 `RiderServiceCallback.onPresentationModeDisplayReleased(display: Display)` 通知应用。
10. **应用**: 确保其创建的 `Presentation` 实例被正确 `dismiss()` 和清理。

---

## 5. API 设计 (RiderService 公共接口)

以下为 `RiderService` 对外暴露的主要公共 API (基于 Kotlin 风格，部分为设想，具体实现时可能调整):

```kotlin
object RiderService {
    /**
     * 初始化 SDK。
     * @param application Application 上下文。
     */
    fun init(application: Application)

    /**
     * 注册 SDK 事件回调。
     */
    fun addCallback(callback: RiderServiceCallback)

    /**
     * 反注册 SDK 事件回调。
     */
    fun removeCallback(callback: RiderServiceCallback)

    /**
     * 开始扫描 BLE 设备。
     */
    fun startBleScan()

    /**
     * 停止扫描 BLE 设备。
     */
    fun stopBleScan()

    /**
     * 连接指定的 BLE 设备。
     * @param device BleDevice 对象，通常从 onScanResult 回调中获取。
     */
    fun connectBle(device: BleDevice) // BleDevice 来自 com.link.riderservice.connection.ble.BleDevice

    /**
     * 断开当前所有连接 (BLE 和 Wi-Fi)，并停止所有投屏活动。
     */
    fun disconnect()

    // --- 镜像投屏 (Mirror Mode) ---    
    /**
     * 请求启动镜像投屏模式。
     * SDK 将通过 RiderServiceCallback.onMirrorModeRequiresMediaProjection() 请求 MediaProjection 权限。
     */
    fun startMirrorMode()

    /**
     * (由应用在 onMirrorModeRequiresMediaProjection 回调后调用)
     * 设置应用层获取到的 MediaProjection 对象给 SDK，用于屏幕捕获。
     * @param mediaProjection 用户授权后获取的 MediaProjection 实例。
     */
    fun setMediaProjection(mediaProjection: MediaProjection)

    /**
     * 请求停止镜像投屏模式。
     */
    fun stopMirrorMode()

    // --- Presentation 投屏 (Presentation Mode) ---
    /**
     * 请求启动 Presentation 投屏模式。
     * SDK 将尝试从仪表获取一个外部 Display，并通过 RiderServiceCallback.onPresentationModeDisplayAvailable(display) 回调给应用。
     */
    fun startPresentationMode()

    /**
     * 请求停止 Presentation 投屏模式，并释放相关 Display。
     */
    fun stopPresentationMode()

    /**
     * 发送业务消息给仪表 (例如：导航指令、通知信息等)。
     * @param message RiderMessage 的子类实例。
     */
    fun sendMessageToRiderService(message: RiderMessage) // RiderMessage 来自 com.link.riderservice.api.dto.RiderMessage

    /**
     * 获取 SDK 当前的整体连接状态 (BLE 和 Wi-Fi)。
     * (可以考虑返回一个包含更详细状态的 ConnectionState 对象)
     */
    // fun getCurrentConnectionState(): ConnectionState 

    /**
     * 获取仪表配置信息 (例如是否支持特定功能)。
     * 此信息通常在连接成功后通过 RiderServiceCallback.onRiderServiceConfigChanged(config) 异步获取。
     * 此处可以提供一个同步获取已缓存配置的方法，若未连接或未收到则返回 null 或默认值。
     */
    // fun getCachedRiderServiceConfig(): RiderServiceConfig?

    /**
     * 销毁 SDK 实例，释放所有资源。通常在 Application 退出时调用。
     */
    fun destroy()
}
```

`RiderServiceCallback` 接口定义已在 `设计文档.md` v0.2 中更新，此处不再重复，其包含了对新投屏模式的特定回调。

---

## 6. 移除天气服务

*   **代码层面**: 
    *   删除 `riderservice/src/main/java/com/link/riderservice/weather/` 整个包。
    *   从 `RiderService.kt` 中移除所有 `WeatherInfo` DTO 的使用、`mWeatherRepository` 成员、`getWeatherInfo()` 方法以及相关的消息处理逻辑 (`MSG_WEATHER_INFO_REQUEST_VALUE`)。
    *   从 `api/dto/RiderServiceConfig.kt` 中移除 `isSupportWeather` 字段。
    *   从 `inject/ModuleInject.kt` 中移除 `weatherRepository` 的注入。
    *   (理论上) 从 `RiderProtocol.proto` 中移除 `WeatherInfoNotification` 和 `WeatherInfoRequest` 等消息定义 (此操作需手动进行并重新编译 proto，SDK 代码中需移除对这些生成类的引用)。
*   **文档层面**: 本文档及后续所有文档中，不再包含天气服务相关内容。

---

## 7. 总结与后续步骤

此设计方案通过引入 `ProjectionManager`，明确了各个核心组件的职责，为实现稳定且易用的镜像投屏和 Presentation 投屏功能打下了良好的架构基础。同时，API 设计也朝着更简洁、用户友好的方向进行了改进。

**后续步骤**: 
1.  **代码实现**: 
    *   创建 `ProjectionManager.kt` 类，并逐步实现其核心逻辑。
    *   重构 `RiderService.kt`，实现新的公共 API，并将投屏逻辑委托给 `ProjectionManager`。
    *   调整 `ConnectionManager.kt` 的职责，使其专注于连接，并与 `ProjectionManager` 进行必要的交互。
    *   将 `DisplayNaviManager.kt` 的相关投屏逻辑迁移到 `ProjectionManager`，并考虑其自身的重构或移除。
    *   确保 `AutolinkControl.kt` 和 `MediaProjectService.java` 能被 `ProjectionManager` 正确调用。
    *   彻底移除天气服务相关代码。
2.  **测试**: 针对新的 API 和两种投屏模式编写单元测试和集成测试。
3.  **文档更新**: 确保所有面向开发者的文档 (包括本设计文档的最终版、API 参考、集成指南等) 与最终实现一致。

---

> 本重构设计文档旨在提供清晰的架构和实现思路。具体实现过程中可能需要根据实际情况进行微调。 