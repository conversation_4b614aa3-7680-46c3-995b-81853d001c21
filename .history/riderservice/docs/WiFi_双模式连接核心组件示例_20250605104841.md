# WiFi双模式连接核心组件代码示例

## ConnectionModeManager - 连接模式管理器核心实现

这个文档展示了如何实现支持P2P和AP双模式的连接管理器，默认使用AP模式，P2P作为备用方案。

```kotlin
package com.link.riderservice.connection.network

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.link.riderservice.api.RiderService
import com.link.riderservice.utils.TimeUtils
import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 连接模式管理器
 * 统一管理WiFi P2P和WiFi AP客户端两种连接模式
 */
class ConnectionModeManager(
    private val listener: ConnectionModeManagerListener
) {
    private val context = RiderService.instance.getApplication().applicationContext
    private val preferences: SharedPreferences by lazy {
        context.getSharedPreferences("wifi_connection_mode", Context.MODE_PRIVATE)
    }
    
    private var currentMode = WifiConnectionMode.AUTO
    private var targetMode = WifiConnectionMode.WIFI_AP_CLIENT // 默认AP模式
    private var isConnecting = AtomicBoolean(false)
    private var connectionJob: Job? = null
    
    // WiFi AP客户端管理器
    private val wifiClientManager: WiFiClientManager by lazy {
        WiFiClientManager(wifiClientListener)
    }
    
    // WiFi P2P管理器（复用现有）
    private val p2pManager: SoftP2pManager by lazy {
        SoftP2pManager(p2pListener)
    }
    
    companion object {
        private const val TAG = "ConnectionModeManager"
        private const val PREF_LAST_SUCCESSFUL_MODE = "last_successful_mode"
        private const val PREF_MODE_PREFERENCE = "mode_preference"
        private const val AP_TIMEOUT_MS = 15000L // AP模式连接超时
        private const val P2P_TIMEOUT_MS = 20000L // P2P模式连接超时
    }

    /**
     * 开始连接流程
     */
    fun startConnection(context: Context, forceMode: WifiConnectionMode? = null) {
        if (isConnecting.get()) {
            Log.w(TAG, "Connection already in progress")
            return
        }
        
        isConnecting.set(true)
        
        // 确定连接模式
        val selectedMode = forceMode ?: determineConnectionMode()
        currentMode = selectedMode
        
        Log.d(TAG, "Starting connection with mode: $selectedMode")
        listener.onModeSelected(selectedMode)
        
        connectionJob = CoroutineScope(Dispatchers.Main).launch {
            try {
                when (selectedMode) {
                    WifiConnectionMode.WIFI_AP_CLIENT -> {
                        attemptApConnection(context)
                    }
                    WifiConnectionMode.WIFI_P2P -> {
                        attemptP2pConnection(context)
                    }
                    WifiConnectionMode.AUTO -> {
                        attemptAutoConnection(context)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Connection failed", e)
                handleConnectionFailure(selectedMode, -999)
            }
        }
    }
    
    /**
     * 断开连接
     */
    fun disconnect() {
        Log.d(TAG, "Disconnecting current mode: $currentMode")
        
        connectionJob?.cancel()
        isConnecting.set(false)
        
        when (currentMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                wifiClientManager.disconnect()
            }
            WifiConnectionMode.WIFI_P2P -> {
                p2pManager.stop()
            }
            else -> {
                // 同时断开两种模式
                wifiClientManager.disconnect()
                p2pManager.stop()
            }
        }
    }
    
    /**
     * 自动连接模式：先尝试AP，失败后回退到P2P
     */
    private suspend fun attemptAutoConnection(context: Context) {
        Log.d(TAG, "Attempting auto connection (AP first, P2P fallback)")
        
        // 首先尝试AP模式
        val apSuccess = withTimeoutOrNull(AP_TIMEOUT_MS) {
            attemptApConnectionInternal(context)
        } ?: false
        
        if (apSuccess) {
            Log.d(TAG, "Auto connection: AP mode succeeded")
            saveSuccessfulMode(WifiConnectionMode.WIFI_AP_CLIENT)
            return
        }
        
        Log.d(TAG, "Auto connection: AP mode failed, trying P2P")
        listener.onModeSwitching(WifiConnectionMode.WIFI_AP_CLIENT, WifiConnectionMode.WIFI_P2P)
        
        // AP失败，尝试P2P模式
        val p2pSuccess = withTimeoutOrNull(P2P_TIMEOUT_MS) {
            attemptP2pConnectionInternal(context)
        } ?: false
        
        if (p2pSuccess) {
            Log.d(TAG, "Auto connection: P2P mode succeeded")
            currentMode = WifiConnectionMode.WIFI_P2P
            saveSuccessfulMode(WifiConnectionMode.WIFI_P2P)
        } else {
            Log.e(TAG, "Auto connection: Both modes failed")
            handleConnectionFailure(WifiConnectionMode.AUTO, -998)
        }
    }
    
    private suspend fun attemptApConnection(context: Context) {
        val success = withTimeoutOrNull(AP_TIMEOUT_MS) {
            attemptApConnectionInternal(context)
        } ?: false
        
        if (!success) {
            handleConnectionFailure(WifiConnectionMode.WIFI_AP_CLIENT, -997)
        }
    }
    
    private suspend fun attemptP2pConnection(context: Context) {
        val success = withTimeoutOrNull(P2P_TIMEOUT_MS) {
            attemptP2pConnectionInternal(context)
        } ?: false
        
        if (!success) {
            handleConnectionFailure(WifiConnectionMode.WIFI_P2P, -996)
        }
    }
    
    private suspend fun attemptApConnectionInternal(context: Context): Boolean {
        return suspendCancellableCoroutine { continuation ->
            listener.onModeConnecting(WifiConnectionMode.WIFI_AP_CLIENT)
            
            // 请求仪表端WiFi热点信息
            requestWifiApInfo { ssid, password ->
                if (ssid.isNotEmpty()) {
                    wifiClientManager.connectToWifi(ssid, password)
                } else {
                    continuation.resumeWith(Result.success(false))
                }
            }
        }
    }
    
    private suspend fun attemptP2pConnectionInternal(context: Context): Boolean {
        return suspendCancellableCoroutine { continuation ->
            listener.onModeConnecting(WifiConnectionMode.WIFI_P2P)
            
            // 请求仪表端P2P信息
            requestP2pInfo { address, port ->
                if (address.isNotEmpty()) {
                    p2pManager.start(address, port)
                } else {
                    continuation.resumeWith(Result.success(false))
                }
            }
        }
    }
    
    /**
     * 确定连接模式
     */
    private fun determineConnectionMode(): WifiConnectionMode {
        // 优先使用上次成功的模式
        val lastSuccessful = getLastSuccessfulMode()
        if (lastSuccessful != WifiConnectionMode.AUTO) {
            Log.d(TAG, "Using last successful mode: $lastSuccessful")
            return lastSuccessful
        }
        
        // 检查用户偏好设置
        val userPreference = getUserModePreference()
        if (userPreference != WifiConnectionMode.AUTO) {
            Log.d(TAG, "Using user preference mode: $userPreference")
            return userPreference
        }
        
        // 默认自动模式
        Log.d(TAG, "Using default auto mode")
        return WifiConnectionMode.AUTO
    }
    
    private fun handleConnectionFailure(mode: WifiConnectionMode, reason: Int) {
        isConnecting.set(false)
        Log.e(TAG, "Connection failed for mode $mode with reason $reason")
        listener.onModeConnectionFailed(mode, reason)
    }
    
    // WiFi AP客户端监听器
    private val wifiClientListener = object : WiFiClientManagerListener {
        override fun onWifiConnecting(ssid: String) {
            Log.d(TAG, "AP mode: WiFi connecting to $ssid")
        }
        
        override fun onWifiConnected(ssid: String, ipAddress: String) {
            Log.d(TAG, "AP mode: WiFi connected to $ssid, IP: $ipAddress")
            val connectionInfo = ConnectionInfo(
                mode = WifiConnectionMode.WIFI_AP_CLIENT,
                ssid = ssid,
                ipAddress = ipAddress
            )
            isConnecting.set(false)
            listener.onModeConnected(WifiConnectionMode.WIFI_AP_CLIENT, connectionInfo)
        }
        
        override fun onWifiDisconnected() {
            Log.d(TAG, "AP mode: WiFi disconnected")
            listener.onModeDisconnected(WifiConnectionMode.WIFI_AP_CLIENT)
        }
        
        override fun onWifiConnectionFailed(reason: Int) {
            Log.e(TAG, "AP mode: WiFi connection failed: $reason")
            handleConnectionFailure(WifiConnectionMode.WIFI_AP_CLIENT, reason)
        }
        
        override fun onNetworkScanComplete(networks: List<android.net.wifi.ScanResult>) {
            Log.d(TAG, "AP mode: Network scan completed, found ${networks.size} networks")
        }
        
        override fun requestWifiInfo() {
            Log.d(TAG, "AP mode: Requesting WiFi info")
            // 通过BLE请求仪表端热点信息
            requestWifiApInfo { _, _ -> }
        }
        
        override fun onWifiState(opened: Boolean) {
            Log.d(TAG, "AP mode: WiFi state changed: $opened")
        }
    }
    
    // WiFi P2P监听器
    private val p2pListener = object : SoftIP2pListener {
        override fun onWifiConnectSuccess() {
            Log.d(TAG, "P2P mode: Connection success")
            val connectionInfo = ConnectionInfo(
                mode = WifiConnectionMode.WIFI_P2P,
                ssid = "P2P_Direct",
                ipAddress = "************" // P2P默认IP
            )
            isConnecting.set(false)
            listener.onModeConnected(WifiConnectionMode.WIFI_P2P, connectionInfo)
        }
        
        override fun onWifiDisconnect() {
            Log.d(TAG, "P2P mode: Disconnected")
            listener.onModeDisconnected(WifiConnectionMode.WIFI_P2P)
        }
        
        override fun onP2pConnectSuccess() {
            Log.d(TAG, "P2P mode: P2P connect success")
        }
        
        override fun connectExist() {
            Log.d(TAG, "P2P mode: Connection already exists")
        }
        
        override fun onCancelConnect() {
            Log.d(TAG, "P2P mode: Connection cancelled")
        }
        
        override fun requestWifiInfo() {
            Log.d(TAG, "P2P mode: Requesting WiFi info")
            // 通过BLE请求仪表端P2P信息
            requestP2pInfo { _, _ -> }
        }
        
        override fun onWifiState(opened: Boolean) {
            Log.d(TAG, "P2P mode: WiFi state changed: $opened")
        }
    }
    
    /**
     * 保存成功的连接模式
     */
    private fun saveSuccessfulMode(mode: WifiConnectionMode) {
        preferences.edit()
            .putString(PREF_LAST_SUCCESSFUL_MODE, mode.name)
            .apply()
        Log.d(TAG, "Saved successful mode: $mode")
    }
    
    /**
     * 获取上次成功的连接模式
     */
    private fun getLastSuccessfulMode(): WifiConnectionMode {
        val modeName = preferences.getString(PREF_LAST_SUCCESSFUL_MODE, WifiConnectionMode.AUTO.name)
        return try {
            WifiConnectionMode.valueOf(modeName!!)
        } catch (e: Exception) {
            WifiConnectionMode.AUTO
        }
    }
    
    /**
     * 获取用户模式偏好
     */
    private fun getUserModePreference(): WifiConnectionMode {
        val modeName = preferences.getString(PREF_MODE_PREFERENCE, WifiConnectionMode.AUTO.name)
        return try {
            WifiConnectionMode.valueOf(modeName!!)
        } catch (e: Exception) {
            WifiConnectionMode.AUTO
        }
    }
    
    /**
     * 设置用户模式偏好
     */
    fun setUserModePreference(mode: WifiConnectionMode) {
        preferences.edit()
            .putString(PREF_MODE_PREFERENCE, mode.name)
            .apply()
        Log.d(TAG, "Set user mode preference: $mode")
    }
    
    private fun requestWifiApInfo(callback: (ssid: String, password: String) -> Unit) {
        // TODO: 通过BLE请求仪表端热点信息
        // 这里需要调用MessageManager发送AP模式请求
        callback("TestInstrumentAP", "testpassword")
    }
    
    private fun requestP2pInfo(callback: (address: String, port: Int) -> Unit) {
        // TODO: 通过BLE请求仪表端P2P信息
        // 这里需要调用MessageManager发送P2P模式请求
        callback("AA:BB:CC:DD:EE:FF", 30512)
    }
    
    /**
     * 获取当前连接模式
     */
    fun getCurrentMode(): WifiConnectionMode = currentMode
    
    /**
     * 强制切换到指定模式
     */
    fun switchToMode(mode: WifiConnectionMode, context: Context) {
        Log.d(TAG, "Force switching to mode: $mode")
        disconnect()
        
        // 短暂延迟后开始新连接
        CoroutineScope(Dispatchers.Main).launch {
            delay(1000)
            startConnection(context, mode)
        }
    }
    
    /**
     * 销毁资源
     */
    fun destroy() {
        disconnect()
        connectionJob?.cancel()
        wifiClientManager.destroy()
        // P2P管理器由DisplayNaviManager管理生命周期
    }
}

/**
 * 连接模式管理器监听接口
 */
interface ConnectionModeManagerListener {
    /**
     * 模式已选择
     */
    fun onModeSelected(mode: WifiConnectionMode)
    
    /**
     * 模式连接中
     */
    fun onModeConnecting(mode: WifiConnectionMode)
    
    /**
     * 模式连接成功
     */
    fun onModeConnected(mode: WifiConnectionMode, connectionInfo: ConnectionInfo)
    
    /**
     * 模式断开连接
     */
    fun onModeDisconnected(mode: WifiConnectionMode)
    
    /**
     * 模式连接失败
     */
    fun onModeConnectionFailed(mode: WifiConnectionMode, reason: Int)
    
    /**
     * 模式切换中
     */
    fun onModeSwitching(fromMode: WifiConnectionMode, toMode: WifiConnectionMode)
}

/**
 * WiFi连接模式枚举
 */
enum class WifiConnectionMode {
    WIFI_AP_CLIENT,  // AP客户端模式
    WIFI_P2P,        // P2P直连模式  
    AUTO             // 自动选择模式
}
```

详细实现将在后续开发阶段完成。 