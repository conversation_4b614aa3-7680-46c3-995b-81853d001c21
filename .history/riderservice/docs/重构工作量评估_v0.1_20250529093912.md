# RiderService SDK 重构工作量评估

## 1. 评估范围

本次评估主要包含以下几个方面：

1.  **核心投屏逻辑重构**:
    *   实现新的 `ProjectionManager`。
    *   调整 `RiderService` API。
    *   修改 `ConnectionManager` 以发送 `NaviMode` 指令并管理连接。
    *   确保底层模块 (`AutolinkControl`, `MediaProjectService`) 与新架构的集成。
2.  **Wi-Fi 连接模式改造 (P2P -> AP)**:
    *   修改网络发现、连接建立、IP 处理、Socket 通信等逻辑。
3.  **测试**:
    *   自测。
4.  **文档**:
    *   更新面向开发者的 API 文档和集成指南。

---

## 2. 工作量估算 (人天)

| 任务模块| 子任务 | 估算 (人天) |
| ----- | ----- | ------ |
| **1. 核心投屏逻辑重构**       |                                                                        | **8 - 12**    |
|                               | 1.1 创建 `ProjectionManager.kt` 并实现核心状态机和资源管理               | 3 - 4         |
|                               | 1.2 重构 `RiderService.kt` API (`requestNaviMode`, `stopCurrentProjection`) | 1 - 2         |
|                               | 1.3 修改 `ConnectionManager.kt` (发送 `NaviMode`, Wi-Fi 状态通知)        | 1.5 - 2.5     |
|                               | 1.4 调整 `AutolinkControl.kt` (或其调用者) 与 `ProjectionManager` 的交互 | 1.5 - 2       |
|                               | 1.5 集成 `MediaProjectService.java`                                      | 1 - 1.5       |
| **2. Wi-Fi 连接模式改造 (P2P -> AP)** |                                                                | **5 - 8**     |
|                               | 2.1 研究并实现 Android 端作为 Wi-Fi AP 的逻辑 (如果SDK需要创建AP)        | 2 - 3         |
|                               | 2.2 修改设备发现、IP 地址获取、Socket 连接逻辑                           | 2 - 3         |
|                               | 2.3 处理 AP 模式下的连接稳定性、错误和重连机制                           | 1 - 2         |
| **3. 测试**                   |                                                                        | **3 - 5**     |
|                               | 3.1 自测 (模拟仪表或真实仪表联调，覆盖所有 NaviMode 和连接场景)           | 3 - 5         |
| **4. 文档**                   |                                                                        | **1 - 2**     |
|                               | 4.1 更新 API 参考文档                                                    | 0.5 - 1       |
|                               | 4.2 更新集成指南和示例代码                                                 | 0.5 - 1       |
| ---                           | ---                                                                    | ---           |
| **总计估算**                  |                                                                        | **17 - 27** |

---

## 4. 潜在风险与依赖

*   **仪表端配合**: Wi-Fi AP 模式的改造和 `NaviMode` 的处理强依赖仪表端的实现和配合。如果仪表端行为与预期不符，或存在 Bug，会显著影响调试和集成时间。
*   **Android Wi-Fi AP 模式的复杂性**: 根据 SDK 是作为 AP 的创建者还是连接者，以及 Android 版本和设备差异，Wi-Fi AP 相关编程可能存在较多陷阱。
*   **客户需求变更**: 客户需求可能在开发过程中发生变化，新增或修改功能要求可能导致重新设计和开发，影响原定工作量和时间计划。
*   **项目时间计划**: 客户的上线时间要求可能与开发实际进度存在差异，紧急的时间节点可能需要并行开发或压缩测试时间，增加项目风险。

---

## 5. 总结

基于当前的理解，完成上述所有重构、功能改造、测试和文档工作的总工作量初步估计在 **17 到 27 人天** 之间。这对应大约 **3.5 到 5.5 周** 的开发时间。

**建议**:
*   在开始编码前，务必与仪表端开发团队确认 `NaviMode` 的所有细节以及 Wi-Fi AP 模式下的通信协议。
*   优先进行 Wi-Fi AP 模式的技术验证，以降低该部分的不确定性。
*   分阶段实施和测试，例如先完成核心投屏逻辑重构，再进行 Wi-Fi AP 改造。
*   与客户保持密切沟通，及时确认需求细节并管理时间预期。

该评估会随着对需求和现有代码更深入的理解而更新。
 