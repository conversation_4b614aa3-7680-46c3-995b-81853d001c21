# RiderService SDK 设计文档

> 版本：0.2
> 作者：AI 自动生成（请根据实际情况补充、修订）

---

## 1. 项目概述
RiderService SDK 是一套用于 **Android 手机与两轮车智能仪表**（以下简称 *仪表盘*）进行双向通信、导航投屏和数据服务 的完整解决方案。其核心目标包括：

1. 通过 **BLE（Bluetooth Low Energy）** 完成快速配对、协议握手与控制信令传输。
2. 通过 **Wi-Fi P2P（或 AP 模式）** 建立高速通道，用于音视频/屏幕镜像数据传输。
3. 提供统一、简洁的 **API 封装**（`com.link.riderservice.api.RiderService`），简化 App 集成成本。
4. 内置 **激活&合规校验**、**通知同步** 等业务插件，开箱即用。
5. 支持 **镜像投屏** 和 **Presentation 投屏** 两种灵活的投屏方式。

项目以 `app`（Demo）、`riderservice`（SDK 主体） 两个 Gradle Module 组织，其中 `riderservice` 同时包含 Kotlin/Java 代码与 C/C++ Native 实现。

```
├── app                 # 示例 App（可作为集成参考）
└── riderservice        # SDK 主体
    ├── src/main/java   # 业务逻辑（Kotlin/Java）
    ├── src/main/cpp    # RTP/视频流 & Protobuf C++ 实现
    └── docs            # 开发/设计文档
```

---

## 2. 总体架构

```
┌─────────────────────┐
│       应用层        │  ←  app 模块 / 第三方集成 App
└─────────┬───────────┘
          │RiderService API 调用 (连接, 投屏, 数据发送)
┌─────────▼───────────┐
│   SDK 核心 (api)    │  com.link.riderservice.api.RiderService
├─────────┬───────────┤
│ ConnectionManager   │  连接状态管理 (BLE & Wi-Fi)
│ MessageManager      │  Protobuf 收发队列
│ DisplayManager      │  投屏逻辑管理 (Mirror & Presentation)
└─────────┬───────────┘
          │
  ┌───────▼────────────┬──────────────┐
  │     连接层 (connection)          │
  │ ┌────────────┐  ┌──────────────┐ │
  │ │ BleManager │  │ WifiManager  │ │  (Wi-Fi Direct/P2P)
  │ └────────────┘  └──────────────┘ │
  └────────┬─────────────────────────┘
           │BLE 握手 / Wi-Fi 连接
┌──────────▼──────────┐   TCP / RTP   ┌─────────────┐
│   智能仪表硬件      │  ←→ 投屏视频 → │  RTP Receiver│ (Native C++)
└─────────────────────┘               └─────────────┘
```

1. **RiderService**: 暴露给 App 的唯一入口（单例模式），提供简洁的 API 用于连接管理、投屏控制和消息通信。
2. **ConnectionManager**: 负责 BLE 扫描、连接、状态维护，以及 Wi-Fi P2P/AP 模式的连接建立和维护。
3. **DisplayManager**: (概念上，具体实现在 `ConnectionManager` 或 `RiderService` 内) 负责处理两种投屏模式的逻辑。
    *   **Mirror Mode**: 使用 `MediaProjection` 捕获屏幕，H264 编码后通过 Socket 发送给仪表。
    *   **Presentation Mode**: 管理外部 `Display` 的生命周期，并通知应用层。
4. **MessageManager**: 维护 Protobuf 消息的发送/接收队列。
5. Native 端 `receiver/rtp` 实现 RTP 解码、渲染（针对 Mirror 模式）。

---

## 3. 关键模块详解

### 3.1 API 层（`com.link.riderservice.api.RiderService`）

* **RiderService.kt**:
    *   初始化：`RiderService.instance.init(application)`
    *   注册/反注册回调：`addCallback(callback)`, `removeCallback(callback)`
    *   连接管理：
        *   `startBleScan()` / `stopBleScan()`
        *   `connectBle(device)`
        *   `disconnect()`
    *   投屏控制:
        *   **Mirror Mode**:
            *   `startMirrorProjection()`: 应用调用以发起镜像投屏。
            *   `setMediaProjection(mediaProjection)`: 应用在收到 `onMirrorModeRequiresMediaProjection` 回调后，将获取到的 `MediaProjection` 对象设置给 SDK。
            *   `stopMirrorProjection()`: 应用调用以停止镜像投屏。
        *   **Presentation Mode**:
            *   `startPresentationMode()`: 应用调用以请求 Presentation 副屏。
            *   `stopPresentationMode()`: 应用调用以释放 Presentation 副屏。
    *   消息发送：`sendMessageToRiderService(RiderMessage)` (用于发送如导航指令、通知等业务消息)
    *   回调接口：`RiderServiceCallback` (详见下文)
* **dto/**: 定义导航、通知等业务数据模型，确保 API 稳定性。天气相关 DTO 已移除。
* **RiderServiceCallback.kt**: 定义了 SDK 向应用层通知各类事件的回调接口，包括：
    *   BLE 扫描结果与状态：`onScanResult`, `onScanning`, `onScanFinish`, `onBleConnectionStateChanged`
    *   Wi-Fi 连接状态：`onWifiConnectionStateChanged`
    *   权限请求：`onRequestOpenBluetooth`, `onNeedBluetoothScanPermission`, `onNeedLocationPermission`
    *   Mirror 投屏回调：`onMirrorModeRequiresMediaProjection`, `onMirrorModeStarted`, `onMirrorModeStopped`
    *   Presentation 投屏回调：`onPresentationModeDisplayAvailable`, `onPresentationModeDisplayReleased`
    *   其他：`onDialogShow`, `onRiderServiceConfigChanged`, `onWifiStateChanged`, `onNaviModeChanged`, `onNaviVersionResponse`, `onMapThemeChanged`

### 3.2 连接管理（`ConnectionManager.kt`）
*   内部维护 BLE (`RiderBleManager`) 和 Wi-Fi (`DisplayNaviManager`的部分逻辑，或新的`WifiManager`) 的状态机。
*   处理 BLE 设备的扫描、连接、断开、重连逻辑。
*   负责请求并建立与仪表的 Wi-Fi P2P 或 AP 连接，为投屏和高速数据传输建立通道。
*   协调 BLE 和 Wi-Fi 的连接状态，并通过 `RiderServiceCallback` 通知应用层。
*   处理投屏模式的切换请求，管理 `MediaProjection` 的生命周期（Mirror 模式）和外部 `Display` 的获取与释放（Presentation 模式）。

### 3.3 消息处理（`MessageManager.kt`）
*   负责将应用层发送的 `RiderMessage` 对象序列化为 Protobuf 字节流，并通过 BLE 或 Wi-Fi 发送给仪表。
*   负责接收来自仪表的 Protobuf 字节流，反序列化后分发给 `RiderService` 内部的相应处理逻辑或通过回调通知应用层。

### 3.4 授权与合规（`authorize` 包）
通过云端接口校验 `productKey、uuid、licenseSign` 等信息，确保设备和应用的合法性。验证失败会导致连接中断。该模块功能保持不变。

### 3.5 Native 实现（`src/main/cpp`）
*   **protobuf/**: Protobuf C++ Runtime。
*   **receiver/**: RTP Client，主要用于接收和处理 Mirror 模式下的 H264 视频流。

---

## 4. 通信协议
协议主要基于 Protobuf 定义，具体文件为 `riderservice/src/main/proto/RiderProtocol.proto`（天气相关消息定义应视为已移除）。

### 消息类型示例 (部分)
| Id (常量名)                  | 方向        | 描述                                   |
|------------------------------|-------------|----------------------------------------|
| `MSG_WIFI_INFO_REQUEST`      | App → 仪表  | 请求仪表 Wi-Fi AP 信息                   |
| `MSG_WIFI_INFO_NOTIFICATION` | 仪表 → App  | 仪表返回 Wi-Fi SSID/Port               |
| `MSG_NAVI_INFO`              | App → 仪表  | 实时导航信息 (如转向、路名、距离等)      |
| `MSG_ANDROID_NOTIFICATION`   | App → 仪表  | 同步手机应用通知 (如来电、短信、App消息) |
| `MSG_CONFIG_NOTIFICATION`    | 仪表 → App  | 仪表功能配置信息                       |
| `MSG_NAVI_MODE_CHANGE`       | 仪表 → App  | 仪表请求切换导航显示模式 (如简易/地图)   |

> 详细字段请参考 `RiderProtocol.proto` 文件。天气相关的消息（如 `MSG_WEATHER_INFO_REQUEST`, `MSG_WEATHER_INFO_NOTIFICATION`）已废弃。

---

## 5. 典型流程

### 5.1 连接建立流程
1.  **App 调用** `RiderService.instance.startBleScan()`。
2.  SDK 通过 `onScanResult` 回调返回发现的设备列表。
3.  **App 调用** `RiderService.instance.connectBle(selectedDevice)`。
4.  SDK 内部 `ConnectionManager` 处理 BLE 连接。
    *   `onBleConnectionStateChanged(CONNECTING)`
    *   连接成功后进行协议握手、版本校验、合规激活校验。
    *   `onBleConnectionStateChanged(CONNECTED)`
5.  BLE 连接成功后，SDK 自动请求仪表 Wi-Fi 信息 (`MSG_WIFI_INFO_REQUEST`)。
6.  仪表返回 Wi-Fi 信息 (`MSG_WIFI_INFO_NOTIFICATION`)。
7.  SDK 尝试连接 Wi-Fi。
    *   `onWifiConnectionStateChanged(CONNECTING)`
    *   `onWifiConnectionStateChanged(CONNECTED)` / `FAILED`
8.  连接全部建立后，通过 `onRiderServiceConfigChanged` 通知仪表配置。

### 5.2 镜像投屏 (Mirror Mode)
1.  **App 调用** `RiderService.instance.startMirrorProjection()`。
2.  SDK (内部 `ConnectionManager` / `DisplayManager`) 判断条件，若需 `MediaProjection` 权限，则通过 `RiderServiceCallback.onMirrorModeRequiresMediaProjection()` 通知应用。
3.  **App 层**:
    *   响应回调，向用户请求 `MediaProjection` 权限。
    *   获取到 `MediaProjection` 对象后，**App 调用** `RiderService.instance.setMediaProjection(mediaProjection)`。
4.  SDK 接收到 `MediaProjection` 对象后，开始捕获屏幕、编码并通过 Wi-Fi 推流到仪表。
5.  SDK 通过 `RiderServiceCallback.onMirrorModeStarted()` 通知应用投屏已开始。
6.  用户或 **App 调用** `RiderService.instance.stopMirrorProjection()`。
7.  SDK 停止屏幕捕获和推流。
8.  SDK 通过 `RiderServiceCallback.onMirrorModeStopped()` 通知应用投屏已停止。

### 5.3 Presentation 投屏 (Presentation Mode)
1.  **App 调用** `RiderService.instance.startPresentationMode()`。
2.  SDK (内部 `ConnectionManager` / `DisplayManager`) 与仪表协商，获取副屏 `Display` 对象。
3.  SDK 通过 `RiderServiceCallback.onPresentationModeDisplayAvailable(display)` 将可用的 `Display` 对象提供给应用。
4.  **App 层**:
    *   使用获取到的 `display` 对象创建 `Presentation` 实例，并加载自定义布局和内容。
    *   `presentation.show()`。
5.  用户或 **App 调用** `RiderService.instance.stopPresentationMode()`。
6.  SDK 通知仪表释放副屏资源。
7.  SDK 通过 `RiderServiceCallback.onPresentationModeDisplayReleased(display)` 通知应用 `Display` 已释放。
8.  **App 层**: 确保自定义的 `Presentation` 被正确 `dismiss()` 和销毁。

### 5.4 发送业务消息 (如导航指令)
```kotlin
// 示例：发送下一路口转向信息
val naviInfo = NaviInfo(
    curStep = 1, // 第一个导航动作
    curStepRetainDistance = 500, // 距离下一个动作点500米
    nextRoadName = "科技园路",
    iconType = 2, // 右转图标
    // ... 其他导航参数
)
RiderService.instance.sendMessageToRiderService(naviInfo)
```

---

## 6. 集成与使用示例

### 6.1 Gradle 依赖
```gradle
// settings.gradle.kts (如果 riderservice 是本地模块)
// include(":app", ":riderservice")

// app/build.gradle.kts
dependencies {
    implementation(project(":riderservice"))
    // 或者 (如果已发布到 Maven)
    // implementation("com.link:riderservice:x.y.z")
}
```

### 6.2 初始化 (Application 类)
```kotlin
class MainApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        RiderService.instance.init(this)
    }
}
```

### 6.3 注册回调与权限处理 (Activity/Fragment)
```kotlin
class MyActivity : AppCompatActivity() {
    private val riderServiceCallback = object : RiderServiceCallback {
        override fun onScanResult(devices: List<BleDevice>) {
            // 更新设备列表 UI
        }

        override fun onBleConnectionStateChanged(status: BleConnectionStatus, device: BluetoothDevice?) {
            // 更新连接状态 UI
            if (status == BleConnectionStatus.CONNECTED) {
                // BLE 连接成功，可以进行后续操作或等待 Wi-Fi 连接
            }
        }

        override fun onWifiConnectionStateChanged(status: WifiConnectionStatus) {
            // 更新 Wi-Fi 连接状态 UI
        }

        override fun onMirrorModeRequiresMediaProjection() {
            // 请求 MediaProjection 权限
            val mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            startActivityForResult(mediaProjectionManager.createScreenCaptureIntent(), REQUEST_MEDIA_PROJECTION)
        }

        override fun onMirrorModeStarted() {
            // Mirror 投屏已开始
            Toast.makeText(this@MyActivity, "镜像投屏已开始", Toast.LENGTH_SHORT).show()
        }

        override fun onMirrorModeStopped() {
            // Mirror 投屏已停止
            Toast.makeText(this@MyActivity, "镜像投屏已停止", Toast.LENGTH_SHORT).show()
        }

        override fun onPresentationModeDisplayAvailable(display: Display) {
            // Presentation Display 可用，创建自定义 Presentation
            // myCustomPresentation = MyCustomPresentation(this@MyActivity, display)
            // myCustomPresentation.show()
            Toast.makeText(this@MyActivity, "Presentation Display 可用", Toast.LENGTH_SHORT).show()
        }

        override fun onPresentationModeDisplayReleased(display: Display) {
            // Presentation Display 已释放
            // myCustomPresentation?.dismiss()
            // myCustomPresentation = null
            Toast.makeText(this@MyActivity, "Presentation Display 已释放", Toast.LENGTH_SHORT).show()
        }

        override fun onDialogShow(title: String, message: String) {
            AlertDialog.Builder(this@MyActivity)
                .setTitle(title)
                .setMessage(message)
                .setPositiveButton("确定", null)
                .show()
        }
        // ... 实现其他回调方法
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        RiderService.instance.addCallback(riderServiceCallback)
        // ... 请求蓝牙、定位等必要权限 ...
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_MEDIA_PROJECTION) {
            if (resultCode == Activity.RESULT_OK && data != null) {
                val mediaProjection = (getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager).getMediaProjection(resultCode, data)
                RiderService.instance.setMediaProjection(mediaProjection)
            } else {
                Toast.makeText(this, "未授予屏幕录制权限", Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun onDestroy() {
        RiderService.instance.removeCallback(riderServiceCallback)
        // RiderService.instance.destroy() // 视应用生命周期管理决定是否在此处销毁
        super.onDestroy()
    }

    companion object {
        private const val REQUEST_MEDIA_PROJECTION = 1001
    }
}
```

### 6.4 控制投屏
```kotlin
// 启动 Mirror 投屏
fun startMirror() {
    RiderService.instance.startMirrorProjection()
}

// 停止 Mirror 投屏
fun stopMirror() {
    RiderService.instance.stopMirrorProjection()
}

// 启动 Presentation 投屏
fun startPresentation() {
    RiderService.instance.startPresentationMode()
}

// 停止 Presentation 投屏
fun stopPresentation() {
    RiderService.instance.stopPresentationMode()
}
```

---

## 7. 错误处理与重连
* **BLE 连接失败/断开**: SDK 内部会尝试自动重连（次数和间隔待定）。若持续失败，会通过 `onBleConnectionStateChanged(DISCONNECTED/FAILED)` 通知应用。应用可选择提示用户或重新发起扫描。
* **Wi-Fi 连接失败/断开**: SDK 会尝试重新请求 Wi-Fi 信息并连接。若持续失败，通过 `onWifiConnectionStateChanged(DISCONNECTED)` 通知。
* **权限缺失**: 通过特定回调（如 `onNeedBluetoothScanPermission`）通知应用层处理。
* **协议错误/超时**: 通过 `onDialogShow` 提示用户。

---

## 8. 构建与运行
```bash
# 克隆仓库
$ git clone https://example.com/RiderService_SDK.git # 替换为实际仓库地址
$ cd RiderService_SDK

# 使用 Android Studio 打开根目录；或命令行构建
$ ./gradlew assembleDebug
```
Demo APK 生成路径：`app/build/outputs/apk/debug/app-debug.apk`。

---

## 9. 后续规划
1.  **协议健壮性**: 增强协议容错和版本兼容性。
2.  **投屏性能优化**: 持续优化 H264 编码参数、RTP 传输效率，降低延迟和功耗。
3.  **安全性增强**: 研究端到端加密方案，保护通信数据安全。
4.  **更完善的错误码和日志**: 提供更细致的错误码和诊断日志，方便问题排查。
5.  **单元测试与集成测试**: 补充核心模块的单元测试和端到端集成测试。

---

> 文档已根据重构需求更新。如有疑问，请查阅源码或联系维护者。 