# RiderService SDK 设计文档

![版本](https://img.shields.io/badge/版本-1.0-blue)
![状态](https://img.shields.io/badge/状态-最新-green)

---

## 📋 目录

- [项目概述](#-项目概述)
- [系统架构](#-系统架构)
- [核心模块](#-核心模块)
- [连接机制](#-连接机制)
- [投屏系统](#-投屏系统)
- [通信协议](#-通信协议)
- [API 设计](#-api-设计)
- [典型流程](#-典型流程)
- [集成指南](#-集成指南)
- [错误处理](#-错误处理)
- [技术规范](#-技术规范)

---

## 🎯 项目概述

### 核心目标

RiderService SDK 是一套面向 **Android 手机与两轮车智能仪表** 的完整通信解决方案，提供双向通信、导航投屏和数据服务功能。

### ✨ 核心特性

- 🔗 **双重连接**: BLE + Wi-Fi AP 模式的稳定连接方案
- 📱 **双投屏模式**: 支持镜像投屏和 Presentation 投屏
- 🎯 **应用发起**: 应用主导的投屏模式切换机制
- 🔐 **安全认证**: 内置激活校验和合规检查
- 📡 **消息同步**: 通知、导航等业务数据实时传输
- 🛠️ **简化集成**: 统一、简洁的 API 设计

### 📦 项目结构

```
RiderService_SDK/
├── app/                    # 示例应用
└── riderservice/           # SDK 核心
    ├── src/main/java/      # 业务逻辑（Kotlin/Java）
    ├── src/main/cpp/       # Native 实现（C++）
    ├── src/main/proto/     # Protocol Buffers 定义
    └── docs/               # 文档
```

---

## 🏗️ 系统架构

### 整体架构图

```mermaid
graph TB
    App[📱 应用层] --> API[🎯 RiderService API]

API --> CM[🔗 ConnectionManager]
API --> PM[🎬 ProjectionManager]
API --> MM[💬 MessageManager]

CM --> BLE[🔵 BLE Manager]
CM --> WiFi[📶 WiFi Manager]

PM --> AC[⚙️ AutolinkControl]
PM --> MPS[🎥 MediaProjectService]

BLE --> Device[🏍️ 智能仪表]
WiFi --> Device
AC --> Device
MPS --> AC

subgraph "连接层"
BLE
WiFi
end

subgraph "投屏层"
PM
AC
MPS
end

subgraph "硬件层"
Device
end
```

### 🔄 交互流程

```mermaid
sequenceDiagram
    participant App as 📱 应用
    participant SDK as 🎯 SDK
    participant BLE as 🔵 BLE
    participant WiFi as 📶 WiFi
    participant Device as 🏍️ 仪表
    App ->> SDK: 请求连接
    SDK ->> BLE: 建立BLE连接
    BLE ->> Device: 连接成功
    Device ->> BLE: 发送WiFi凭证
    SDK ->> WiFi: 连接仪表AP
    WiFi ->> Device: 建立数据通道
    SDK ->> App: 连接完成回调
```

---

## 🧩 核心模块

### 🎯 RiderService (API 门面)

> **单一入口，统一管理**

**主要职责：**

- 对外暴露统一、简洁的API接口
- 管理回调注册与事件分发
- 协调各子模块间的交互

**核心API：**

```kotlin
object RiderService {
    // 基础功能
    fun init(application: Application)
    fun addCallback(callback: RiderServiceCallback)

    // 连接管理
    fun startBleScan()
    fun connectBle(device: BleDevice)
    fun disconnect()

    // 投屏控制
    fun requestNaviMode(targetNaviMode: NaviModeType)
    fun setMediaProjection(mediaProjection: MediaProjection)
    fun stopCurrentProjection(naviModeForStop: NaviModeType)

    // 消息通信
    fun sendMessageToRiderService(message: RiderMessage)
}
```

### 🔗 ConnectionManager (连接管理器)

> **稳定连接，智能重连**

**主要职责：**

- BLE 设备扫描、连接、状态管理
- Wi-Fi AP 模式连接建立与维护
- 网络状态监控与异常处理
- 协议握手与版本校验

**连接状态机：**

```mermaid
stateDiagram-v2
    [*] --> Idle
    Idle --> BleScanning: startScan()
    BleScanning --> BleConnecting: connectBle()
    BleConnecting --> BleConnected: 连接成功
    BleConnected --> WiFiConnecting: 获取AP凭证
    WiFiConnecting --> FullyConnected: WiFi连接成功
    FullyConnected --> Disconnected: disconnect()
    Disconnected --> [*]
```

### 🎬 ProjectionManager (投屏管理器)

> **应用发起，仪表执行，SDK协调**

**设计理念：**

- **应用发起**: 通过 `requestNaviMode()` 请求投屏模式
- **仪表执行**: 仪表负责关闭旧模式，开启新模式
- **SDK协调**: 响应仪表状态变化，管理资源和回调

**支持模式：**

| 模式                        | 描述      | 应用职责                | SDK职责       |
|---------------------------|---------|---------------------|-------------|
| 🪞 **Mirror Mode**        | 屏幕镜像投屏  | 提供MediaProjection权限 | 屏幕捕获、编码、推流  |
| 🖥️ **Presentation Mode** | 外部显示屏投屏 | 自定义Presentation内容   | 提供Display对象 |

### 💬 MessageManager (消息管理器)

> **高效通信，可靠传输**

**主要职责：**

- Protobuf 消息序列化/反序列化
- 消息队列管理与优先级处理
- 发送状态跟踪与重试机制

---

## 🌐 连接机制

### 🔵 BLE 连接

**特点：**

- ⚡ 快速配对与协议握手
- 🔐 安全的控制信令传输
- 🔋 低功耗维持连接

**用途：**

- 设备发现与配对
- Wi-Fi 凭证交换
- 控制指令传输（如NaviMode）
- 心跳与状态监控

### 📶 Wi-Fi AP 模式

**架构变更：** `P2P模式` → `AP模式`

**连接流程：**

```mermaid
sequenceDiagram
    participant SDK as 📱 SDK
    participant Device as 🏍️ 仪表
    Note over SDK, Device: 1. BLE连接已建立
    Device ->> SDK: 发送AP凭证(SSID+密码)
    SDK ->> SDK: 连接仪表AP
    SDK ->> SDK: 获取自身IP地址
    SDK ->> SDK: 启动TCP服务器(端口30512)
    SDK ->> Device: 通过BLE发送SDK的IP地址
    Device ->> SDK: 连接TCP服务器
    Note over SDK, Device: 2. 数据通道建立完成
```

**关键特性：**

- 🎯 **SDK作为TCP服务端**：在端口30512监听连接
- 📍 **动态IP通知**：通过BLE将SDK的IP地址发送给仪表
- 🔄 **仪表重试机制**：连接失败时自动重试
- 🚫 **无自动重连**：连接断开后不自动重连，由应用层决定

---

## 🎬 投屏系统

### 🪞 镜像投屏 (Mirror Mode)

**适用场景：** 完整屏幕内容投屏，适合地图导航、多媒体展示

**实现流程：**

```mermaid
sequenceDiagram
    participant App as 📱 应用
    participant SDK as 🎯 SDK
    participant Device as 🏍️ 仪表
    App ->> SDK: requestNaviMode(MIRROR_TYPE)
    SDK ->> Device: 发送NaviMode指令
    Device ->> Device: 准备镜像模式
    SDK ->> App: onMirrorModeRequiresMediaProjection()
    App ->> App: 用户授权MediaProjection
    App ->> SDK: setMediaProjection(mediaProjection)
    SDK ->> SDK: 开始屏幕捕获与编码
    SDK ->> Device: 推送H264视频流
    SDK ->> App: onMirrorModeStarted()
```

**技术特点：**

- 🎥 **H264硬件编码**：高效压缩，低延迟
- 📡 **RTP传输协议**：稳定的视频流传输
- ⚙️ **动态参数调整**：根据网络状况自适应

### 🖥️ Presentation 投屏 (Presentation Mode)

**适用场景：** 自定义内容显示，适合专门的仪表界面设计

**实现流程：**

```mermaid
sequenceDiagram
    participant App as 📱 应用
    participant SDK as 🎯 SDK
    participant Device as 🏍️ 仪表
    App ->> SDK: requestNaviMode(PRESENTATION_TYPE)
    SDK ->> Device: 发送NaviMode指令
    Device ->> Device: 准备Presentation模式
    Device ->> SDK: 提供Display对象
    SDK ->> App: onPresentationModeDisplayAvailable(display)
    App ->> App: 创建自定义Presentation
    App ->> App: presentation.show()
    Note over App, Device: 应用完全控制显示内容
```

**技术特点：**

- 🎨 **完全自定义**：应用可自由设计界面
- 🖼️ **原生Display**：使用Android标准Display API
- 🔧 **灵活控制**：支持动态内容更新

---

## 📡 通信协议

### Protocol Buffers 消息定义

**核心消息类型：**

| 消息ID                         | 方向     | 描述           | 用途         |
|------------------------------|--------|--------------|------------|
| `MSG_WIFI_INFO_REQUEST`      | SDK→仪表 | 请求Wi-Fi AP信息 | 获取连接凭证     |
| `MSG_WIFI_INFO_NOTIFICATION` | 仪表→SDK | 返回AP凭证       | 提供SSID/密码  |
| `MSG_NAVI_INFO`              | SDK→仪表 | 导航信息         | 实时路况、转向指引  |
| `MSG_ANDROID_NOTIFICATION`   | SDK→仪表 | 通知同步         | 来电、短信、应用消息 |
| `MSG_CONFIG_NOTIFICATION`    | 仪表→SDK | 配置信息         | 仪表功能配置     |
| `MSG_NAVI_MODE_CHANGE`       | 双向     | 模式切换         | 投屏模式控制     |

### 消息传输通道

```mermaid
graph LR
    subgraph "BLE通道"
        A[控制信令] --> B[NaviMode]
        A --> C[配置信息]
        A --> D[心跳检测]
    end

    subgraph "Wi-Fi通道"
        E[业务数据] --> F[导航信息]
        E --> G[通知消息]
        E --> H[视频流]
    end
```

---

## 🛠️ API 设计

### 核心接口

```kotlin
interface RiderServiceCallback {
    // 连接状态回调
    fun onScanResult(devices: List<BleDevice>)
    fun onBleConnectionStateChanged(status: BleConnectionStatus, device: BluetoothDevice?)
    fun onWifiConnectionStateChanged(status: WifiConnectionStatus)

    // 镜像投屏回调
    fun onMirrorModeRequiresMediaProjection()
    fun onMirrorModeStarted()
    fun onMirrorModeStopped()

    // Presentation投屏回调
    fun onPresentationModeDisplayAvailable(display: Display)
    fun onPresentationModeDisplayReleased(display: Display)

    // 其他回调
    fun onRiderServiceConfigChanged(config: RiderServiceConfig)
    fun onDialogShow(title: String, message: String)
}
```

### 业务数据模型

```kotlin
// 导航信息
data class NaviInfo(
    val curStep: Int,                    // 当前步骤
    val curStepRetainDistance: Int,      // 剩余距离
    val nextRoadName: String,            // 下一路段名称
    val iconType: Int,                   // 转向图标类型
    val totalDistance: Int,              // 总距离
    val totalTime: Int                   // 总时间
)

// 通知信息
data class AndroidNotification(
    val packageName: String,             // 应用包名
    val title: String,                   // 通知标题
    val content: String,                 // 通知内容
    val timestamp: Long                  // 时间戳
)
```

---

## 🔄 典型流程

### 设备连接流程

```kotlin
// 1. 初始化SDK
RiderService.instance.init(application)
RiderService.instance.addCallback(callback)

// 2. 扫描设备
RiderService.instance.startBleScan()

// 3. 连接设备（在onScanResult回调中选择）
RiderService.instance.connectBle(selectedDevice)

// 4. 等待连接完成（通过回调通知）
```

### 镜像投屏流程

```kotlin
// 1. 请求镜像模式
RiderService.instance.requestNaviMode(NaviModeType.MIRROR)

// 2. 处理权限请求（在onMirrorModeRequiresMediaProjection回调中）
override fun onMirrorModeRequiresMediaProjection() {
    val intent = mediaProjectionManager.createScreenCaptureIntent()
    startActivityForResult(intent, REQUEST_CODE_MEDIA_PROJECTION)
}

// 3. 设置MediaProjection（在onActivityResult中）
override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
    if (requestCode == REQUEST_CODE_MEDIA_PROJECTION && resultCode == RESULT_OK) {
        val mediaProjection = mediaProjectionManager.getMediaProjection(resultCode, data)
        RiderService.instance.setMediaProjection(mediaProjection)
    }
}

// 4. 投屏开始（通过onMirrorModeStarted回调通知）
```

### Presentation投屏流程

```kotlin
// 1. 请求Presentation模式
RiderService.instance.requestNaviMode(NaviModeType.PRESENTATION)

// 2. 处理Display可用（在回调中）
override fun onPresentationModeDisplayAvailable(display: Display) {
    // 创建自定义Presentation
    val presentation = MyCustomPresentation(this, display)
    presentation.show()
}

// 3. 处理Display释放（在回调中）
override fun onPresentationModeDisplayReleased(display: Display) {
    presentation?.dismiss()
    presentation = null
}
```

---

## 🔧 集成指南

### Gradle 配置

```kotlin
// settings.gradle.kts
include(":app", ":riderservice")

// app/build.gradle.kts
dependencies {
    implementation(project(":riderservice"))
    // 或远程依赖
    // implementation("com.link:riderservice:1.0.0")
}
```

### 权限配置

```xml
<!-- AndroidManifest.xml -->
<uses-permission android:name="android.permission.BLUETOOTH" /><uses-permission
android:name="android.permission.BLUETOOTH_ADMIN" /><uses-permission
android:name="android.permission.ACCESS_FINE_LOCATION" /><uses-permission
android:name="android.permission.ACCESS_WIFI_STATE" /><uses-permission
android:name="android.permission.CHANGE_WIFI_STATE" /><uses-permission
android:name="android.permission.INTERNET" />

    <!-- Android 12+ 蓝牙权限 -->
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" /><uses-permission
android:name="android.permission.BLUETOOTH_CONNECT" />
```

### 基本集成示例

```kotlin
class MainActivity : AppCompatActivity() {

    private val riderServiceCallback = object : RiderServiceCallback {
        override fun onBleConnectionStateChanged(
            status: BleConnectionStatus,
            device: BluetoothDevice?
        ) {
            when (status) {
                BleConnectionStatus.CONNECTED -> {
                    showToast("设备连接成功")
                }
                BleConnectionStatus.DISCONNECTED -> {
                    showToast("设备连接断开")
                }
            }
        }

        override fun onMirrorModeRequiresMediaProjection() {
            requestMediaProjectionPermission()
        }

        // ... 其他回调实现
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // 初始化SDK
        RiderService.instance.init(application)
        RiderService.instance.addCallback(riderServiceCallback)
    }

    override fun onDestroy() {
        RiderService.instance.removeCallback(riderServiceCallback)
        super.onDestroy()
    }
}
```

---

## ⚠️ 错误处理

### 连接错误

| 错误类型      | 原因          | 处理策略     |
|-----------|-------------|----------|
| BLE扫描失败   | 权限不足、蓝牙未开启  | 引导用户开启权限 |
| BLE连接超时   | 设备距离过远、信号干扰 | 提示用户靠近设备 |
| Wi-Fi连接失败 | AP信息错误、网络异常 | 重新获取AP凭证 |
| TCP连接失败   | 端口被占用、防火墙阻拦 | 检查网络配置   |

### 投屏错误

| 错误类型                | 原因         | 处理策略      |
|---------------------|------------|-----------|
| MediaProjection权限拒绝 | 用户拒绝授权     | 引导用户重新授权  |
| 编码器初始化失败            | 硬件不支持、资源不足 | 降低编码参数    |
| Display获取失败         | 仪表端异常、协议错误 | 重试或切换模式   |
| 网络传输中断              | 网络不稳定、连接断开 | 自动重连或提示用户 |

### 错误码定义

```kotlin
enum class RiderServiceError(val code: Int, val message: String) {
    // 连接错误 (1xxx)
    BLE_PERMISSION_DENIED(1001, "蓝牙权限被拒绝"),
    BLE_CONNECTION_TIMEOUT(1002, "BLE连接超时"),
    WIFI_CONNECTION_FAILED(1003, "Wi-Fi连接失败"),

    // 投屏错误 (2xxx)
    MEDIA_PROJECTION_DENIED(2001, "屏幕投射权限被拒绝"),
    ENCODER_INIT_FAILED(2002, "编码器初始化失败"),
    DISPLAY_NOT_AVAILABLE(2003, "外部显示器不可用"),

    // 协议错误 (3xxx)
    PROTOCOL_VERSION_MISMATCH(3001, "协议版本不匹配"),
    MESSAGE_PARSE_ERROR(3002, "消息解析错误"),
    DEVICE_NOT_SUPPORTED(3003, "设备不支持")
}
```

---

## 📋 技术规范

### 性能指标

| 指标        | 目标值       | 备注       |
|-----------|-----------|----------|
| BLE连接时间   | < 3秒      | 从扫描到连接完成 |
| Wi-Fi连接时间 | < 5秒      | AP模式连接建立 |
| 投屏启动时间    | < 2秒      | 从请求到开始推流 |
| 视频延迟      | < 200ms   | 端到端延迟    |
| 视频帧率      | 30fps     | 镜像模式     |
| 视频分辨率     | 1920x1080 | 最大支持分辨率  |

### 兼容性要求

| 项目        | 要求             | 备注                |
|-----------|----------------|-------------------|
| Android版本 | ≥ API 21 (5.0) | 核心功能支持            |
| 投屏功能      | ≥ API 24 (7.0) | MediaProjection需求 |
| 蓝牙版本      | BLE 4.0+       | 兼容性考虑             |
| Wi-Fi标准   | 802.11n+       | 保证传输速度            |

### 资源占用

| 资源    | 限制       | 说明          |
|-------|----------|-------------|
| 内存使用  | < 100MB  | 包含编码缓冲区     |
| CPU占用 | < 20%    | 镜像模式下       |
| 网络带宽  | < 10Mbps | 1080p@30fps |
| 电池消耗  | < 5%/小时  | 相对基准测试      |

---

## 🔮 后续规划

### 短期目标 (v1.1)

- [ ] 增强错误处理和重连机制
- [ ] 优化视频编码参数和传输协议
- [ ] 完善单元测试和集成测试
- [ ] 提供详细的API文档和示例

### 中期目标 (v1.5)

- [ ] 支持多设备同时连接
- [ ] 增加端到端加密
- [ ] 实现云端设备管理
- [ ] 支持音频传输

### 长期目标 (v2.0)

- [ ] 跨平台支持（iOS、Flutter）
- [ ] AI辅助的智能投屏
- [ ] 5G网络优化
- [ ] AR/VR集成支持

---

> 📝 **文档维护**：本文档将随SDK版本更新而持续维护，确保内容的准确性和时效性。
>
> 🤝 **技术支持**：如有疑问或建议，请通过Issue或邮件联系维护团队。

---

**文档版本**: 1.0  
**最后更新**: 2024年  
**维护团队**: RiderService SDK 开发团队 