# WiFi AP客户端连接重构代码示例

## 代码架构总览

### 核心类关系图

```mermaid
classDiagram
    class WiFiClientManager {
        -wifiManager: WifiManager
        -connectivityManager: ConnectivityManager
        -targetSsid: String
        -targetPassword: String
        -currentState: WifiClientState
        +connectToWifi(ssid, password)
        +disconnect()
        +destroy()
        -connectToWifiModern()
        -connectToWifiLegacy()
        -startReconnection()
    }
    
    class WiFiClientManagerListener {
        <<interface>>
        +onWifiConnecting(ssid)
        +onWifiConnected(ssid, ip)
        +onWifiDisconnected()
        +onWifiConnectionFailed(reason)
        +onNetworkScanComplete(networks)
        +requestWifiInfo()
        +onWifiState(opened)
    }
    
    class TcpServerConnection {
        -serverSocket: ServerSocket
        -listenPort: Int
        +startTcpServer()
        +notifyInstrumentWithIpAddress()
        +getPhoneIpAddress(): String
        +sendIpAddressThroughBle(ip, port)
    }
    
    class DisplayNaviManager {
        -wifiClientManager: WiFiClientManager
        -mTcpServerConnection: TcpServerConnection
        +connectToInstrumentWifi(context, ssid, password)
        +startTcpServer()
        +shutdown()
    }
    
    class ConnectionManager {
        +connectToInstrumentWifi(context, ssid, password)
        +requestWifiInfo(isReset)
        +checkVersion(version)
    }
    
    class NetworkPermissionHelper {
        <<utility>>
        +hasAllPermissions(context): Boolean
        +requestPermissions(activity)
        +getRequiredPermissions(): List~String~
    }
    
    WiFiClientManager --> WiFiClientManagerListener : implements
    DisplayNaviManager --> WiFiClientManager : uses
    DisplayNaviManager --> TcpServerConnection : uses
    ConnectionManager --> DisplayNaviManager : uses
    WiFiClientManager --> NetworkPermissionHelper : uses
    
    class WifiClientState {
        <<enumeration>>
        IDLE
        SCANNING
        CONNECTING
        CONNECTED
        DISCONNECTED
        CONNECTION_FAILED
        RECONNECTING
    }
    
    WiFiClientManager --> WifiClientState : uses
```

### 连接建立执行流程

```mermaid
flowchart TD
    A[开始连接] --> B[检查权限]
    B --> C{权限是否完整}
    C -->|否| D[请求权限]
    C -->|是| E[检查WiFi状态]
    
    D --> F{权限授权结果}
    F -->|拒绝| G[连接失败]
    F -->|同意| E
    
    E --> H{WiFi是否开启}
    H -->|否| I[提示开启WiFi]
    H -->|是| J[检查Android版本]
    
    J --> K{API >= 29}
    K -->|是| L[使用现代化API]
    K -->|否| M[使用传统API]
    
    L --> N[WifiNetworkSpecifier]
    N --> O[NetworkRequest]
    O --> P[requestNetwork]
    
    M --> Q[扫描WiFi网络]
    Q --> R[创建WifiConfiguration]
    R --> S[addNetwork & enableNetwork]
    
    P --> T[NetworkCallback监听]
    S --> U[BroadcastReceiver监听]
    
    T --> V{连接结果}
    U --> V
    
    V -->|成功| W[获取手机IP地址]
    V -->|失败| X[重连机制]
    
    W --> Y[启动TCP服务器]
    Y --> Z[通过BLE发送IP给仪表]
    Z --> AA[等待仪表TCP客户端连接]
    AA --> BB{仪表连接结果}
    BB -->|成功| CC[连接完成]
    BB -->|失败| DD[TCP重连]
    
    X --> EE{重连次数检查}
    EE -->|未超限| FF[指数退避延迟]
    EE -->|超限| G
    FF --> J
    
    DD --> GG{TCP重连次数}
    GG -->|未超限| Y
    GG -->|超限| G
    
    style CC fill:#c8e6c9
    style G fill:#ffcdd2
    style I fill:#fff3e0
```

### 错误处理机制

```mermaid
flowchart TD
    A[检测到异常] --> B{异常类型判断}
    
    B -->|权限异常| C[PermissionHelper处理]
    B -->|WiFi连接失败| D[WiFi重连机制]
    B -->|TCP连接失败| E[TCP重连机制]
    B -->|网络状态异常| F[网络监听机制]
    
    C --> C1[检查缺失权限]
    C1 --> C2[显示权限请求对话框]
    C2 --> C3{用户是否授权}
    C3 -->|是| C4[重新开始连接]
    C3 -->|否| C5[显示功能受限提示]
    
    D --> D1[记录失败次数]
    D1 --> D2{是否超过最大重试次数}
    D2 -->|否| D3[计算退避延迟]
    D2 -->|是| D4[连接彻底失败]
    D3 --> D5[延迟后重新连接]
    D5 --> D6[重新执行WiFi连接流程]
    
    E --> E1[记录TCP连接失败]
    E1 --> E2[获取仪表端IP]
    E2 --> E3[重新建立TCP连接]
    E3 --> E4{TCP连接是否成功}
    E4 -->|是| E5[恢复正常通信]
    E4 -->|否| E6[等待后重试]
    
    F --> F1[监听网络状态变化]
    F1 --> F2{网络是否恢复}
    F2 -->|是| F3[重新开始连接流程]
    F2 -->|否| F1
    
    style C4 fill:#c8e6c9
    style C5 fill:#ffcdd2
    style D4 fill:#ffcdd2
    style E5 fill:#c8e6c9
    style F3 fill:#c8e6c9
```

## 1. WiFiClientManager.kt - WiFi客户端管理类

```kotlin
package com.link.riderservice.connection.network

import android.Manifest
import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.*
import android.net.wifi.ScanResult
import android.net.wifi.WifiConfiguration
import android.net.wifi.WifiManager
import android.net.wifi.WifiNetworkSpecifier
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import com.link.riderservice.api.RiderService
import com.link.riderservice.utils.TimeUtils
import java.net.Inet4Address

/**
 * WiFi客户端管理器
 * 替换原有的SoftP2pManager，实现连接到仪表端WiFi热点功能
 */
internal class WiFiClientManager(
    private val listener: WiFiClientManagerListener
) {
    private val context = RiderService.instance.getApplication().applicationContext
    private val wifiManager: WifiManager by lazy {
        context.getSystemService(Context.WIFI_SERVICE) as WifiManager
    }
    private val connectivityManager: ConnectivityManager by lazy {
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    private var currentState = WifiClientState.WIFI_CLIENT_START
    private var targetSsid: String = ""
    private var targetPassword: String = ""
    private var currentNetwork: Network? = null
    private var reconnectionAttempts = 0
    private val reconnectionHandler = Handler(Looper.getMainLooper())

    companion object {
        private const val TAG = "WiFiClientManager"
        private const val MAX_RECONNECTION_ATTEMPTS = 5
        private const val RECONNECTION_FAILED = -999
    }

    // WiFi状态广播接收器
    private val wifiStateReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                WifiManager.WIFI_STATE_CHANGED_ACTION -> {
                    handleWifiStateChanged(intent)
                }
                WifiManager.NETWORK_STATE_CHANGED_ACTION -> {
                    handleNetworkStateChanged(intent)
                }
                ConnectivityManager.CONNECTIVITY_ACTION -> {
                    handleConnectivityChanged()
                }
            }
        }
    }

    // 网络回调（Android 6.0+）
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            super.onAvailable(network)
            Log.d(TAG, "Network available: $network")
            
            currentNetwork = network
            
            // 绑定应用到此网络（Android 6.0+）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                try {
                    connectivityManager.bindProcessToNetwork(network)
                } catch (e: Exception) {
                    Log.w(TAG, "Failed to bind process to network", e)
                }
            }
            
            val ipAddress = getNetworkIpAddress(network)
            updateState(WifiClientState.CONNECTED)
            
            // 验证连接有效性
            updateState(WifiClientState.VALIDATING_CONNECTION)
            if (validateConnection(ipAddress)) {
                updateState(WifiClientState.CONNECTION_VALIDATED)
                reconnectionAttempts = 0 // 重置重连计数
                listener.onWifiConnected(targetSsid, ipAddress)
            } else {
                updateState(WifiClientState.CONNECTION_INVALID)
                listener.onWifiConnectionFailed(-2)
            }
        }
        
        override fun onLost(network: Network) {
            super.onLost(network)
            Log.d(TAG, "Network lost: $network")
            
            if (currentNetwork == network) {
                currentNetwork = null
                updateState(WifiClientState.DISCONNECTED)
                listener.onWifiDisconnected()
                
                // 启动重连
                startReconnection()
            }
        }
        
        override fun onUnavailable() {
            super.onUnavailable()
            Log.d(TAG, "Network unavailable")
            updateState(WifiClientState.CONNECTION_FAILED)
            listener.onWifiConnectionFailed(-1)
        }
        
        override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities)
            Log.d(TAG, "Network capabilities changed: $networkCapabilities")
        }
    }

    /**
     * 连接到指定WiFi网络
     */
    fun connectToWifi(ssid: String, password: String) {
        Log.d(TAG, "Connecting to WiFi: $ssid")
        
        targetSsid = ssid
        targetPassword = password
        
        if (!checkPermissions()) {
            Log.e(TAG, "Missing required permissions")
            updateState(WifiClientState.CONNECTION_FAILED)
            listener.onWifiConnectionFailed(-2)
            return
        }
        
        if (!wifiManager.isWifiEnabled) {
            Log.e(TAG, "WiFi is not enabled")
            listener.onWifiState(false)
            return
        }
        
        // 检查是否已连接到目标网络
        if (currentState == WifiClientState.CONNECTED && getCurrentSsid() == ssid) {
            Log.d(TAG, "Already connected to target network")
            return
        }
        
        updateState(WifiClientState.CHECKING_PERMISSIONS)
        
        if (!checkPermissions()) {
            updateState(WifiClientState.PERMISSION_DENIED)
            listener.onWifiConnectionFailed(-1)
            return
        }
        
        updateState(WifiClientState.WIFI_STATE_CHECK)
        updateState(WifiClientState.CONNECTING)
        listener.onWifiConnecting(ssid)
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            connectToWifiModern(ssid, password)
        } else {
            connectToWifiLegacy(ssid, password)
        }
    }

    /**
     * Android 10+ 使用新的网络请求API
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    private fun connectToWifiModern(ssid: String, password: String) {
        try {
            val wifiNetworkSpecifier = WifiNetworkSpecifier.Builder()
                .setSsid(ssid)
                .setWpa2Passphrase(password)
                .build()

            val networkRequest = NetworkRequest.Builder()
                .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
                .setNetworkSpecifier(wifiNetworkSpecifier)
                .build()

            // 先取消之前的请求
            try {
                connectivityManager.unregisterNetworkCallback(networkCallback)
            } catch (e: Exception) {
                // 忽略未注册的异常
            }

            connectivityManager.requestNetwork(networkRequest, networkCallback)
            Log.d(TAG, "Modern WiFi connection request sent")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to connect using modern API", e)
            updateState(WifiClientState.CONNECTION_FAILED)
            listener.onWifiConnectionFailed(-3)
        }
    }

    /**
     * Android 6.0-9 使用传统WifiConfiguration方式
     */
    @SuppressLint("MissingPermission")
    private fun connectToWifiLegacy(ssid: String, password: String) {
        try {
            // 先扫描网络，确认目标网络存在
            startWifiScanForTarget(ssid) { found ->
                if (found) {
                    connectToWifiLegacyDirect(ssid, password)
                } else {
                    Log.e(TAG, "Target network not found in scan")
                    updateState(WifiClientState.CONNECTION_FAILED)
                    listener.onWifiConnectionFailed(-4)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to connect using legacy API", e)
            updateState(WifiClientState.CONNECTION_FAILED)
            listener.onWifiConnectionFailed(-5)
        }
    }

    @SuppressLint("MissingPermission")
    private fun connectToWifiLegacyDirect(ssid: String, password: String) {
        val wifiConfig = WifiConfiguration().apply {
            SSID = "\"$ssid\""
            preSharedKey = "\"$password\""
            allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
            allowedProtocols.set(WifiConfiguration.Protocol.RSN)
            allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
            allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
        }

        // 移除可能存在的相同配置
        val existingConfigs = wifiManager.configuredNetworks
        existingConfigs?.forEach { config ->
            if (config.SSID == "\"$ssid\"") {
                wifiManager.removeNetwork(config.networkId)
            }
        }

        val networkId = wifiManager.addNetwork(wifiConfig)
        if (networkId != -1) {
            Log.d(TAG, "WiFi configuration added, networkId: $networkId")
            
            wifiManager.disconnect()
            Thread.sleep(500) // 短暂等待断开完成
            
            val enabled = wifiManager.enableNetwork(networkId, true)
            if (enabled) {
                wifiManager.reconnect()
                Log.d(TAG, "WiFi reconnect requested")
            } else {
                Log.e(TAG, "Failed to enable network")
                updateState(WifiClientState.CONNECTION_FAILED)
                listener.onWifiConnectionFailed(-6)
            }
        } else {
            Log.e(TAG, "Failed to add WiFi configuration")
            updateState(WifiClientState.CONNECTION_FAILED)
            listener.onWifiConnectionFailed(-7)
        }
    }

    /**
     * 扫描WiFi网络
     */
    @SuppressLint("MissingPermission")
    private fun startWifiScanForTarget(targetSsid: String, callback: (Boolean) -> Unit) {
        updateState(WifiClientState.SCANNING)
        
        val scanReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                if (intent.action == WifiManager.SCAN_RESULTS_AVAILABLE_ACTION) {
                    val scanResults = wifiManager.scanResults
                    val found = scanResults.any { it.SSID == targetSsid }
                    
                    Log.d(TAG, "Scan completed, target network found: $found")
                    listener.onNetworkScanComplete(scanResults)
                    
                    context.unregisterReceiver(this)
                    callback(found)
                }
            }
        }
        
        val filter = IntentFilter(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION)
        context.registerReceiver(scanReceiver, filter)
        
        val scanStarted = wifiManager.startScan()
        if (!scanStarted) {
            Log.w(TAG, "Failed to start WiFi scan")
            context.unregisterReceiver(scanReceiver)
            callback(false)
        }
    }

    /**
     * 断开WiFi连接
     */
    fun disconnect() {
        Log.d(TAG, "Disconnecting WiFi")
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                connectivityManager.bindProcessToNetwork(null)
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                connectivityManager.unregisterNetworkCallback(networkCallback)
            }
        } catch (e: Exception) {
            Log.w(TAG, "Error during disconnect", e)
        }
        
        currentNetwork = null
        updateState(WifiClientState.DISCONNECTED)
    }

    /**
     * 获取当前连接的SSID
     */
    @SuppressLint("MissingPermission")
    private fun getCurrentSsid(): String {
        return try {
            val wifiInfo = wifiManager.connectionInfo
            wifiInfo?.ssid?.replace("\"", "") ?: ""
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current SSID", e)
            ""
        }
    }

    /**
     * 获取网络IP地址
     */
    private fun getNetworkIpAddress(network: Network): String {
        return try {
            val linkProperties = connectivityManager.getLinkProperties(network)
            linkProperties?.linkAddresses?.find { 
                it.address is Inet4Address 
            }?.address?.hostAddress ?: ""
        } catch (e: Exception) {
            Log.e(TAG, "Error getting IP address", e)
            ""
        }
    }

    /**
     * 启动重连机制
     */
    private fun startReconnection() {
        if (currentState == WifiClientState.RECONNECTING || 
            targetSsid.isEmpty() || 
            reconnectionAttempts >= MAX_RECONNECTION_ATTEMPTS) {
            return
        }
        
        updateState(WifiClientState.RECONNECTING)
        reconnectionAttempts++
        
        val delay = calculateBackoffDelay(reconnectionAttempts)
        Log.d(TAG, "Scheduling reconnection attempt $reconnectionAttempts in ${delay}ms")
        
        reconnectionHandler.postDelayed({
            if (reconnectionAttempts <= MAX_RECONNECTION_ATTEMPTS) {
                Log.d(TAG, "Reconnection attempt: $reconnectionAttempts")
                connectToWifi(targetSsid, targetPassword)
            } else {
                Log.e(TAG, "Max reconnection attempts reached")
                updateState(WifiClientState.CONNECTION_FAILED)
                listener.onWifiConnectionFailed(RECONNECTION_FAILED)
            }
        }, delay)
    }

    private fun calculateBackoffDelay(attempt: Int): Long {
        // 指数退避：1s, 2s, 4s, 8s, 16s
        return minOf(1000L * (1 shl (attempt - 1)), 16000L)
    }

    /**
     * 检查必要权限
     */
    private fun checkPermissions(): Boolean {
        val fineLocation = ContextCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
        
        val nearbyWifiDevices = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context, Manifest.permission.NEARBY_WIFI_DEVICES
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }
        
        return fineLocation && nearbyWifiDevices
    }

    private fun handleWifiStateChanged(intent: Intent) {
        val state = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN)
        when (state) {
            WifiManager.WIFI_STATE_DISABLED -> {
                Log.d(TAG, "WiFi disabled")
                listener.onWifiState(false)
                updateState(WifiClientState.DISCONNECTED)
            }
            WifiManager.WIFI_STATE_ENABLED -> {
                Log.d(TAG, "WiFi enabled")
                listener.onWifiState(true)
            }
        }
    }

    private fun handleNetworkStateChanged(intent: Intent) {
        val networkInfo = intent.getParcelableExtra<NetworkInfo>(WifiManager.EXTRA_NETWORK_INFO)
        Log.d(TAG, "Network state changed: ${networkInfo?.state}")
        
        // 这里主要用于Android 6.0以下版本的网络状态监听
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            when (networkInfo?.state) {
                NetworkInfo.State.CONNECTED -> {
                    if (networkInfo.type == ConnectivityManager.TYPE_WIFI) {
                        val currentSsid = getCurrentSsid()
                        if (currentSsid == targetSsid) {
                            Log.d(TAG, "Legacy: Connected to target network")
                            updateState(WifiClientState.CONNECTED)
                            listener.onWifiConnected(targetSsid, getWifiIpAddress())
                        }
                    }
                }
                NetworkInfo.State.DISCONNECTED -> {
                    if (currentState == WifiClientState.CONNECTED) {
                        Log.d(TAG, "Legacy: Disconnected from network")
                        updateState(WifiClientState.DISCONNECTED)
                        listener.onWifiDisconnected()
                        startReconnection()
                    }
                }
                else -> {
                    // 其他状态不处理
                }
            }
        }
    }

    @SuppressLint("MissingPermission")
    private fun getWifiIpAddress(): String {
        return try {
            val wifiInfo = wifiManager.connectionInfo
            val ipInt = wifiInfo.ipAddress
            String.format(
                "%d.%d.%d.%d",
                ipInt and 0xff,
                ipInt shr 8 and 0xff,
                ipInt shr 16 and 0xff,
                ipInt shr 24 and 0xff
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting WiFi IP", e)
            ""
        }
    }

    private fun handleConnectivityChanged() {
        // 处理网络连接变化
        val networkInfo = connectivityManager.activeNetworkInfo
        Log.d(TAG, "Connectivity changed: ${networkInfo?.isConnected}")
    }

    private fun updateState(newState: WifiClientState) {
        Log.d(TAG, "State changed: $currentState -> $newState")
        currentState = newState
    }

    /**
     * 初始化
     */
    init {
        // 注册广播接收器
        val filter = IntentFilter().apply {
            addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)
            addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION)
            addAction(ConnectivityManager.CONNECTIVITY_ACTION)
        }
        context.registerReceiver(wifiStateReceiver, filter)
    }

    /**
     * 销毁资源
     */
    fun destroy() {
        try {
            context.unregisterReceiver(wifiStateReceiver)
        } catch (e: Exception) {
            Log.e(TAG, "Error unregistering receiver", e)
        }
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                connectivityManager.unregisterNetworkCallback(networkCallback)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error unregistering network callback", e)
        }
        
        reconnectionHandler.removeCallbacksAndMessages(null)
        disconnect()
    }
}

/**
 * WiFi客户端连接状态
 */
enum class WifiClientState {
    WIFI_CLIENT_START,      // 初始化状态
    CHECKING_PERMISSIONS,   // 检查权限
    PERMISSION_DENIED,      // 权限被拒绝
    WIFI_STATE_CHECK,       // 检查WiFi状态
    WIFI_DISABLED,          // WiFi未开启
    SCANNING,               // 扫描网络中
    NETWORK_FOUND,          // 找到目标网络
    SCAN_FAILED,            // 扫描失败
    SCAN_TIMEOUT,           // 扫描超时
    CONNECTING,             // 连接中
    CONNECTED,              // 已连接
    VALIDATING_CONNECTION,  // 验证连接
    CONNECTION_VALIDATED,   // 连接验证通过
    CONNECTION_INVALID,     // 连接无效
    DISCONNECTED,           // 已断开
    CONNECT_FAILED,         // 连接失败
    CONNECT_TIMEOUT,        // 连接超时
    RECONNECTING,           // 重连中
    RECONNECT_FAILED        // 重连失败
}
```

## 2. WiFiClientManagerListener.kt - 监听接口

```kotlin
package com.link.riderservice.connection.network

import android.net.wifi.ScanResult

/**
 * WiFi客户端管理器事件监听接口
 */
interface WiFiClientManagerListener {
    /**
     * 开始连接WiFi
     * @param ssid 目标网络SSID
     */
    fun onWifiConnecting(ssid: String)

    /**
     * WiFi连接成功
     * @param ssid 连接的网络SSID
     * @param ipAddress 获得的IP地址
     */
    fun onWifiConnected(ssid: String, ipAddress: String)

    /**
     * WiFi连接断开
     */
    fun onWifiDisconnected()

    /**
     * WiFi连接失败
     * @param reason 失败原因码
     */
    fun onWifiConnectionFailed(reason: Int)

    /**
     * 网络扫描完成
     * @param networks 扫描到的网络列表
     */
    fun onNetworkScanComplete(networks: List<ScanResult>)

    /**
     * 请求WiFi信息
     */
    fun requestWifiInfo()

    /**
     * WiFi状态变化
     * @param opened WiFi是否开启
     */
    fun onWifiState(opened: Boolean)
}
```

## 3. TcpServerConnection.kt - TCP服务器连接

```kotlin
package com.link.riderservice.connection.display.transport.wifi

import android.util.Log
import com.link.riderservice.connection.display.transport.DataConnection
import com.link.riderservice.inject.ModuleInject
import com.link.riderservice.utils.WiFiUtils
import com.link.riderservice.api.RiderProtocol
import java.io.IOException
import java.net.ServerSocket
import java.net.Socket
import java.util.concurrent.atomic.AtomicBoolean

/**
 * TCP服务器连接 - 保持现有架构
 * 手机端作为TCP服务器，仪表端作为TCP客户端连接
 */
class TcpServerConnection : DataConnection() {
    private var serverSocket: ServerSocket? = null
    private var clientSocket: Socket? = null
    private var listenPort: Int = 30512
    private var isServerRunning = AtomicBoolean(false)

    companion object {
        private const val TAG = "TcpServerConnection"
        private const val CONNECTION_TIMEOUT = 10000 // 10秒连接超时
        private const val SO_TIMEOUT = 30000 // 30秒读取超时
    }

    /**
     * 启动TCP服务器
     */
    override fun onStart() {
        startTcpServer()
    }

    private fun startTcpServer() {
        if (isServerRunning.get()) {
            Log.w(TAG, "TCP server already running")
            return
        }

        try {
            serverSocket = ServerSocket(listenPort)
            Log.d(TAG, "TCP server started on port $listenPort")
            isServerRunning.set(true)

            // 启动服务器后，通过BLE通知仪表端手机的IP地址
            notifyInstrumentWithIpAddress()

            // 启动接受连接的线程
            AcceptThread("TCP Server Accept").start()

        } catch (e: IOException) {
            Log.e(TAG, "Failed to start TCP server", e)
            isServerRunning.set(false)
            mCallback?.onDisconnected()
        }
    }

    override fun onShutdown() {
        isServerRunning.set(false)
        
        try {
            clientSocket?.close()
            serverSocket?.close()
        } catch (e: IOException) {
            Log.e(TAG, "Error closing server socket", e)
        }
        
        clientSocket = null
        serverSocket = null
        Log.d(TAG, "TCP server shutdown")
    }

    /**
     * 通知仪表端IP地址
     */
    private fun notifyInstrumentWithIpAddress() {
        val phoneIp = getPhoneIpAddress()
        if (phoneIp.isNotEmpty()) {
            Log.d(TAG, "Notifying instrument with IP: $phoneIp:$listenPort")
            sendIpAddressThroughBle(phoneIp, listenPort)
        } else {
            Log.e(TAG, "Failed to get phone IP address")
        }
    }

    /**
     * 获取手机在WiFi网络中的IP地址
     */
    private fun getPhoneIpAddress(): String {
        return try {
            WiFiUtils.getWifiIpAddress()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting phone IP address", e)
            ""
        }
    }

    /**
     * 通过BLE发送IP地址给仪表端
     */
    private fun sendIpAddressThroughBle(ipAddress: String, port: Int) {
        try {
            ModuleInject.messageManager.queueOutgoing(
                RiderProtocol.NaviMessageId.MSG_PHONE_IP_NOTIFICATION_VALUE,
                RiderProtocol.PhoneIpNotification
                    .newBuilder()
                    .setIpAddress(ipAddress)
                    .setPort(port)
                    .build()
            )
            Log.d(TAG, "IP address sent via BLE: $ipAddress:$port")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send IP address via BLE", e)
        }
    }

    /**
     * 接受TCP客户端连接的线程
     */
    inner class AcceptThread(name: String) : Thread(name) {
        override fun run() {
            try {
                Log.d(TAG, "Waiting for instrument TCP client connection...")
                
                val incomingSocket = serverSocket?.accept()
                if (incomingSocket != null) {
                    Log.d(TAG, "Instrument connected from: ${incomingSocket.remoteSocketAddress}")
                    
                    // 配置socket参数
                    incomingSocket.tcpNoDelay = true
                    incomingSocket.soTimeout = SO_TIMEOUT
                    incomingSocket.keepAlive = true
                    
                    clientSocket = incomingSocket
                    
                    // 创建传输层
                    val transport = TcpTransport(incomingSocket)
                    mCallback?.onConnected(transport)
                } else {
                    Log.e(TAG, "Failed to accept connection - socket is null")
                    mCallback?.onDisconnected()
                }
                
            } catch (e: IOException) {
                if (isServerRunning.get()) {
                    Log.e(TAG, "Error accepting TCP connection", e)
                    mCallback?.onDisconnected()
                } else {
                    Log.d(TAG, "Server socket closed normally")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Unexpected error in accept thread", e)
                mCallback?.onDisconnected()
            }
        }
    }

    /**
     * 检查服务器是否运行
     */
    fun isServerRunning(): Boolean {
        return isServerRunning.get() && serverSocket?.isClosed == false
    }

    /**
     * 检查是否有客户端连接
     */
    fun hasClientConnected(): Boolean {
        return clientSocket?.isConnected == true && clientSocket?.isClosed == false
    }

    /**
     * 获取连接信息
     */
    fun getConnectionInfo(): String {
        return when {
            !isServerRunning() -> "Server not running"
            !hasClientConnected() -> "Server running on port $listenPort, waiting for client"
            else -> "Client connected from ${clientSocket?.remoteSocketAddress}"
        }
    }
}
```

## 4. 修改DisplayNaviManager.kt

```kotlin
// 在DisplayNaviManager.kt中的修改部分

// 替换原有的P2P监听器
private val mWifiClientListener = object : WiFiClientManagerListener {
    override fun onWifiConnecting(ssid: String) {
        Log.d(TAG, "Connecting to WiFi: $ssid")
    }

    override fun onWifiConnected(ssid: String, ipAddress: String) {
        Log.d(TAG, "WiFi connected: $ssid, IP: $ipAddress")
        isWifiConnected = true
        
        // 连接到仪表端的TCP服务器
        startTcpServer()
    }

    override fun onWifiDisconnected() {
        Log.d(TAG, "WiFi disconnected")
        if (isWifiConnected) {
            shutdown()
        }
        isWifiConnected = false
    }

    override fun onWifiConnectionFailed(reason: Int) {
        Log.e(TAG, "WiFi connection failed: $reason")
        requestWifiInfo()
    }

    override fun onNetworkScanComplete(networks: List<ScanResult>) {
        Log.d(TAG, "Network scan completed, found ${networks.size} networks")
    }

    override fun requestWifiInfo() {
        requestWifiInfo()
    }

    override fun onWifiState(opened: Boolean) {
        onWifiState(opened)
    }
}

// 替换P2P管理器为WiFi客户端管理器
private val wifiClientManager: WiFiClientManager = WiFiClientManager(mWifiClientListener)

// 替换TCP连接为客户端模式
private var mTcpServerConnection: TcpServerConnection? = null

// 修改连接方法
fun connectToInstrumentWifi(context: Context, ssid: String, password: String) {
    Log.d(TAG, "Connecting to instrument WiFi: $ssid")
    mContext = context
    wifiClientManager.connectToWifi(ssid, password)
}

// 新增TCP服务器连接方法
private fun startTcpServer() {
    Log.d(TAG, "Starting TCP server connection")
    
    mTcpServerConnection = TcpServerConnection().apply {
        setCallback(this@DisplayNaviManager)
        start()
        startTcpServer()
    }
}

// 修改shutdown方法
override fun shutdown() {
    Log.d(TAG, "Shutting down WiFi client connection")
    wifiClientManager.disconnect()
    mTcpServerConnection?.shutdown()
    // ... 其他清理代码
}
```

## 5. 修改ConnectionManager.kt

```kotlin
// 在ConnectionManager.kt中的修改部分

/**
 * 连接到仪表WiFi热点
 */
fun connectToInstrumentWifi(context: Context, ssid: String, password: String) {
    if (_connectionStatus.value.wifiStatus is WifiStatus.DeviceConnected) {
        Log.d(TAG, "WiFi already connected")
        return
    }
    
    Log.d(TAG, "Connecting to instrument WiFi: $ssid")
    mDisplayNaviManager.connectToInstrumentWifi(context, ssid, password)
}

/**
 * 请求仪表WiFi信息 - 修改为AP模式
 */
fun requestWifiInfo(isReset: Boolean = true) {
    Log.d(TAG, "requestWifiInfo ${_connectionStatus.value.btStatus}")
    if (_connectionStatus.value.btStatus is BleStatus.DeviceConnected) {
        ModuleInject.messageManager.queueOutgoing(
            RiderProtocol.NaviMessageId.MSG_WIFI_INFO_REQUEST_VALUE,
            RiderProtocol.WifiInfoRequest
                .newBuilder()
                .setWifiMode(RiderProtocol.WifiMode.WIFI_AP) // 改为AP模式
                .setIsResetWifi(isReset)
                .build()
        )
    }
}

// 更新版本检查中的WiFi连接逻辑
fun checkVersion(version: RiderProtocol.ProtocolVerNotification) {
    cancelConnectWithTimeout()
    if (version.major <= RiderProtocol.ProtocolVersion.MAJOR_VERSION_VALUE) {
        if (RiderBleManager.instance.isConfigConnect() && !RiderService.instance.getOnBleItemClick()) {
            val sharedPref = RiderService.instance.getApplication()
                .getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
            val ssid = sharedPref.getString("wifi_ssid", "") // 改为存储SSID
            val password = sharedPref.getString("wifi_password", "") // 改为存储密码
            
            if (!ssid.isNullOrEmpty() && !password.isNullOrEmpty()) {
                if (_connectionStatus.value.wifiStatus is WifiStatus.DeviceConnected) {
                    Log.d(TAG, "WiFi already connected")
                    return
                }
                connectToInstrumentWifi(
                    RiderService.instance.getApplication(),
                    ssid,
                    password
                )
            } else {
                showDialog(mApplication.getString(R.string.error_title), 
                         mApplication.getString(R.string.config_error))
            }
        } else {
            requestWifiInfo(false)
        }
    } else {
        protocolError(mApplication.getString(R.string.version_error))
    }
}
```

## 6. 修改RiderService.kt

```kotlin
// 在RiderService.kt中的修改部分

private fun handleWifiInfo(msg: ByteArray?) {
    Log.d("connect analysis:", "wifi info response (client mode)::${TimeUtils.getCurrentTimeStr()}")
    val wifiInfo = RiderProtocol.WifiInfoNotification.parseFrom(msg)
    
    // 获取仪表端WiFi热点信息
    val instrumentSsid = wifiInfo.name ?: ""
    val instrumentPassword = wifiInfo.password ?: ""
    
    if (instrumentSsid.isNotEmpty()) {
        Log.d(TAG, "Received instrument WiFi info - SSID: $instrumentSsid")
        
        // 保存WiFi信息到SharedPreferences
        val sharedPref = getApplication()
            .getSharedPreferences("config_auto_connection", Context.MODE_PRIVATE)
        with(sharedPref.edit()) {
            putString("wifi_ssid", instrumentSsid)
            putString("wifi_password", instrumentPassword)
            apply()
        }
        
        // 连接到仪表端WiFi热点
        mConnectionManager.connectToInstrumentWifi(
            getApplication().applicationContext,
            instrumentSsid,
            instrumentPassword
        )
    } else {
        Log.e(TAG, "Invalid WiFi info received from instrument")
        // 可以显示错误提示或重新请求WiFi信息
    }
}
```

## 7. 权限配置 - AndroidManifest.xml

```xml
<!-- WiFi基础权限 -->
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />

<!-- 位置权限（WiFi扫描需要） -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

<!-- Android 13+ 的附近WiFi设备权限 -->
<uses-permission 
    android:name="android.permission.NEARBY_WIFI_DEVICES" 
    android:usesPermissionFlags="neverForLocation"
    tools:targetApi="s" />

<!-- 网络连接权限 -->
<uses-permission android:name="android.permission.INTERNET" />
```

## 8. 权限请求助手类

```kotlin
package com.link.riderservice.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * 网络权限助手类
 */
object NetworkPermissionHelper {
    
    const val PERMISSION_REQUEST_CODE = 1001
    
    /**
     * 检查是否拥有所有必要权限
     */
    fun hasAllPermissions(context: Context): Boolean {
        val requiredPermissions = getRequiredPermissions()
        return requiredPermissions.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    /**
     * 请求必要权限
     */
    fun requestPermissions(activity: Activity) {
        val requiredPermissions = getRequiredPermissions()
        val missingPermissions = requiredPermissions.filter { permission ->
            ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED
        }
        
        if (missingPermissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(
                activity,
                missingPermissions.toTypedArray(),
                PERMISSION_REQUEST_CODE
            )
        }
    }
    
    /**
     * 获取所需权限列表
     */
    private fun getRequiredPermissions(): List<String> {
        val permissions = mutableListOf(
            Manifest.permission.ACCESS_WIFI_STATE,
            Manifest.permission.CHANGE_WIFI_STATE,
            Manifest.permission.ACCESS_NETWORK_STATE,
            Manifest.permission.ACCESS_FINE_LOCATION
        )
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissions.add(Manifest.permission.NEARBY_WIFI_DEVICES)
        }
        
        return permissions
    }
    
    /**
     * 检查权限请求结果
     */
    fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ): Boolean {
        if (requestCode == PERMISSION_REQUEST_CODE) {
            return grantResults.all { it == PackageManager.PERMISSION_GRANTED }
        }
        return false
    }
}
```

## 9. 测试验证代码

```kotlin
/**
 * WiFi客户端连接测试类
 */
class WiFiClientManagerTest {
    
    private lateinit var wifiClientManager: WiFiClientManager
    private var testCompleted = false
    
    fun testWifiClientConnection() {
        val listener = object : WiFiClientManagerListener {
            override fun onWifiConnecting(ssid: String) {
                Log.d("Test", "Connecting to: $ssid")
                assert(ssid.isNotEmpty())
            }
            
            override fun onWifiConnected(ssid: String, ipAddress: String) {
                Log.d("Test", "Connected to: $ssid, IP: $ipAddress")
                assert(ssid.isNotEmpty())
                assert(ipAddress.isNotEmpty())
                testCompleted = true
            }
            
            override fun onWifiConnectionFailed(reason: Int) {
                Log.e("Test", "Connection failed: $reason")
                testCompleted = true
                assert(false) { "WiFi connection should succeed" }
            }
            
            override fun onWifiDisconnected() {
                Log.d("Test", "WiFi disconnected")
            }
            
            override fun onNetworkScanComplete(networks: List<ScanResult>) {
                Log.d("Test", "Scan completed, found ${networks.size} networks")
            }
            
            override fun requestWifiInfo() {}
            override fun onWifiState(opened: Boolean) {}
        }
        
        wifiClientManager = WiFiClientManager(listener)
        
        // 测试连接
        wifiClientManager.connectToWifi("TestInstrumentAP", "testpassword")
        
        // 等待连接完成
        val startTime = System.currentTimeMillis()
        while (!testCompleted && (System.currentTimeMillis() - startTime) < 30000) {
            Thread.sleep(1000)
        }
        
        assert(testCompleted) { "Test should complete within 30 seconds" }
        
        // 清理
        wifiClientManager.destroy()
    }
}
```

这些完善的架构图表和代码示例展示了WiFi AP客户端模式重构的完整实现方案，包含了详细的流程控制、错误处理机制和Android版本兼容性处理。通过这些图表可以清晰地理解整个系统的运行逻辑和各组件之间的交互关系。 
这些完善的架构图表和代码示例展示了WiFi AP客户端模式重构的完整实现方案，包含了详细的流程控制、错误处理机制和Android版本兼容性处理。通过这些图表可以清晰地理解整个系统的运行逻辑和各组件之间的交互关系。 