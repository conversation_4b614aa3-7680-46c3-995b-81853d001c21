# RiderService SDK 设计文档

> 版本：0.1  
> 作者：AI 自动生成（请根据实际情况补充、修订）

---

## 1. 项目概述
RiderService SDK 是一套用于 **Android 手机与两轮车智能仪表**（以下简称 *仪表盘*）进行双向通信、导航投屏和数据服务 的完整解决方案。其核心目标包括： 

1. 通过 **BLE（Bluetooth Low Energy）** 完成快速配对、协议握手与控制信令传输。
2. 通过 **Wi-Fi P2P（或 AP 模式）** 建立高速通道，用于音视频/屏幕镜像数据传输。
3. 提供统一的 **API 封装**（`com.link.riderservice.api.RiderService`），简化 App 集成成本。
4. 内置 **激活&合规校验**、**天气服务**、**通知同步** 等业务插件，开箱即用。

项目以 `app`（Demo）、`riderservice`（SDK 主体） 两个 Gradle Module 组织，其中 `riderservice` 同时包含 Kotlin/Java 代码与 C/C++ Native 实现。

```
├── app                 # 示例 App（可作为集成参考）
└── riderservice        # SDK 主体
    ├── src/main/java   # 业务逻辑（Kotlin/Java）
    ├── src/main/cpp    # RTP/视频流 & Protobuf C++ 实现
    └── docs            # 开发/设计文档
```

---

## 2. 总体架构

```
┌─────────────────────┐
│       应用层        │  ←  app 模块 / 第三方集成 App
└─────────┬───────────┘
          │RiderService API 调用
┌─────────▼───────────┐
│   SDK 核心 (api)    │  com.link.riderservice.api
├─────────┬───────────┤
│ ConnectionManager   │  连接状态管理
│ MessageManager      │  Protobuf 收发队列
│ WeatherRepository   │  天气数据
└─────────┬───────────┘
          │
  ┌───────▼────────────┬──────────────┐
  │     连接层 (connection)          │
  │ ┌────────────┐  ┌──────────────┐ │
  │ │ BleManager │  │ Wifi/Display │ │
  │ └────────────┘  └──────────────┘ │
  └────────┬─────────────────────────┘
           │BLE 握手 / Wi-Fi P2P
┌──────────▼──────────┐   TCP / RTP   ┌─────────────┐
│   智能仪表硬件      │  ←→ 投屏视频 → │  RTP Receiver│ (Native C++)
└─────────────────────┘               └─────────────┘
```

1. **RiderService** 暴露给 App 的唯一入口（单例模式）。
2. **ConnectionManager** 负责 BLE、Wi-Fi、投屏状态协同。
3. **MessageManager** 维护发送/接收队列，通过 Protobuf (`RiderProtocol.proto`) 编码。
4. **Ble** 使用 NordicSemiconductor Android-BLE 库封装（`connection/ble`）。
5. **DisplayNaviManager** 借助 `MediaProjection` 捕获屏幕，H264 编码后通过 Socket 发送给仪表。
6. Native 端 `receiver/rtp` 实现 RTP 解码、渲染。

---

## 3. 关键模块详解

### 3.1 API 层（`com.link.riderservice.api`）

* **RiderService.kt**  
  - 初始化：`RiderService.instance.init(application)`  
  - 连接流程：`startScan() → connectBle() → requestWifiInfo()`  
  - 消息派发：`sendMessageToRiderService(RiderMessage)`  
  - 回调接口：`RiderServiceCallback` 通知连接、投屏、错误等事件。
* **dto/** 定义导航、天气、通知等上行/下行数据模型，保证 API 稳定性。

### 3.2 连接层（`connection`）

| 子包 | 功能 | 备注 |
|------|------|------|
| ble | BLE 扫描、连接、MTU/Notification 设置 | 基于 Nordic BLE 库；`RiderBleManager` 持久化自动重连。 |
| display | Wi-Fi 直连 & 投屏 | `DisplayNaviManager` 将 `MediaProjection` 流推送给仪表，并同步方向/旋转信息。 |
| network | TCP/Socket 底层封装 | -- |

重要类 **ConnectionManager.kt**：集中处理状态流 (`StateFlow<Connection>`)，对外暴露统一连接状态 & 方法。

### 3.3 消息队列（`message`）

* 收到 BLE Notification → `enqueueIncoming()` → Protobuf 解码 → 回调到 `RiderService`。
* App 侧调用 → `queueOutgoing()` → `ConnectionManager.loopWrite()` 循环写入 BLE characteristic。

### 3.4 授权&合规（`authorize`）

通过云端接口校验 `productKey、uuid、licenseSign` 等信息，返回结果后透传给仪表。失败场景会自动弹框并断链。

### 3.5 天气服务（`weather`）

封装外部天气 API，将温度、湿度、气压等数据以 `WeatherInfoNotification` 形式推送给仪表。

### 3.6 Native 实现（`src/main/cpp`）

* **protobuf/** C++ Runtime
* **receiver/** RTP Client，用于接收投屏视频；生成 `librtp.so` 等。

---

## 4. 通信协议

协议定义位于 `riderservice/src/main/proto/RiderProtocol.proto`（省略）。编译后生成 `com.link.riderservice.protobuf.RiderProtocol`。

### 消息类型示例

| Id | 名称 | 方向 | 描述 |
|----|------|------|------|
| `MSG_WIFI_INFO_REQUEST` | 获取仪表 Wi-Fi AP 信息 | App → 仪表 | 握手后触发 |
| `MSG_WIFI_INFO_NOTIFICATION` | 仪表返回 Wi-Fi SSID/Port | 仪表 → App | App 随即连接 Wi-Fi |
| `MSG_NAVI_INFO` | 实时导航信息 | App → 仪表 | 每秒更新 |
| `MSG_WEATHER_INFO_NOTIFICATION` | 天气信息 | App → 仪表 | 可选 |

> 详细字段请参考 proto 文件。

---

## 5. 典型流程

### 5.1 自动互联（Autolink）

1. **扫描 BLE**：`RiderBleManager.startScan()` 发现目标设备。
2. **BLE 握手**：连接成功后写入认证特征，发送 `MSG_PROTOCOL_VERSION_REQUEST`。
3. **合规校验**：仪表下发 `MSG_COMPLIANCE_REQUEST`，SDK 调用云端验证并回复 `MSG_COMPLIANCE_NOTIFICATION`。
4. **激活校验**：同上，流程类似。
5. **Wi-Fi 切换**：发送 `MSG_WIFI_INFO_REQUEST` → 连接 Wi-Fi P2P。
6. **投屏**：`DisplayNaviManager.startScreenProjection()`，屏幕经 H264/RTP 推流到仪表。

### 5.2 手动导航信息推送

```kotlin
val naviInfo = NaviInfo(
    curStep = 3,
    curStepRetainDistance = 120,
    naviType = 0,
    nextRoadName = "人民大道"
)
RiderService.instance.sendMessageToRiderService(naviInfo)
```

---

## 6. 集成&使用示例

### 6.1 Gradle 依赖

若已发布到 Maven 私服：
```gradle
implementation("com.link:riderservice:x.y.z")
```

本仓库示例使用 **多 Module** 直接依赖，可参看 `settings.gradle.kts`。

### 6.2 初始化

```kotlin
class App: Application() {
    override fun onCreate() {
        super.onCreate()
        RiderService.instance.init(this)
    }
}
```

### 6.3 连接&投屏

```kotlin
// 1. 扫描 & 连接 BLE
autoConnectButton.setOnClickListener {
    RiderService.instance.startScan() // 内部自动连接首个匹配设备
}

// 2. 投屏触发（需要用户授权 MediaProjection）
projectBtn.setOnClickListener {
    RiderService.instance.startScreenProjection()
}
```

回调：
```kotlin
object : RiderServiceCallback {
    override fun onScanResult(devices: List<BleDevice>) { /* 更新 UI */ }
    override fun onDisplayInitialized(display: Display) { /* 创建 Presentation */ }
    override fun onDialogShow(title: String, msg: String) { Toast.makeText(ctx, msg, LENGTH).show() }
}
```

---

## 7. 错误处理与重连

* **超时**：`ConnectionManager` 在 3 秒内未收到协议版本回复将自动断链并提示用户。
* **BLE 断开**：自动重连 3 次，失败则进入扫描模式等待用户选择。
* **Wi-Fi 断开**：立即重新请求 Wi-Fi 信息并尝试连接。

---

## 8. 构建&运行

```bash
# 克隆仓库
$ git clone https://example.com/RiderService_SDK.git
$ cd RiderService_SDK

# 使用 Android Studio 打开根目录；或命令行构建
$ ./gradlew assembleDebug
```

Demo APK 生成路径：`app/build/outputs/apk/`。

---

## 9. 后续规划

1. **协议升级**：支持增量升级 Protobuf，避免大版本不兼容。
2. **CarPlay/AA**：引入标准车载投屏协议，兼容更多车型。
3. **单元测试**：补充 ConnectionManager、MessageManager Mock 测试。

---

> 文档自动生成，仅供参考。如有疑问，请查阅源码或联系维护者。 