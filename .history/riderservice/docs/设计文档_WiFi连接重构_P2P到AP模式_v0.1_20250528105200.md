# RiderService SDK Wi-Fi 连接重构设计文档 (P2P 到 AP 模式)

> 版本：0.1
> 作者：AI 自动生成（请根据实际情况补充、修订）
> 基于总体设计文档：`设计文档_投屏重构_v0.5_App发起仪表执行.md`

---

## 1. 背景与目标

本文档旨在详细阐述将 RiderService SDK 中的 Wi-Fi 连接方式从当前的 Wi-Fi Direct (P2P) 模式重构为 Wi-Fi AP (Access Point) 模式的技术方案。目标是提高连接的稳定性、简化连接流程，并可能改善在某些环境下的兼容性。

**核心假设 (待用户确认):**
1.  **智能仪表创建 AP**: 智能仪表硬件将作为 Wi-Fi AP 热点提供方。
2.  **手机 App (SDK) 连接 AP**: RiderService SDK 将负责扫描、连接并维护与仪表 AP 的连接。
3.  **AP 凭证交换**: 仪表的 Wi-Fi AP的SSID和密码 (PSK) 将在 BLE 连接建立后，通过安全的 BLE 通道从仪表发送给手机 App (SDK)。
4.  **仪表 IP 地址**: 仪表在成功建立 AP 后的 IP 地址是可预期的 (例如，作为 AP 的网关 IP，如 `192.168.x.1`) 或通过其他机制通知给 SDK。

---

## 2. 现有 Wi-Fi P2P 逻辑简述 (待补充)

(此处需要简要描述当前 SDK 中 Wi-Fi P2P 的实现方式、关键类和流程。例如，如何发现对端、如何协商组所有者 GO、如何建立连接、如何进行数据传输等。这有助于理解需要修改的部分。如果信息不足，标记为待调研。)

*   当前 P2P 连接主要由 `ConnectionManager` 和可能的 `DisplayNaviManager` (如果涉及 GO 协商) 处理。
*   涉及 `WifiP2pManager` API 的使用。
*   ...

---

## 3. 提议的 Wi-Fi AP 模式逻辑

### 3.1 总体流程

```mermaid
sequenceDiagram
    participant App as 手机 App
    participant SDK as RiderService SDK
    participant Instrument as 智能仪表

    App->>SDK: connectBle(device)
    SDK->>Instrument: 建立 BLE 连接
    Instrument-->>SDK: BLE 连接成功
    Instrument-->>SDK: 发送 Wi-Fi AP SSID 和密码 (via BLE)
    SDK->>SDK: 存储 AP 凭证
    SDK->>App: onBleConnected()
    SDK->>SDK: (内部逻辑) 开始连接仪表 Wi-Fi AP
    SDK->>Android OS: 使用凭证扫描并连接指定 SSID 的 AP
    Android OS-->>SDK: Wi-Fi 连接成功 (获取到 IP 地址)
    SDK->>SDK: (内部逻辑) 确认网络状态，获取本机 IP
    SDK->>Instrument: (可选) 通知仪表 Wi-Fi 已连接 (via BLE)
    SDK->>SDK: 建立与仪表的数据通道 (如 TCP/UDP Socket)
    Note right of SDK: 使用已知的仪表 IP 和端口
    SDK->>App: onRiderServiceConnected() / onWiFiConnected() (或类似回调)
    App->>SDK: requestNaviMode(targetMode)
    SDK->>Instrument: 发送 NaviMode (via Wi-Fi 数据通道或 BLE)
    Instrument-->>SDK: 响应 / 状态变化
    SDK->>App: 相关投屏回调
```

### 3.2 AP 凭证获取与存储
*   当 BLE 连接成功后，SDK 应期望从仪表接收包含 Wi-Fi AP 的 SSID 和密码 (PSK) 的特定 BLE 消息/特征值。
*   SDK 内部应安全地存储这些凭证，至少在当前连接会话中有效。是否持久化存储以及如何安全存储（如加密）待定，但建议会话期间有效即可，每次重新 BLE 连接时重新获取。

### 3.3 连接到仪表的 AP
*   `ConnectionManager` 将负责此过程。
*   SDK 收到 AP 凭证后，使用 Android 的 `WifiManager` 或 `ConnectivityManager` (对于较新 Android 版本，推荐使用 NetworkRequest API) 来连接到指定的 Wi-Fi 网络。
    *   创建一个 `WifiNetworkSuggestion` (Android Q+) 或配置一个 `WifiConfiguration` (旧版)。
    *   触发连接请求。
    *   监听网络连接状态变化广播 (`ConnectivityManager.CONNECTIVITY_ACTION` 或 `NetworkCallback`)。

### 3.4 IP 地址和端口
*   **仪表 IP**: 假设仪表作为 AP 时，其 IP 地址是固定的网关地址 (例如 `************` - Android 默认热点网段)。或者，此 IP 地址可以在 AP 凭证交换时一同由仪表提供。
*   **SDK (手机) IP**: SDK 连接上 AP 后，会由 DHCP 获取到一个 IP 地址。
*   **端口**: 数据传输的端口号应预先定义好，双方遵循此约定。

### 3.5 数据通道建立
*   一旦 Wi-Fi 连接成功并且 SDK 获取到 IP 地址，`ConnectionManager` (或其委托的组件) 将尝试与仪表的已知 IP 和预定义端口建立数据传输通道 (通常是 TCP Socket 用于可靠传输，或 UDP 用于低延迟数据，取决于协议设计)。

### 3.6 错误处理与重连
*   **AP 扫描失败**: 无法找到指定的 SSID。
*   **连接认证失败**: 密码错误。
*   **连接后断开**: Wi-Fi 信号弱或仪表 AP 关闭。
*   `ConnectionManager` 需要实现相应的重试逻辑和状态回调，通知应用层连接失败或中断。
*   在 Wi-Fi 断开后，是否自动尝试重连，以及重连次数策略需要定义。

---

## 4. 组件职责变更

### 4.1 `ConnectionManager.kt`
*   **主要变更方**。
*   移除所有 Wi-Fi P2P 相关逻辑 (发现、连接、GO 协商等)。
*   新增通过 BLE 接收和处理 AP 凭证的逻辑。
*   新增使用 `WifiManager` / `ConnectivityManager` / `NetworkRequest API` 连接到指定 AP 的逻辑。
*   管理 Wi-Fi AP 连接的生命周期：连接、断开、状态监控、错误处理、重连。
*   在 Wi-Fi 连接成功后，建立与仪表的 Socket 数据通道。
*   更新其内部状态机以反映新的连接流程。
*   继续负责将 `NaviMode` (来自 `RiderService`) 发送给仪表，现在主要通过已建立的 Wi-Fi Socket 通道 (或依然通过 BLE，需明确)。

### 4.2 `RiderService.kt`
*   对外的 API 可能影响不大，`connectBle()` 仍然是入口。
*   内部调用 `ConnectionManager` 的逻辑可能需要调整以适应新的 Wi-Fi 连接流程和状态回调。

### 4.3 `DisplayNaviManager.kt` (如果存在并处理 P2P)
*   如果此类包含 P2P 连接或 GO 协商逻辑，这些部分将被移除或重构。
*   其核心职责 (如果保留) 应更专注于与显示相关的逻辑，而非网络连接。

### 4.4 `AutolinkControl.kt` (或类似底层协议封装类)
*   如果它直接处理了 P2P 模式下的 Socket 通信，现在需要调整为在 `ConnectionManager` 建立的 AP 模式 Socket 上进行操作。
*   需要知道目标 IP 地址和端口（由 `ConnectionManager` 提供或配置）。

### 4.5 BLE 通信协议
*   需要新增或修改 BLE 服务/特征，用于仪表向 SDK 安全地传输 Wi-Fi AP 的 SSID 和密码。
*   (可选) 新增 BLE 消息，用于 SDK 通知仪表 Wi-Fi 连接状态。

---

## 5. 配置变更

*   可能不再需要在 App 中请求部分 Wi-Fi P2P 相关权限 (如 `ACCESS_FINE_LOCATION` 对于 P2P 扫描是必需的，但对于连接已知 AP 可能不是，需验证)。`CHANGE_WIFI_STATE`, `ACCESS_WIFI_STATE`, `INTERNET` 权限依然需要。
*   如果仪表端的 AP 参数 (如默认 IP) 是固定的，可以作为常量配置在 SDK 中。

---

## 6. 关键流程详细描述

### 6.1 初始化与 BLE 连接 (同总体设计)

### 6.2 AP 凭证交换与存储
1.  SDK 与仪表 BLE 连接成功。
2.  SDK 监听仪表特定 BLE 特征值的通知/读取请求。
3.  仪表通过该特征值发送 AP 的 SSID 和 PSK。
4.  `ConnectionManager` 接收到数据，解析出 SSID 和 PSK。
5.  `ConnectionManager` 临时存储凭证以供后续连接使用。

### 6.3 连接仪表 AP 并建立数据通道
1.  `ConnectionManager` 使用获取到的 SSID 和 PSK 构建 `NetworkRequest` (或 `WifiConfiguration`)。
2.  调用 `ConnectivityManager.requestNetwork` (或 `WifiManager.addNetwork`, `enableNetwork`)。
3.  通过 `ConnectivityManager.NetworkCallback` (或广播接收器) 监听网络连接结果。
4.  **成功**: 
    *   获取到网络连接 (`Network` 对象)。
    *   (可选) 将此 `Network` 对象绑定到进程，确保后续 Socket 通信走此网络：`ConnectivityManager.bindProcessToNetwork()`。
    *   `ConnectionManager` 更新内部状态为 Wi-Fi 已连接。
    *   尝试与仪表的预知 IP 和端口建立 Socket 连接。
    *   Socket 连接成功后，通知 `RiderService` (进而通知应用层) Wi-Fi 数据通道就绪。
5.  **失败 (超时/认证错误等)**:
    *   `ConnectionManager`记录错误，执行重试策略。
    *   多次失败后，回调错误状态给 `RiderService`。

### 6.4 Wi-Fi 断开与重连
1.  `NetworkCallback` 检测到网络断开 (`onLost`)。
2.  `ConnectionManager` 更新状态，通知 `RiderService` Wi-Fi 断开。
3.  根据重连策略，`ConnectionManager` 尝试重新执行 6.3 中的连接步骤。
4.  如果 BLE 仍然连接，可以直接使用之前获取的 AP 凭证。如果 BLE 也断开，则整个连接流程需要从 BLE 重新开始。

---

## 7. 风险与兼容性

*   **Android 版本差异**: Wi-Fi API 在不同 Android 版本 (特别是 Android Q/10 之后) 有较大变化。使用 `NetworkRequest` 和 `NetworkSuggestion` API 是推荐的现代做法，但需处理好向后兼容性或设定最低 SDK 版本。
*   **权限**: 确保 `AndroidManifest.xml` 中包含正确的 Wi-Fi 和网络权限。
*   **后台连接**: 如果需要在 App 退至后台时仍保持 Wi-Fi 连接和数据传输，需要考虑 Android 的后台执行限制和 Doze 模式影响，可能需要前台服务。
*   **仪表 AP 实现的稳定性**: 仪表端 AP 的稳定性、DHCP 服务、IP 地址分配等直接影响 SDK 连接成功率。
*   **多网络处理**: 手机可能同时连接到蜂窝网络和仪表的 Wi-Fi AP。需要确保 SDK 的数据通信明确通过仪表 AP 的网络接口进行 (通过 `bindProcessToNetwork` 或指定 Socket 使用的 `Network` 对象)。

---

## 8. 待讨论与确认的点

*   **Q1: 仪表创建 AP 的具体参数是固定的还是动态生成的？** (SSID/密码是固定的，还是每次动态生成并通过 BLE 告知？)
*   **Q2: 仪表在 AP 模式下的 IP 地址获取机制？** (固定网关 IP？BLE 通知？mDNS/DNS-SD 服务发现？)
*   **Q3: 数据传输是通过 TCP 还是 UDP？端口号是多少？**
*   **Q4: Wi-Fi 断开后的具体重连策略？** (重试次数、间隔时间等)
*   **Q5: 是否需要在 BLE 断开后自动断开 Wi-Fi AP 连接，还是保持连接尝试重连 BLE？**
*   **Q6: `NaviMode` 等控制信令是通过 Wi-Fi Socket 发送，还是继续通过 BLE 发送？** (建议数据量大的通过 Wi-Fi，轻量控制或带外信令可通过 BLE)

---

该文档为初步设计，具体实现细节待进一步调研和用户确认上述问题后进行完善。 