# WiFi双模式连接核心组件代码示例（简化版）

## ConnectionModeManager - 连接模式管理器

这个文档展示了简化版的双模式连接管理器实现，移除自动回退逻辑，由上层控制模式切换。

```kotlin
package com.link.riderservice.connection

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.link.riderservice.api.RiderService
import com.link.riderservice.ble.SoftP2pManager
import com.link.riderservice.connection.network.WiFiClientManager
import com.link.riderservice.connection.network.WiFiClientManagerListener
import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 连接模式管理器（简化版）
 * 提供清晰的API接口，模式切换由上层控制
 */
class ConnectionModeManager(
    private val listener: ConnectionModeManagerListener
) {
    private val context = RiderService.instance.getApplication().applicationContext
    private val preferences: SharedPreferences by lazy {
        context.getSharedPreferences("wifi_connection_mode", Context.MODE_PRIVATE)
    }
    
    private var currentMode: WifiConnectionMode? = null
    private var isConnecting = AtomicBoolean(false)
    private var connectionJob: Job? = null
    
    // WiFi AP客户端管理器
    private val wifiClientManager: WiFiClientManager by lazy {
        WiFiClientManager(wifiClientListener)
    }
    
    // WiFi P2P管理器（复用现有）
    private val p2pManager: SoftP2pManager by lazy {
        SoftP2pManager(p2pListener)
    }
    
    companion object {
        private const val TAG = "ConnectionModeManager"
        private const val PREF_DEFAULT_MODE = "default_mode"
        private const val CONNECTION_TIMEOUT_MS = 30000L
    }

    /**
     * 主要API：使用指定模式连接
     */
    fun connectWithMode(mode: WifiConnectionMode, context: Context) {
        if (isConnecting.get()) {
            Log.w(TAG, "Connection already in progress")
            listener.onModeConnectionFailed(mode, -1)
            return
        }
        
        Log.d(TAG, "Connecting with mode: $mode")
        isConnecting.set(true)
        currentMode = mode
        
        listener.onModeConnecting(mode)
        
        connectionJob = CoroutineScope(Dispatchers.Main).launch {
            try {
                when (mode) {
                    WifiConnectionMode.WIFI_AP_CLIENT -> {
                        connectApMode()
                    }
                    WifiConnectionMode.WIFI_P2P -> {
                        connectP2pMode()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Connection failed for mode: $mode", e)
                handleConnectionFailure(mode, -999)
            }
        }
    }
    
    /**
     * 断开当前连接
     */
    fun disconnect() {
        Log.d(TAG, "Disconnecting current mode: $currentMode")
        
        connectionJob?.cancel()
        isConnecting.set(false)
        
        when (currentMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                wifiClientManager.disconnect()
            }
            WifiConnectionMode.WIFI_P2P -> {
                p2pManager.stop()
            }
            null -> {
                // 都断开以确保清理
                wifiClientManager.disconnect()
                p2pManager.stop()
            }
        }
        
        currentMode = null
    }
    
    /**
     * 获取当前连接模式
     */
    fun getCurrentMode(): WifiConnectionMode? {
        return currentMode
    }
    
    /**
     * 获取支持的模式列表
     */
    fun getSupportedModes(): List<WifiConnectionMode> {
        return listOf(
            WifiConnectionMode.WIFI_AP_CLIENT,
            WifiConnectionMode.WIFI_P2P
        )
    }
    
    /**
     * 检查指定模式是否可用
     */
    fun isModeAvailable(mode: WifiConnectionMode): Boolean {
        return when (mode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                // 检查WiFi权限和状态
                wifiClientManager.checkPermissions() && wifiClientManager.isWifiEnabled()
            }
            WifiConnectionMode.WIFI_P2P -> {
                // 检查P2P是否可用
                p2pManager.isP2pSupported()
            }
        }
    }
    
    /**
     * 设置默认连接模式
     */
    fun setDefaultMode(mode: WifiConnectionMode) {
        preferences.edit()
            .putString(PREF_DEFAULT_MODE, mode.name)
            .apply()
        Log.d(TAG, "Default mode set to: $mode")
    }
    
    /**
     * 获取默认连接模式
     */
    fun getDefaultMode(): WifiConnectionMode {
        val modeName = preferences.getString(PREF_DEFAULT_MODE, WifiConnectionMode.WIFI_AP_CLIENT.name)
        return try {
            WifiConnectionMode.valueOf(modeName!!)
        } catch (e: Exception) {
            WifiConnectionMode.WIFI_AP_CLIENT
        }
    }
    
    /**
     * AP模式连接
     */
    private suspend fun connectApMode() {
        withTimeout(CONNECTION_TIMEOUT_MS) {
            suspendCancellableCoroutine<Unit> { continuation ->
                // 请求仪表端WiFi热点信息
                requestWifiApInfo { ssid, password ->
                    if (ssid.isNotEmpty()) {
                        wifiClientManager.connectToWifi(ssid, password)
                        // 连接结果通过wifiClientListener回调处理
                    } else {
                        continuation.resumeWith(Result.failure(Exception("Invalid WiFi info")))
                    }
                }
            }
        }
    }
    
    /**
     * P2P模式连接
     */
    private suspend fun connectP2pMode() {
        withTimeout(CONNECTION_TIMEOUT_MS) {
            suspendCancellableCoroutine<Unit> { continuation ->
                // 启动P2P连接
                p2pManager.startConnection()
                // 连接结果通过p2pListener回调处理
            }
        }
    }
    
    /**
     * WiFi客户端监听器
     */
    private val wifiClientListener = object : WiFiClientManagerListener {
        override fun onWifiConnecting(ssid: String) {
            Log.d(TAG, "WiFi connecting to: $ssid")
        }
        
        override fun onWifiConnected(ssid: String, ipAddress: String) {
            Log.d(TAG, "WiFi connected: $ssid, IP: $ipAddress")
            isConnecting.set(false)
            
            val connectionInfo = ConnectionInfo(
                mode = WifiConnectionMode.WIFI_AP_CLIENT,
                ssid = ssid,
                ipAddress = ipAddress
            )
            listener.onModeConnected(WifiConnectionMode.WIFI_AP_CLIENT, connectionInfo)
        }
        
        override fun onWifiDisconnected() {
            Log.d(TAG, "WiFi disconnected")
            if (currentMode == WifiConnectionMode.WIFI_AP_CLIENT) {
                listener.onModeDisconnected(WifiConnectionMode.WIFI_AP_CLIENT)
            }
        }
        
        override fun onWifiConnectionFailed(reason: Int) {
            Log.e(TAG, "WiFi connection failed: $reason")
            handleConnectionFailure(WifiConnectionMode.WIFI_AP_CLIENT, reason)
        }
        
        override fun onNetworkScanComplete(networks: List<android.net.wifi.ScanResult>) {
            Log.d(TAG, "Network scan completed: ${networks.size} networks found")
        }
        
        override fun requestWifiInfo() {
            // 通过消息管理器请求WiFi信息
            requestWifiApInfo { _, _ -> }
        }
        
        override fun onWifiState(opened: Boolean) {
            Log.d(TAG, "WiFi state changed: $opened")
        }
    }
    
    /**
     * P2P监听器（适配现有SoftP2pListener）
     */
    private val p2pListener = object : SoftP2pListener {
        override fun onP2pConnected(address: String) {
            Log.d(TAG, "P2P connected: $address")
            isConnecting.set(false)
            
            val connectionInfo = ConnectionInfo(
                mode = WifiConnectionMode.WIFI_P2P,
                peerAddress = address
            )
            listener.onModeConnected(WifiConnectionMode.WIFI_P2P, connectionInfo)
        }
        
        override fun onP2pDisconnected() {
            Log.d(TAG, "P2P disconnected")
            if (currentMode == WifiConnectionMode.WIFI_P2P) {
                listener.onModeDisconnected(WifiConnectionMode.WIFI_P2P)
            }
        }
        
        override fun onP2pConnectionFailed(reason: Int) {
            Log.e(TAG, "P2P connection failed: $reason")
            handleConnectionFailure(WifiConnectionMode.WIFI_P2P, reason)
        }
        
        // 其他P2P回调方法...
    }
    
    /**
     * 处理连接失败
     */
    private fun handleConnectionFailure(mode: WifiConnectionMode, reason: Int) {
        isConnecting.set(false)
        currentMode = null
        connectionJob?.cancel()
        
        Log.e(TAG, "Connection failed for mode $mode, reason: $reason")
        listener.onModeConnectionFailed(mode, reason)
    }
    
    /**
     * 请求仪表端WiFi AP信息
     */
    private fun requestWifiApInfo(callback: (String, String) -> Unit) {
        // 实现BLE消息发送，请求仪表端WiFi热点信息
        // 具体实现参考现有的消息管理器
        Log.d(TAG, "Requesting WiFi AP info from instrument")
        
        // 模拟异步回调
        CoroutineScope(Dispatchers.Main).launch {
            delay(100) // 模拟网络延迟
            // 实际实现中应该通过BLE消息获取
            callback("InstrumentAP_12345", "password123")
        }
    }
    
    /**
     * 销毁资源
     */
    fun destroy() {
        disconnect()
        connectionJob?.cancel()
        wifiClientManager.destroy()
        p2pManager.destroy()
    }
}

/**
 * 连接模式枚举
 */
enum class WifiConnectionMode {
    WIFI_AP_CLIENT,  // AP客户端模式
    WIFI_P2P         // P2P直连模式
}

/**
 * 连接信息数据类
 */
data class ConnectionInfo(
    val mode: WifiConnectionMode,
    val ssid: String = "",
    val ipAddress: String = "",
    val peerAddress: String = "",
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 连接模式管理器监听接口
 */
interface ConnectionModeManagerListener {
    /**
     * 模式连接中
     */
    fun onModeConnecting(mode: WifiConnectionMode)
    
    /**
     * 模式连接成功
     */
    fun onModeConnected(mode: WifiConnectionMode, connectionInfo: ConnectionInfo)
    
    /**
     * 模式连接断开
     */
    fun onModeDisconnected(mode: WifiConnectionMode)
    
    /**
     * 模式连接失败
     */
    fun onModeConnectionFailed(mode: WifiConnectionMode, reason: Int)
}
```

## 使用示例

### 基本用法

```kotlin
class MyApplication {
    private val riderService = RiderService()
    
    init {
        // 设置WiFi连接状态回调
        riderService.setWifiConnectionCallback(wifiConnectionCallback)
    }
    
    private val wifiConnectionCallback = object : WifiConnectionCallback {
        override fun onWifiConnecting(mode: WifiConnectionMode) {
            Log.d(TAG, "Connecting with mode: $mode")
            // 更新UI状态
        }
        
        override fun onWifiConnected(mode: WifiConnectionMode, connectionInfo: ConnectionInfo) {
            Log.d(TAG, "Connected successfully: $mode")
            // 连接成功，可以开始数据传输
        }
        
        override fun onWifiDisconnected(mode: WifiConnectionMode) {
            Log.d(TAG, "Disconnected from mode: $mode")
            // 处理断开连接
        }
        
        override fun onWifiConnectionFailed(mode: WifiConnectionMode, reason: Int) {
            Log.e(TAG, "Connection failed: $mode, reason: $reason")
            
            // APP端决定是否切换模式
            when (mode) {
                WifiConnectionMode.WIFI_AP_CLIENT -> {
                    if (shouldFallbackToP2P(reason)) {
                        // 切换到P2P模式
                        riderService.connectWithWifiMode(WifiConnectionMode.WIFI_P2P)
                    } else {
                        // 显示错误提示
                        showConnectionError("AP模式连接失败")
                    }
                }
                WifiConnectionMode.WIFI_P2P -> {
                    // P2P也失败了，显示错误
                    showConnectionError("所有连接模式都失败")
                }
            }
        }
    }
    
    /**
     * 开始连接（APP端决定模式）
     */
    fun startConnection() {
        // APP端根据业务逻辑决定使用哪种模式
        val preferredMode = getPreferredConnectionMode()
        
        if (riderService.isWifiModeAvailable(preferredMode)) {
            riderService.connectWithWifiMode(preferredMode)
        } else {
            // 尝试其他可用模式
            val fallbackMode = when (preferredMode) {
                WifiConnectionMode.WIFI_AP_CLIENT -> WifiConnectionMode.WIFI_P2P
                WifiConnectionMode.WIFI_P2P -> WifiConnectionMode.WIFI_AP_CLIENT
            }
            
            if (riderService.isWifiModeAvailable(fallbackMode)) {
                riderService.connectWithWifiMode(fallbackMode)
            } else {
                showConnectionError("没有可用的连接模式")
            }
        }
    }
    
    /**
     * 强制使用指定模式连接
     */
    fun connectWithSpecificMode(mode: WifiConnectionMode) {
        if (riderService.isWifiModeAvailable(mode)) {
            riderService.connectWithWifiMode(mode)
        } else {
            showConnectionError("指定的连接模式不可用")
        }
    }
    
    /**
     * 断开WiFi连接
     */
    fun disconnect() {
        riderService.disconnectWifi()
    }
    
    /**
     * APP端业务逻辑决定连接模式
     */
    private fun getPreferredConnectionMode(): WifiConnectionMode {
        // 这里可以根据用户设置、历史连接记录、设备特性等决定
        return when {
            // 例如：某些特定设备优先使用P2P
            isSpecialDevice() -> WifiConnectionMode.WIFI_P2P
            // 例如：用户设置了偏好模式
            hasUserPreference() -> getUserPreferredMode()
            // 默认使用AP模式
            else -> WifiConnectionMode.WIFI_AP_CLIENT
        }
    }
    
    private fun shouldFallbackToP2P(reason: Int): Boolean {
        // 根据失败原因决定是否回退
        return when (reason) {
            -2 -> true  // 权限问题，尝试P2P
            -3 -> true  // WiFi连接问题，尝试P2P
            else -> false
        }
    }
    
    private fun showConnectionError(message: String) {
        // 显示错误提示给用户
        Log.e(TAG, message)
    }
}
```

### APP端完整使用示例

```kotlin
/**
 * APP端完整的连接管理示例
 * 展示如何根据业务逻辑控制连接模式
 */
class InstrumentConnectionManager {
    private val riderService = RiderService()
    private var retryCount = 0
    private val maxRetries = 2
    private val preferences = getSharedPreferences("connection_prefs", Context.MODE_PRIVATE)
    
    init {
        riderService.setWifiConnectionCallback(wifiConnectionCallback)
    }
    
    /**
     * 智能连接：根据历史记录和设备特性选择模式
     */
    fun startSmartConnection() {
        val preferredMode = determineOptimalMode()
        Log.d(TAG, "Starting smart connection with preferred mode: $preferredMode")
        
        if (riderService.isWifiModeAvailable(preferredMode)) {
            riderService.connectWithWifiMode(preferredMode)
        } else {
            // 尝试备用模式
            val fallbackMode = getFallbackMode(preferredMode)
            if (riderService.isWifiModeAvailable(fallbackMode)) {
                Log.d(TAG, "Preferred mode unavailable, using fallback: $fallbackMode")
                riderService.connectWithWifiMode(fallbackMode)
            } else {
                onAllModesFailed()
            }
        }
    }
    
    /**
     * 强制使用指定模式（用户手动选择）
     */
    fun connectWithUserSelectedMode(mode: WifiConnectionMode) {
        Log.d(TAG, "User selected connection mode: $mode")
        retryCount = 0 // 重置重试计数
        
        if (riderService.isWifiModeAvailable(mode)) {
            riderService.connectWithWifiMode(mode)
        } else {
            showModeUnavailableError(mode)
        }
    }
    
    /**
     * 断开连接
     */
    fun disconnect() {
        riderService.disconnectWifi()
        retryCount = 0
    }
    
    /**
     * 根据多种因素确定最优连接模式
     */
    private fun determineOptimalMode(): WifiConnectionMode {
        // 1. 检查用户明确设置的偏好
        val userPreference = getUserPreference()
        if (userPreference != null) {
            Log.d(TAG, "Using user preference: $userPreference")
            return userPreference
        }
        
        // 2. 检查历史成功记录
        val lastSuccessfulMode = getLastSuccessfulMode()
        if (lastSuccessfulMode != null) {
            Log.d(TAG, "Using last successful mode: $lastSuccessfulMode")
            return lastSuccessfulMode
        }
        
        // 3. 根据设备特性决定
        val deviceOptimalMode = getDeviceOptimalMode()
        if (deviceOptimalMode != null) {
            Log.d(TAG, "Using device optimal mode: $deviceOptimalMode")
            return deviceOptimalMode
        }
        
        // 4. 默认使用AP模式
        Log.d(TAG, "Using default AP mode")
        return WifiConnectionMode.WIFI_AP_CLIENT
    }
    
    private val wifiConnectionCallback = object : WifiConnectionCallback {
        override fun onWifiConnecting(mode: WifiConnectionMode) {
            Log.d(TAG, "Connecting with mode: $mode")
            updateConnectionStatus("正在连接...")
        }
        
        override fun onWifiConnected(mode: WifiConnectionMode, connectionInfo: ConnectionInfo) {
            Log.d(TAG, "Connected successfully with mode: $mode")
            retryCount = 0
            
            // 保存成功的连接模式
            saveLastSuccessfulMode(mode)
            
            // 更新UI状态
            updateConnectionStatus("连接成功")
            
            // 通知业务层连接成功
            notifyConnectionSuccess(mode, connectionInfo)
        }
        
        override fun onWifiDisconnected(mode: WifiConnectionMode) {
            Log.d(TAG, "Disconnected from mode: $mode")
            updateConnectionStatus("连接断开")
            
            // 根据业务需求决定是否自动重连
            if (shouldAutoReconnect()) {
                Log.d(TAG, "Starting auto reconnection...")
                startSmartConnection()
            }
        }
        
        override fun onWifiConnectionFailed(mode: WifiConnectionMode, reason: Int) {
            Log.e(TAG, "Connection failed: mode=$mode, reason=$reason")
            retryCount++
            
            if (retryCount <= maxRetries) {
                // 尝试切换到其他模式
                val nextMode = getNextModeToTry(mode)
                if (nextMode != null && riderService.isWifiModeAvailable(nextMode)) {
                    Log.d(TAG, "Trying fallback mode: $nextMode (attempt $retryCount)")
                    riderService.connectWithWifiMode(nextMode)
                } else {
                    Log.w(TAG, "No fallback mode available")
                    onAllModesFailed()
                }
            } else {
                Log.e(TAG, "Max retry attempts reached")
                onAllModesFailed()
            }
        }
    }
    
    private fun getNextModeToTry(failedMode: WifiConnectionMode): WifiConnectionMode? {
        return when (failedMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> WifiConnectionMode.WIFI_P2P
            WifiConnectionMode.WIFI_P2P -> WifiConnectionMode.WIFI_AP_CLIENT
        }
    }
    
    private fun getFallbackMode(primaryMode: WifiConnectionMode): WifiConnectionMode {
        return when (primaryMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> WifiConnectionMode.WIFI_P2P
            WifiConnectionMode.WIFI_P2P -> WifiConnectionMode.WIFI_AP_CLIENT
        }
    }
    
    private fun onAllModesFailed() {
        Log.e(TAG, "All connection modes failed")
        updateConnectionStatus("连接失败")
        showConnectionFailedDialog()
    }
    
    private fun saveLastSuccessfulMode(mode: WifiConnectionMode) {
        preferences.edit()
            .putString("last_successful_mode", mode.name)
            .putLong("last_success_time", System.currentTimeMillis())
            .apply()
    }
    
    private fun getLastSuccessfulMode(): WifiConnectionMode? {
        val modeName = preferences.getString("last_successful_mode", null)
        val lastSuccessTime = preferences.getLong("last_success_time", 0)
        
        // 只使用最近24小时内成功的模式
        return if (modeName != null && 
                   System.currentTimeMillis() - lastSuccessTime < 24 * 60 * 60 * 1000) {
            try {
                WifiConnectionMode.valueOf(modeName)
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }
    
    private fun getUserPreference(): WifiConnectionMode? {
        val prefName = preferences.getString("user_preferred_mode", null)
        return try {
            if (prefName != null) WifiConnectionMode.valueOf(prefName) else null
        } catch (e: Exception) {
            null
        }
    }
    
    private fun getDeviceOptimalMode(): WifiConnectionMode? {
        // 根据设备制造商、型号等决定最优模式
        return when {
            isHuaweiDevice() -> WifiConnectionMode.WIFI_P2P // 华为设备P2P兼容性更好
            isXiaomiDevice() -> WifiConnectionMode.WIFI_AP_CLIENT // 小米设备AP模式更稳定
            else -> null
        }
    }
    
    private fun shouldAutoReconnect(): Boolean {
        // 根据业务逻辑决定是否自动重连
        return preferences.getBoolean("auto_reconnect", true)
    }
    
    // 业务相关的辅助方法
    private fun updateConnectionStatus(status: String) {
        // 更新UI状态
    }
    
    private fun notifyConnectionSuccess(mode: WifiConnectionMode, info: ConnectionInfo) {
        // 通知业务层连接成功
    }
    
    private fun showConnectionFailedDialog() {
        // 显示连接失败对话框
    }
    
    private fun showModeUnavailableError(mode: WifiConnectionMode) {
        // 显示模式不可用错误
    }
}