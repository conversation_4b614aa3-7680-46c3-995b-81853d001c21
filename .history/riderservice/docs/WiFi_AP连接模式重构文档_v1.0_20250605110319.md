# WiFi双模式连接支持技术文档

## 1. 概述

本文档描述了RiderService SDK支持WiFi P2P和WiFi AP双连接模式的技术实现方案，默认使用WiFi AP（接入点）模式，P2P模式作为备用方案。

### 1.1 背景
当前系统仅使用WiFi P2P模式进行设备间的直接连接，但由于兼容性和稳定性问题，需要增加WiFi AP模式支持。为确保最大兼容性，系统将同时支持两种模式。

### 1.2 架构说明
**双模式架构**：

**模式一：WiFi AP模式（默认）**
- **仪表端（车机）**：作为WiFi热点（AP）+ TCP客户端
- **Android手机端**：作为WiFi客户端，连接到仪表热点 + TCP服务器
- **连接流程**：BLE协商模式 → 获取热点信息 → 手机连接热点 → 手机通过BLE发送IP → 建立TCP连接

**模式二：WiFi P2P模式（备用）**
- **仪表端（车机）**：作为P2P GroupOwner + TCP客户端
- **Android手机端**：作为P2P客户端 + TCP服务器
- **连接流程**：BLE协商模式 → P2P设备发现 → 建立P2P连接 → 建立TCP连接

**模式选择策略**：
1. 默认使用AP模式
2. 提供API支持上层主动切换模式
3. 连接失败时报告给上层，由上层决定后续操作
4. 支持配置默认连接模式偏好

### 1.3 目标
- 在现有WiFi P2P基础上增加WiFi AP客户端模式支持
- 保持现有TCP服务器架构不变
- 实现清晰的双模式API接口
- 通过BLE协商和传递连接信息
- 确保连接的稳定性和兼容性
- 支持Android 6.0-15(API 23-35)的最新实践
- 完全向后兼容，P2P模式作为可靠备用方案
- 增强并发安全性和资源管理
- 提供统一的连接管理接口

## 2. 现状分析

### 2.1 当前架构
- **核心类**: `SoftP2pManager.kt` - 负责WiFi P2P连接管理
- **协议支持**: `WifiMode.WIFI_P2P` 和 `WifiMode.WIFI_AP` （仅声明，未实现）
- **传输层**: `TcpConnection.kt` - TCP服务器监听30512端口
- **连接管理**: `DisplayNaviManager.kt` - 显示和导航管理
- **状态管理**: 复杂的P2P连接状态机（11个状态）

### 2.1.1 现有P2P模式优缺点分析
**优点**：
- 成熟稳定，已在生产环境验证
- 无需仪表端创建WiFi热点
- 设备发现机制完善

**缺点**：
- 部分Android设备兼容性问题
- 连接建立时间较长
- 某些厂商ROM限制较多

### 2.2 现有WiFi P2P流程
1. 通过BLE获取仪表WiFi信息
2. 启动WiFi P2P设备发现
3. 连接到指定MAC地址的P2P设备
4. 建立TCP连接（端口30512）
5. 开始数据传输

### 2.3 现有状态机分析
当前`SoftP2pManager`使用复杂的11状态自动连接状态机：
- `AUTOLINK_STATION_WIFI_START` (0) - 进入app时，未获取到其他状态
- `AUTOLINK_STATION_NO_CONNECT` (1) - wifi p2p未连接
- `AUTOLINK_STATION_ALREADY_CONNECTED` (2) - wifi p2p已经连接
- `AUTOLINK_STATION_SEARCH_WIFI` (3) - 搜索wifi，持续10s
- `AUTOLINK_STATION_SEARCH_FAILED` (4) - 搜索wifi重试失败
- `AUTOLINK_STATION_SEARCH_SUCCESS` (5) - wifi搜索成功
- `AUTOLINK_STATION_CONNECT_SUCCESS` (6) - wifi连接成功
- `AUTOLINK_STATION_CONNECT_FAILED` (7) - wifi连接失败，重试3次
- `AUTOLINK_STATION_RECONNECT_FAILED` (8) - wifi连接重试失败
- `AUTOLINK_STATION_AUTOLINK_RESTART` (9) - 已连接状态下的Autolink重启
- `AUTOLINK_STATION_P2P_DEVICE_ERROR` (10) - p2p对等设备错误
- `AUTOLINK_STATION_WIFI_DISABLE` (11) - wifi关闭

新架构需要保持类似的状态管理复杂度以确保连接稳定性。

## 3. 详细架构设计

### 3.1 整体系统架构

```mermaid
graph TB
    subgraph "仪表端 (车机)"
        I1[WiFi热点AP]
        I2[WiFi P2P GroupOwner]
        I3[TCP客户端]
        I4[BLE服务端]
        I5[模式协商逻辑]
        I6[数据处理层]
        
        I4 --> I5
        I5 --> I1
        I5 --> I2
        I1 --> I3
        I2 --> I3
        I3 --> I6
    end
    
    subgraph "Android手机端"
        A1[BLE客户端]
        A2[ConnectionModeManager]
        A3[WiFiClientManager]
        A4[SoftP2pManager]
        A5[TCP服务器:30512]
        A6[数据传输层]
        A7[RiderService API]
        
        A1 --> A2
        A2 --> A3
        A2 --> A4
        A3 --> A5
        A4 --> A5
        A5 --> A6
        A6 --> A7
    end
    
    I4 -.BLE连接.-> A1
    I1 -.WiFi AP连接.-> A3
    I2 -.WiFi P2P连接.-> A4
    I3 -.TCP连接.-> A5
    
    style A2 fill:#ffd54f
    style A3 fill:#f3e5f5
    style A4 fill:#e1f5fe
    style I5 fill:#ffd54f
    style A5 fill:#e8f5e8
```

### 3.2 核心组件架构

```mermaid
graph LR
    subgraph "连接管理层"
        CM[ConnectionManager]
        DNM[DisplayNaviManager]
        CMM[ConnectionModeManager]
    end
    
    subgraph "网络层"
        WCM[WiFiClientManager]
        SPM[SoftP2pManager]
        TCS[TcpServerConnection]
        NPH[NetworkPermissionHelper]
    end
    
    subgraph "协议层"
        RS[RiderService]
        MP[MessageProtocol]
        MN[ModeNegotiation]
    end
    
    subgraph "传输层"
        TT[TcpTransport]
        DC[DataConnection]
    end
    
    CM --> DNM
    DNM --> CMM
    CMM --> WCM
    CMM --> SPM
    WCM --> NPH
    WCM --> TCS
    SPM --> TCS
    TCS --> DC
    DC --> TT
    RS --> CM
    RS --> MP
    MP --> MN
    
    style CMM fill:#ffd54f
    style WCM fill:#e3f2fd
    style SPM fill:#e1f5fe
    style TCS fill:#e8f5e8
    style MN fill:#ffd54f
```

### 3.3 双模式连接建立完整流程

```mermaid
sequenceDiagram
    participant App as Android App
    participant CMM as ConnectionModeManager
    participant BLE as BLE Manager
    participant Instrument as 仪表端
    participant WiFi as WiFiClientManager
    participant P2P as SoftP2pManager
    participant TCP as TcpServerConnection
    
    Note over App,Instrument: 1. BLE连接和模式协商
    App->>BLE: 扫描并连接BLE设备
    BLE->>Instrument: 建立BLE连接
    App->>CMM: 开始连接流程
    CMM->>Instrument: 请求支持的WiFi模式
    Instrument->>CMM: 返回支持的模式(AP+P2P)
    CMM->>CMM: 选择默认AP模式
    
    Note over App,Instrument: 2. 尝试AP模式连接
    CMM->>Instrument: 请求WiFi热点信息(AP模式)
    Instrument->>CMM: 返回热点信息(SSID,密码)
    CMM->>WiFi: connectToWifi(ssid, password)
    
    alt AP模式连接成功
        WiFi->>WiFi: 检查权限和WiFi状态
        WiFi->>Instrument: 连接到WiFi热点
        Instrument->>WiFi: 分配IP地址
        WiFi->>CMM: onWifiConnected(ssid, ip)
        CMM->>TCP: 启动TCP服务器
        CMM->>Instrument: 通过BLE发送手机IP
        Instrument->>TCP: 连接到手机TCP服务器
        TCP->>CMM: onTcpConnected()
        
    else AP模式连接失败
        WiFi->>CMM: onWifiConnectionFailed()
        CMM->>CMM: 切换到P2P模式
        CMM->>Instrument: 请求P2P连接信息
        Instrument->>CMM: 返回P2P设备信息
        CMM->>P2P: startP2pConnection(deviceInfo)
        P2P->>P2P: 设备发现和连接
        P2P->>CMM: onP2pConnected()
        CMM->>TCP: 启动TCP服务器
        TCP->>CMM: onTcpConnected()
    end
    
    Note over App,Instrument: 3. 数据传输
    App->>Instrument: 发送控制指令
    Instrument->>App: 返回视频/音频数据
```

### 3.4 WiFi客户端状态机（增强版）

```mermaid
stateDiagram-v2
    [*] --> WIFI_CLIENT_START: 初始化
    
    WIFI_CLIENT_START --> CHECKING_PERMISSIONS: 检查权限
    CHECKING_PERMISSIONS --> PERMISSION_DENIED: 权限不足
    CHECKING_PERMISSIONS --> WIFI_STATE_CHECK: 权限充足
    
    WIFI_STATE_CHECK --> WIFI_DISABLED: WiFi未开启
    WIFI_STATE_CHECK --> SCANNING: WiFi已开启
    
    SCANNING --> NETWORK_FOUND: 找到目标网络
    SCANNING --> SCAN_FAILED: 扫描失败
    SCANNING --> SCAN_TIMEOUT: 扫描超时
    
    NETWORK_FOUND --> CONNECTING: 开始连接
    CONNECTING --> CONNECTED: 连接成功
    CONNECTING --> CONNECT_FAILED: 连接失败
    CONNECTING --> CONNECT_TIMEOUT: 连接超时
    
    CONNECTED --> VALIDATING_CONNECTION: 验证连接
    VALIDATING_CONNECTION --> CONNECTION_VALIDATED: 连接有效
    VALIDATING_CONNECTION --> CONNECTION_INVALID: 连接无效
    
    CONNECTION_VALIDATED --> DISCONNECTED: 网络断开
    CONNECTION_VALIDATED --> [*]: 主动断开
    
    DISCONNECTED --> RECONNECTING: 自动重连
    RECONNECTING --> SCANNING: 重连扫描
    RECONNECTING --> RECONNECT_FAILED: 重连失败
    
    CONNECT_FAILED --> RECONNECTING: 重试连接
    SCAN_FAILED --> RECONNECTING: 重试扫描
    SCAN_TIMEOUT --> RECONNECTING: 重试扫描
    CONNECT_TIMEOUT --> RECONNECTING: 重试连接
    CONNECTION_INVALID --> RECONNECTING: 重新连接
    
    RECONNECT_FAILED --> [*]: 彻底失败
    PERMISSION_DENIED --> [*]: 权限失败
    WIFI_DISABLED --> [*]: WiFi未启用
    
    note right of RECONNECTING: 指数退避策略<br/>最多重试5次
    note right of VALIDATING_CONNECTION: 验证IP获取<br/>和网络可达性
    note right of CHECKING_PERMISSIONS: 检查位置权限<br/>WiFi权限等
```

### 3.5 错误处理和恢复流程

```mermaid
flowchart TD
    A["连接异常"] --> B{"异常类型"}
    
    B -->|WiFi断开| C["启动重连机制"]
    B -->|TCP断开| D["重新建立TCP连接"]
    B -->|权限错误| E["请求用户授权"]
    B -->|网络不可用| F["等待网络恢复"]
    
    C --> G{"重连次数检查"}
    G -->|< 5次| H["指数退避延迟"]
    G -->|≥ 5次| I["重连失败"]
    
    H --> J["重新连接WiFi"]
    J --> K{"连接结果"}
    K -->|成功| L["恢复正常"]
    K -->|失败| C
    
    D --> M["获取仪表IP"]
    M --> N["重新建立TCP连接"]
    N --> O{"TCP连接结果"}
    O -->|成功| L
    O -->|失败| P["报告连接错误"]
    
    E --> Q["显示权限请求"]
    Q --> R{"用户授权"}
    R -->|同意| S["重新开始连接"]
    R -->|拒绝| T["功能不可用"]
    
    F --> U["监听网络状态"]
    U --> V{"网络恢复"}
    V -->|是| S
    V -->|否| F
    
    I --> P
    L --> Z["连接正常"]
    P --> Y["显示错误信息"]
    T --> Y
    Y --> End1["结束"]
    Z --> End2["结束"]
    
    style L fill:#c8e6c9
    style P fill:#ffcdd2
    style T fill:#ffcdd2
```

### 3.6 Android版本兼容性架构

```mermaid
graph TD
    A[WiFi连接请求] --> B{Android版本检查}
    
    B -->|API ≥ 29 Android 10+| C[现代化API路径]
    B -->|API 23-28 Android 6.0-9| D[传统API路径]
    B -->|API < 23| E[不支持/P2P回退]
    
    subgraph "现代化API (Android 10+)"
        C --> C1[WifiNetworkSpecifier]
        C1 --> C2[NetworkRequest.Builder]
        C2 --> C3[ConnectivityManager.requestNetwork]
        C3 --> C4[NetworkCallback监听]
    end
    
    subgraph "传统API (Android 6.0-9)"
        D --> D1[权限检查]
        D1 --> D2[WiFi网络扫描]
        D2 --> D3[WifiConfiguration配置]
        D3 --> D4[WifiManager连接]
        D4 --> D5[BroadcastReceiver监听]
    end
    
    subgraph "权限管理"
        F[ACCESS_FINE_LOCATION]
        G[CHANGE_WIFI_STATE]
        H[NEARBY_WIFI_DEVICES API 33+]
        I[ACCESS_BACKGROUND_LOCATION API 29+]
        J[PARTIAL_WAKE_LOCK]
    end
    
    C4 --> I[连接成功回调]
    D5 --> I
    E --> J[P2P模式回退或不支持提示]
    I --> K[建立TCP连接]
    
    D1 --> F
    D1 --> G
    C --> H
    
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#ffebee
```

## 4. 双模式支持实现方案

### 4.1 整体架构设计

基于以上详细分析，我们的双模式支持方案采用分层架构，确保各组件职责清晰，易于维护和扩展，同时保持向后兼容性。

### 4.2 核心组件设计

#### 4.2.1 新建 `ConnectionModeManager.kt`
核心连接模式管理器，统一管理P2P和AP两种连接模式：

**主要功能:**
- 连接模式选择和切换
- 模式间的状态同步
- 自动回退机制
- 连接偏好记忆

**核心接口:**
```kotlin
interface ConnectionModeManagerListener {
    fun onModeSelected(mode: WifiConnectionMode)
    fun onModeConnecting(mode: WifiConnectionMode)
    fun onModeConnected(mode: WifiConnectionMode, connectionInfo: ConnectionInfo)
    fun onModeDisconnected(mode: WifiConnectionMode)
    fun onModeConnectionFailed(mode: WifiConnectionMode, reason: Int)
    fun onModeSwitching(fromMode: WifiConnectionMode, toMode: WifiConnectionMode)
}

enum class WifiConnectionMode {
    WIFI_AP_CLIENT,  // AP客户端模式
    WIFI_P2P,        // P2P直连模式
    AUTO             // 自动选择模式
}
```

#### 4.2.2 新建 `WiFiClientManager.kt`
实现WiFi AP客户端连接，与现有`SoftP2pManager.kt`并行工作：

**主要功能:**
- WiFi网络扫描和连接
- 网络状态监控
- 自动重连机制
- 跨版本兼容性处理

**核心接口:**
```kotlin
interface WiFiClientManagerListener {
    fun onWifiConnecting(ssid: String)
    fun onWifiConnected(ssid: String, ipAddress: String)
    fun onWifiDisconnected()
    fun onWifiConnectionFailed(reason: Int)
    fun onNetworkScanComplete(networks: List<ScanResult>)
    fun requestWifiInfo()
    fun onWifiState(opened: Boolean)
}
```

#### 4.2.3 改进现有 `SoftP2pManager.kt`
保留并优化现有P2P连接管理器：

**主要改进:**
- 增加与ConnectionModeManager的接口适配
- 优化状态回调机制
- 改进错误处理和重连逻辑
- 保持现有功能完全兼容

#### 4.2.4 统一状态管理
双模式下的统一状态定义：

```kotlin
enum class WifiClientState {
    WIFI_CLIENT_START,      // 初始化状态
    CHECKING_PERMISSIONS,   // 检查权限
    PERMISSION_DENIED,      // 权限被拒绝
    WIFI_STATE_CHECK,       // 检查WiFi状态
    WIFI_DISABLED,          // WiFi未开启
    SCANNING,               // 扫描网络中
    NETWORK_FOUND,          // 找到目标网络
    SCAN_FAILED,            // 扫描失败
    SCAN_TIMEOUT,           // 扫描超时
    CONNECTING,             // 连接中
    CONNECTED,              // 已连接
    VALIDATING_CONNECTION,  // 验证连接
    CONNECTION_VALIDATED,   // 连接验证通过
    CONNECTION_INVALID,     // 连接无效
    DISCONNECTED,           // 已断开
    CONNECT_FAILED,         // 连接失败
    CONNECT_TIMEOUT,        // 连接超时
    RECONNECTING,           // 重连中
    RECONNECT_FAILED        // 重连失败
}
```

## 5. 详细实现方案

### 5.1 Android版本兼容性处理

#### 5.1.1 Android 10+ (API 29+) 新方式
使用 `WifiNetworkSpecifier` 和 `NetworkRequest`：

```kotlin
@RequiresApi(Build.VERSION_CODES.Q)
private fun connectToWifiModern(ssid: String, password: String) {
    val wifiNetworkSpecifier = WifiNetworkSpecifier.Builder()
        .setSsid(ssid)
        .setWpa2Passphrase(password)
        .build()

    val networkRequest = NetworkRequest.Builder()
        .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
        .setNetworkSpecifier(wifiNetworkSpecifier)
        .build()

    connectivityManager.requestNetwork(networkRequest, networkCallback)
}
```

#### 5.1.2 Android 6.0-9 (API 23-28) 兼容方式
使用 `WifiConfiguration` 和 `WifiManager`：

```kotlin
@SuppressLint("MissingPermission")
private fun connectToWifiLegacy(ssid: String, password: String) {
    val wifiConfig = WifiConfiguration().apply {
        SSID = "\"$ssid\""
        preSharedKey = "\"$password\""
        allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
        allowedProtocols.set(WifiConfiguration.Protocol.RSN)
        allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
        allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
    }

    val networkId = wifiManager.addNetwork(wifiConfig)
    if (networkId != -1) {
        wifiManager.disconnect()
        wifiManager.enableNetwork(networkId, true)
        wifiManager.reconnect()
    }
}
```

### 5.2 权限管理

#### 5.2.1 必需权限
```xml
<!-- WiFi基础权限 -->
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />

<!-- 位置权限（WiFi扫描需要） -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

<!-- Android 13+ 的附近WiFi设备权限 -->
<uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" 
    android:usesPermissionFlags="neverForLocation"
    tools:targetApi="s" />

<!-- Android 10+ 后台位置权限（如需要） -->
<uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

<!-- 唤醒锁权限（用于保持连接稳定性） -->
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

#### 5.2.2 动态权限申请
```kotlin
private fun checkAndRequestPermissions(): Boolean {
    val requiredPermissions = mutableListOf<String>()
    
    if (ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) 
        != PackageManager.PERMISSION_GRANTED) {
        requiredPermissions.add(Manifest.permission.ACCESS_FINE_LOCATION)
    }
    
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.NEARBY_WIFI_DEVICES)
            != PackageManager.PERMISSION_GRANTED) {
            requiredPermissions.add(Manifest.permission.NEARBY_WIFI_DEVICES)
        }
    }
    
    return requiredPermissions.isEmpty()
}
```

### 5.3 网络扫描和连接

#### 5.3.1 WiFi网络扫描
```kotlin
@SuppressLint("MissingPermission")
private fun startWifiScan() {
    if (!wifiManager.isWifiEnabled) {
        listener.onWifiState(false)
        return
    }
    
    updateState(WifiClientState.SCANNING)
    
    val scanReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action == WifiManager.SCAN_RESULTS_AVAILABLE_ACTION) {
                val scanResults = wifiManager.scanResults
                listener.onNetworkScanComplete(scanResults)
                context.unregisterReceiver(this)
            }
        }
    }
    
    context.registerReceiver(scanReceiver, IntentFilter(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION))
    wifiManager.startScan()
}
```

#### 5.3.2 目标网络连接
```kotlin
fun connectToTargetNetwork(ssid: String, password: String) {
    Log.d(TAG, "Connecting to target network: $ssid")
    
    if (currentState == WifiClientState.CONNECTED && getCurrentSsid() == ssid) {
        Log.d(TAG, "Already connected to target network")
        return
    }
    
    updateState(WifiClientState.CONNECTING)
    
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        connectToWifiModern(ssid, password)
    } else {
        connectToWifiLegacy(ssid, password)
    }
}
```

### 5.4 网络状态监控

#### 5.4.1 NetworkCallback实现
```kotlin
private val networkCallback = object : ConnectivityManager.NetworkCallback() {
    override fun onAvailable(network: Network) {
        super.onAvailable(network)
        Log.d(TAG, "Network available: $network")
        
        // 绑定应用到此网络（Android 10+）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            connectivityManager.bindProcessToNetwork(network)
        }
        
        val ipAddress = getNetworkIpAddress(network)
        updateState(WifiClientState.CONNECTED)
        listener.onWifiConnected(targetSsid, ipAddress)
    }
    
    override fun onLost(network: Network) {
        super.onLost(network)
        Log.d(TAG, "Network lost: $network")
        updateState(WifiClientState.DISCONNECTED)
        listener.onWifiDisconnected()
        
        // 启动重连
        startReconnection()
    }
    
    override fun onUnavailable() {
        super.onUnavailable()
        Log.d(TAG, "Network unavailable")
        updateState(WifiClientState.CONNECTION_FAILED)
        listener.onWifiConnectionFailed(-1)
    }
}
```

#### 5.4.2 IP地址获取
```kotlin
private fun getNetworkIpAddress(network: Network): String {
    return try {
        val linkProperties = connectivityManager.getLinkProperties(network)
        linkProperties?.linkAddresses?.find { 
            it.address is Inet4Address 
        }?.address?.hostAddress ?: ""
    } catch (e: Exception) {
        Log.e(TAG, "Error getting IP address", e)
        ""
    }
}
```

### 5.5 自动重连机制

#### 5.5.1 重连策略
```kotlin
private fun startReconnection() {
    if (currentState == WifiClientState.RECONNECTING) {
        return
    }
    
    updateState(WifiClientState.RECONNECTING)
    reconnectionAttempts++
    
    val delay = calculateBackoffDelay(reconnectionAttempts)
    
    reconnectionHandler.postDelayed({
        if (reconnectionAttempts <= MAX_RECONNECTION_ATTEMPTS) {
            Log.d(TAG, "Reconnection attempt: $reconnectionAttempts")
            connectToTargetNetwork(targetSsid, targetPassword)
        } else {
            Log.e(TAG, "Max reconnection attempts reached")
            updateState(WifiClientState.CONNECTION_FAILED)
            listener.onWifiConnectionFailed(RECONNECTION_FAILED)
        }
    }, delay)
}

private fun calculateBackoffDelay(attempt: Int): Long {
    // 指数退避：1s, 2s, 4s, 8s, 16s
    return minOf(1000L * (1 shl (attempt - 1)), 16000L)
}
```

## 6. 协议层修改

### 6.1 更新WiFi请求协议
```kotlin
// ConnectionManager.kt
fun requestWifiInfo(isReset: Boolean = true) {
    Log.d(TAG, "requestWifiInfo ${_connectionStatus.value.btStatus}")
    if (_connectionStatus.value.btStatus is BleStatus.DeviceConnected) {
        ModuleInject.messageManager.queueOutgoing(
            RiderProtocol.NaviMessageId.MSG_WIFI_INFO_REQUEST_VALUE,
            RiderProtocol.WifiInfoRequest
                .newBuilder()
                .setWifiMode(RiderProtocol.WifiMode.WIFI_AP) // 改为AP模式
                .setIsResetWifi(isReset)
                .build()
        )
    }
}
```

### 6.2 处理仪表端热点信息
```kotlin
// RiderService.kt  
private fun handleWifiInfo(msg: ByteArray?) {
    Log.d("connect analysis:", "wifi info response (AP client mode)::${TimeUtils.getCurrentTimeStr()}")
    val wifiInfo = RiderProtocol.WifiInfoNotification.parseFrom(msg)
    
    // 连接到仪表端的WiFi热点
    val instrumentSsid = wifiInfo.name ?: ""
    val instrumentPassword = wifiInfo.password ?: ""
    
    if (instrumentSsid.isNotEmpty()) {
        mConnectionManager.connectToInstrumentWifi(
            getApplication().applicationContext,
            instrumentSsid,
            instrumentPassword
        )
    } else {
        Log.e(TAG, "Invalid WiFi info received from instrument")
    }
}
```

## 7. TCP连接调整

### 7.1 TCP服务器模式保持不变
基于用户补充的架构信息，手机端继续作为TCP服务器，仪表端作为TCP客户端连接：

```kotlin
// 保持现有的TCP服务器架构，主要修改连接通知逻辑
class TcpServerConnection : DataConnection() {
    private var serverSocket: ServerSocket? = null
    private var listenPort: Int = 30512
    
    override fun onStart() {
        startTcpServer()
    }
    
    private fun startTcpServer() {
        try {
            serverSocket = ServerSocket(listenPort)
            Log.d(TAG, "TCP server started on port $listenPort")
            
            // 启动服务器后，通过BLE通知仪表端手机的IP地址
            notifyInstrumentWithIpAddress()
            
            AcceptThread("TCP Server").start()
        } catch (e: IOException) {
            Log.e(TAG, "Failed to start TCP server", e)
            mCallback?.onDisconnected()
        }
    }
    
    private fun notifyInstrumentWithIpAddress() {
        val phoneIp = getPhoneIpAddress()
        if (phoneIp.isNotEmpty()) {
            // 通过BLE发送IP地址给仪表端
            sendIpAddressThroughBle(phoneIp, listenPort)
        }
    }
    
    private fun getPhoneIpAddress(): String {
        // 获取手机在WiFi网络中的IP地址
        return WiFiUtils.getWifiIpAddress()
    }
    
    private fun sendIpAddressThroughBle(ipAddress: String, port: Int) {
        // 通过BLE消息管理器发送IP地址给仪表端
        ModuleInject.messageManager.queueOutgoing(
            RiderProtocol.NaviMessageId.MSG_PHONE_IP_NOTIFICATION_VALUE,
            RiderProtocol.PhoneIpNotification
                .newBuilder()
                .setIpAddress(ipAddress)
                .setPort(port)
                .build()
        )
    }
    
    inner class AcceptThread(name: String) : Thread(name) {
        override fun run() {
            try {
                Log.d(TAG, "Waiting for instrument TCP client connection...")
                val clientSocket = serverSocket?.accept()
                
                if (clientSocket != null) {
                    Log.d(TAG, "Instrument connected from: ${clientSocket.remoteSocketAddress}")
                    val transport = TcpTransport(clientSocket)
                    mCallback?.onConnected(transport)
                }
            } catch (e: IOException) {
                Log.e(TAG, "Error accepting TCP connection", e)
                mCallback?.onDisconnected()
            }
        }
    }
}
```

### 7.2 协议层增加IP通知消息
需要在协议定义中增加手机IP地址通知消息：

```protobuf
// 新增消息类型
message PhoneIpNotification {
    string ip_address = 1;    // 手机IP地址
    int32 port = 2;           // TCP服务器端口
}
```

## 8. 具体文件修改清单

### 8.1 新增文件
- `ConnectionModeManager.kt` - 连接模式管理器
- `WiFiClientManager.kt` - WiFi AP客户端连接管理
- `WiFiClientManagerListener.kt` - WiFi客户端事件监听
- `NetworkPermissionHelper.kt` - 网络权限助手
- `WifiConnectionMode.kt` - 连接模式枚举和相关定义
- `ConnectionInfo.kt` - 连接信息数据类

### 8.2 修改文件
- `DisplayNaviManager.kt` - 集成ConnectionModeManager，支持双模式
- `ConnectionManager.kt` - 更新连接逻辑，增加模式协商
- `RiderService.kt` - 修改WiFi信息处理，支持模式选择
- `SoftP2pManager.kt` - 适配新的回调接口，保持功能兼容
- `TcpConnection.kt` - 增加IP地址通知逻辑
- `AndroidManifest.xml` - 添加网络权限
- `协议文件` - 增加模式协商和IP通知消息定义

### 8.3 无需删除文件
**注意**: 此方案不删除任何现有文件，完全保持向后兼容性
- `SoftP2pManager.kt` - 保留作为P2P模式管理器
- `SoftIP2pListener.kt` - 保留现有P2P接口

## 9. 测试方案

### 9.1 单元测试
- WiFi网络扫描功能
- 不同版本的连接兼容性
- 重连机制验证
- 权限处理测试

### 9.2 集成测试
- 完整连接流程测试
- 网络切换场景
- 异常恢复测试
- 性能和稳定性测试

### 9.3 兼容性测试
- Android 6.0-14版本测试
- 不同设备厂商兼容性
- 各种网络环境测试

## 10. 风险评估和缓解

### 10.1 技术风险
- **权限复杂性**: Android版本间权限差异大
  - **缓解**: 完善的权限检查和引导机制
- **网络API变化**: Android 10+网络API变化较大
  - **缓解**: 分版本实现，充分测试
- **自动连接限制**: 某些厂商限制自动WiFi连接
  - **缓解**: 提供手动连接指引

### 10.2 项目风险
- **仪表端稳定性**: 依赖仪表端热点稳定性
  - **缓解**: 强化重连和错误恢复机制
- **用户体验**: 可能需要用户手动授权
  - **缓解**: 优化权限申请流程和用户引导

### 10.3 新增风险评估
- **状态同步复杂性**: BLE与WiFi状态同步可能出现不一致
  - **缓解**: 设计统一的状态管理机制，确保状态同步
- **并发安全性**: 多线程操作WiFi连接可能导致竞态条件
  - **缓解**: 使用线程安全的状态管理和同步机制
- **资源泄漏**: 长期运行可能导致内存或连接泄漏
  - **缓解**: 完善的资源释放机制和生命周期管理
- **回滚复杂性**: 重构失败时回滚到P2P模式的复杂度
  - **缓解**: 设计渐进式替换策略，保持P2P模式的兼容性

## 11. 实施计划

### 11.1 第一阶段 (3-4天)
- 创建ConnectionModeManager基础框架
- 定义连接模式枚举和接口
- 设计模式选择和切换逻辑
- 网络权限助手类实现

### 11.2 第二阶段 (4-5天)
- 创建WiFiClientManager实现AP客户端连接
- 实现Android版本兼容处理
- 网络扫描和连接逻辑
- 状态监控和回调处理

### 11.3 第三阶段 (3-4天)
- 适配现有SoftP2pManager到新接口
- 实现双模式状态同步机制
- 自动重连和回退逻辑
- 单元测试实现

### 11.4 第四阶段 (3-4天)
- 集成ConnectionModeManager到DisplayNaviManager
- 协议层适配，增加模式协商
- TCP连接层适配
- 配置管理和偏好存储

### 11.5 第五阶段 (4-5天)
- 完整集成测试
- 双模式切换测试
- 兼容性和稳定性测试
- 错误处理和日志优化

### 11.6 第六阶段 (2-3天)
- 性能优化和内存管理
- 文档更新和代码审查
- 最终验收测试
- 部署准备

**总计预估**: 19-25天

**分阶段部署建议**:
1. **阶段1-3**: 核心框架开发，可与现有P2P模式并行运行
2. **阶段4-5**: 集成测试，逐步启用AP模式
3. **阶段6**: 全面部署，AP为默认模式，P2P为备用

## 12. 附录

### 12.1 相关Android API
- `ConnectivityManager.NetworkCallback` (Android 6.0+)
- `WifiNetworkSpecifier` (Android 10+)
- `NetworkRequest.Builder` (Android 6.0+)
- `WifiConfiguration` (已废弃但仍需兼容)

### 12.2 参考资料
- Android网络连接最佳实践
- WiFi连接跨版本兼容指南
- Android权限管理指南

### 12.3 重要技术注意事项
- **状态机复杂性**: 新的状态机需要保持与现有P2P状态机相似的复杂度，确保连接稳定性
- **日志记录**: 参考现有代码的详细日志记录策略，包含时间戳和连接分析日志
- **并发安全**: 使用AtomicBoolean等线程安全机制，参考现有isConnecting.set()的使用方式
- **资源管理**: 确保广播接收器、网络回调等资源的正确注册和释放
- **错误恢复**: 实现类似现有autolinkStationChange方法的严格状态转换逻辑 