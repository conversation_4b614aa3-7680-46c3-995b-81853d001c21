# RiderService SDK 重构工作量评估

> 版本：0.1
> 评估日期：{{YYYY-MM-DD}} (请替换为实际日期)
> 基于设计文档：`设计文档_投屏重构_v0.5_App发起仪表执行.md`

---

## 1. 引言

本文档旨在对 RiderService SDK 的核心重构（包括投屏逻辑调整、API 精炼、移除天气服务）以及将 Wi-Fi 连接方式从 P2P 改造为 AP 模式所需的工作量进行初步评估。所有工作量估算均以"人天"为单位，并假设开发人员具备相关的 Android、Kotlin 及网络编程经验。

---

## 2. 评估范围

本次评估主要包含以下几个方面：

1.  **核心投屏逻辑重构**:
    *   实现新的 `ProjectionManager`。
    *   调整 `RiderService` API 以符合"应用发起，仪表执行"模型。
    *   修改 `ConnectionManager` 以发送 `NaviMode` 指令并管理连接。
    *   确保底层模块 (`AutolinkControl`, `MediaProjectService`) 与新架构的集成。
2.  **Wi-Fi 连接模式改造 (P2P -> AP)**:
    *   修改网络发现、连接建立、IP 处理、Socket 通信等逻辑。
3.  **测试**:
    *   单元测试覆盖新旧模块的改动。
    *   集成测试验证端到端流程。
4.  **文档**:
    *   更新面向开发者的 API 文档和集成指南。

---

## 3. 工作量估算 (人天)

| 任务模块                      | 子任务                                                                 | 初步估算 (人天) | 备注                                                                 |
| ----------------------------- | ---------------------------------------------------------------------- | ------------- | -------------------------------------------------------------------- |
| **1. 核心投屏逻辑重构**       |                                                                        | **8 - 12**    |                                                                      |
|                               | 1.1 创建 `ProjectionManager.kt` 并实现核心状态机和资源管理               | 3 - 4         | 响应 `NaviMode`，处理 `MediaProjection` 和 `Display`                 |
|                               | 1.2 重构 `RiderService.kt` API (`requestNaviMode`, `stopCurrentProjection`) | 1 - 2         | 接口定义，参数传递，调用 `ProjectionManager` 和 `ConnectionManager`     |
|                               | 1.3 修改 `ConnectionManager.kt` (发送 `NaviMode`, Wi-Fi 状态通知)        | 1.5 - 2.5     | 重点是与仪表可靠通信 `NaviMode`                                        |
|                               | 1.4 调整 `AutolinkControl.kt` (或其调用者) 与 `ProjectionManager` 的交互 | 1.5 - 2       | 回调 `Display` 对象，通知状态                                           |
|                               | 1.5 集成 `MediaProjectService.java`                                      | 1 - 1.5       | 确保在 `ProjectionManager` 控制下正确启停                            |
| **2. Wi-Fi 连接模式改造 (P2P -> AP)** |                                                                | **5 - 8**     | 涉及较多网络底层细节，不确定性较高                                       |
|                               | 2.1 研究并实现 Android 端作为 Wi-Fi AP 的逻辑 (如果SDK需要创建AP)        | 2 - 3         | 或连接到仪表创建的 AP 的逻辑，取决于具体场景                           |
|                               | 2.2 修改设备发现、IP 地址获取、Socket 连接逻辑                           | 2 - 3         | AP 模式下的服务发现和连接建立                                          |
|                               | 2.3 处理 AP 模式下的连接稳定性、错误和重连机制                           | 1 - 2         |                                                                      |
| **3. 测试**                   |                                                                        | **6 - 10**    |                                                                      |
|                               | 3.1 单元测试 (ProjectionManager, RiderService API, ConnectionManager)  | 3 - 4         |                                                                      |
|                               | 3.2 集成测试 (模拟仪表或真实仪表联调，覆盖所有 NaviMode 和连接场景)        | 3 - 6         | 联调耗时可能较多                                                       |
| **4. 文档**                   |                                                                        | **1 - 2**     |                                                                      |
|                               | 4.1 更新 API 参考文档                                                    | 0.5 - 1       |                                                                      |
|                               | 4.2 更新集成指南和示例代码                                                 | 0.5 - 1       |                                                                      |
| ---                           | ---                                                                    | ---           | ---                                                                  |
| **总计估算**                  |                                                                        | **20 - 32** | **约 4 - 6.5 周 (按每周5工作日计算)**                                 |

---

## 4. 潜在风险与依赖

*   **仪表端配合**: Wi-Fi AP 模式的改造和 `NaviMode` 的处理强依赖仪表端的实现和配合。如果仪表端行为与预期不符，或存在 Bug，会显著影响调试和集成时间。
*   **`AutolinkControl` 内部逻辑**: 如果 `AutolinkControl` 内部非常复杂且文档不足，理解和修改其与 `ProjectionManager` 的交互可能比预期耗时。
*   **Android Wi-Fi AP 模式的复杂性**: 根据 SDK 是作为 AP 的创建者还是连接者，以及 Android 版本和设备差异，Wi-Fi AP 相关编程可能存在较多陷阱。
*   **测试环境与设备**: 稳定且接近真实用户场景的测试环境和设备对于高效测试至关重要。
*   **`NaviMode` 定义的清晰度**: 需要非常清晰和稳定的 `NaviMode` 枚举/常量定义，包括每个模式的确切行为和预期的仪表响应。

---

## 5. 总结

基于当前的理解，完成上述所有重构、功能改造、测试和文档工作的总工作量初步估计在 **20 到 32 人天** 之间。这对应大约 **4 到 6.5 周** 的开发时间。

**建议**:
*   在开始编码前，务必与仪表端开发团队确认 `NaviMode` 的所有细节以及 Wi-Fi AP 模式下的通信协议。
*   优先进行 Wi-Fi AP 模式的技术验证，以降低该部分的不确定性。
*   分阶段实施和测试，例如先完成核心投屏逻辑重构，再进行 Wi-Fi AP 改造。

该评估会随着对需求和现有代码更深入的理解而更新。 