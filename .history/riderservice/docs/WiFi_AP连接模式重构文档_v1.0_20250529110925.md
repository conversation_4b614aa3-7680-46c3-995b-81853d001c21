# WiFi AP连接模式重构技术文档

## 1. 概述

本文档描述了将RiderService SDK的WiFi连接模式从WiFi P2P（点对点直连）改为WiFi AP（接入点）模式的技术重构方案。

### 1.1 背景
当前系统使用WiFi P2P模式进行设备间的直接连接，但由于兼容性和稳定性问题，需要改为WiFi AP模式。

### 1.2 目标
- 替换WiFi P2P连接机制为WiFi AP模式
- 保持现有API接口不变
- 确保连接的稳定性和兼容性
- 支持多设备连接能力

## 2. 现状分析

### 2.1 当前架构
- **核心类**: `SoftP2pManager.kt` - 负责WiFi P2P连接管理
- **协议支持**: `WifiMode.WIFI_P2P` 和 `WifiMode.WIFI_AP`
- **传输层**: `TcpConnection.kt` - TCP服务器监听30512端口
- **连接管理**: `DisplayNaviManager.kt` - 显示和导航管理
- **状态管理**: 复杂的P2P连接状态机（11个状态）

### 2.2 现有WiFi P2P流程
1. 通过BLE获取仪表WiFi信息
2. 启动WiFi P2P设备发现
3. 连接到指定MAC地址的P2P设备
4. 建立TCP连接（端口30512）
5. 开始数据传输

## 3. AP模式重构方案

### 3.1 整体架构设计

```
[手机端 - AP模式]
       ↓
[WiFi AP管理器] ← 替换 SoftP2pManager
       ↓
[TCP服务器] (保持不变)
       ↓
[数据传输层] (保持不变)
```

### 3.2 核心组件重构

#### 3.2.1 新建 `WiFiApManager.kt`
替换 `SoftP2pManager.kt`，实现AP模式管理：

**主要功能:**
- WiFi热点创建和管理
- 设备连接监听
- 网络状态管理
- IP地址分配管理

**核心接口:**
```kotlin
interface WiFiApManagerListener {
    fun onApEnabled(ssid: String, password: String)
    fun onApDisabled()
    fun onDeviceConnected(deviceInfo: DeviceInfo)
    fun onDeviceDisconnected(deviceInfo: DeviceInfo)
    fun onApStartFailed(reason: Int)
    fun requestWifiInfo()
    fun onWifiState(opened: Boolean)
}
```

#### 3.2.2 状态机简化
将现有的11个P2P状态简化为AP模式状态：

```kotlin
enum class ApConnectionState {
    IDLE,                    // 空闲状态
    STARTING_AP,             // 启动热点中
    AP_ENABLED,              // 热点已启用
    WAITING_CONNECTION,      // 等待设备连接
    DEVICE_CONNECTED,        // 设备已连接
    CONNECTION_FAILED,       // 连接失败
    AP_DISABLED             // 热点已禁用
}
```

## 4. 详细实现方案

### 4.1 WiFiApManager实现

#### 4.1.1 热点配置
```kotlin
private fun createHotspotConfig(ssid: String, password: String): WifiConfiguration {
    return WifiConfiguration().apply {
        SSID = ssid
        preSharedKey = password
        allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
        allowedProtocols.set(WifiConfiguration.Protocol.RSN)
        allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
        allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
    }
}
```

#### 4.1.2 权限管理
需要添加的权限：
```xml
<uses-permission android:name="android.permission.WRITE_SETTINGS" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
```

#### 4.1.3 兼容性处理
- Android 8.0+ 使用WifiManager.LocalOnlyHotspotCallback
- Android 8.0以下使用反射调用WifiManager.setWifiApEnabled

### 4.2 协议层修改

#### 4.2.1 更新WiFi请求协议
```kotlin
// ConnectionManager.kt
fun requestWifiInfo(isReset: Boolean = true) {
    ModuleInject.messageManager.queueOutgoing(
        RiderProtocol.NaviMessageId.MSG_WIFI_INFO_REQUEST_VALUE,
        RiderProtocol.WifiInfoRequest
            .newBuilder()
            .setWifiMode(RiderProtocol.WifiMode.WIFI_AP) // 改为AP模式
            .setIsResetWifi(isReset)
            .build()
    )
}
```

#### 4.2.2 处理AP模式WiFi信息
```kotlin
// RiderService.kt  
private fun handleWifiInfo(msg: ByteArray?) {
    val wifiInfo = RiderProtocol.WifiInfoNotification.parseFrom(msg)
    // AP模式下，手机作为热点，仪表连接到手机
    mConnectionManager.startWifiAp(
        getApplication().applicationContext,
        wifiInfo.name ?: "RiderService_AP",
        wifiInfo.password ?: "12345678"
    )
}
```

### 4.3 网络发现和连接

#### 4.3.1 AP模式连接流程
1. **手机端创建WiFi热点**
   - SSID: 从协议获取或默认"RiderService_AP"
   - 密码: 从协议获取或默认密码
   - IP: ************ (默认AP网关)

2. **仪表端连接热点**
   - 扫描并连接到指定SSID
   - 获取DHCP分配的IP地址
   - 建立TCP连接到手机端

3. **TCP连接建立**
   - 手机端监听30512端口（保持不变）
   - 仪表端作为客户端连接

#### 4.3.2 IP地址管理
```kotlin
private fun getApIpAddress(): String {
    return try {
        val interfaces = NetworkInterface.getNetworkInterfaces()
        for (intf in interfaces) {
            if (intf.name.contains("ap") || intf.name.contains("wlan1")) {
                for (addr in intf.inetAddresses) {
                    if (addr is Inet4Address && !addr.isLoopbackAddress) {
                        return addr.hostAddress ?: ""
                    }
                }
            }
        }
        "************" // 默认AP网关地址
    } catch (e: Exception) {
        "************"
    }
}
```

### 4.4 错误处理和重连机制

#### 4.4.1 AP启动失败处理
```kotlin
private fun handleApStartFailure(reason: Int) {
    when (reason) {
        WifiManager.LocalOnlyHotspotCallback.ERROR_NO_CHANNEL -> {
            // 重试或降级到其他连接方式
        }
        WifiManager.LocalOnlyHotspotCallback.ERROR_GENERIC -> {
            // 通用错误处理
        }
        WifiManager.LocalOnlyHotspotCallback.ERROR_INCOMPATIBLE_MODE -> {
            // 关闭WiFi后重试
        }
    }
}
```

#### 4.4.2 设备断连重连
```kotlin
private fun handleDeviceDisconnected() {
    // 保持热点开启，等待重新连接
    startReconnectionTimer()
}

private fun startReconnectionTimer() {
    countDownTimer = object : CountDownTimer(30000, 1000) {
        override fun onTick(millisUntilFinished: Long) {
            checkDeviceConnection()
        }
        
        override fun onFinish() {
            // 30秒无连接，关闭热点
            stopWifiAp()
        }
    }.start()
}
```

## 5. 具体文件修改清单

### 5.1 新增文件
- `WiFiApManager.kt` - AP模式管理核心类
- `WiFiApManagerListener.kt` - AP事件监听接口
- `DeviceInfo.kt` - 连接设备信息数据类

### 5.2 修改文件
- `DisplayNaviManager.kt` - 替换P2P管理器为AP管理器
- `ConnectionManager.kt` - 更新WiFi请求协议
- `RiderService.kt` - 修改WiFi信息处理逻辑
- `AndroidManifest.xml` - 添加AP相关权限

### 5.3 删除文件
- `SoftP2pManager.kt` - P2P管理器（保留作为参考）
- `SoftIP2pListener.kt` - P2P监听接口

## 6. 测试方案

### 6.1 单元测试
- AP热点创建和关闭
- 设备连接和断连检测
- IP地址获取和分配
- 错误场景处理

### 6.2 集成测试
- 完整的连接建立流程
- 数据传输稳定性
- 多设备连接能力
- 网络切换场景

### 6.3 兼容性测试
- 不同Android版本（6.0-14）
- 不同设备厂商
- 不同网络环境

## 7. 风险评估和缓解

### 7.1 技术风险
- **权限限制**: Android 8.0+对热点创建有限制
  - **缓解**: 使用LocalOnlyHotspot API
- **设备兼容性**: 不同厂商对AP功能支持差异
  - **缓解**: 实现多种兼容方案
- **网络冲突**: 与现有网络连接冲突
  - **缓解**: 智能网络切换机制

### 7.2 项目风险
- **仪表端配合**: 需要仪表端同步支持AP连接
  - **缓解**: 与仪表端团队密切协作
- **用户体验**: 热点配置可能需要用户干预
  - **缓解**: 自动化配置和用户引导

## 8. 实施计划

### 8.1 第一阶段 (3-4天)
- 创建WiFiApManager基础框架
- 实现AP热点创建和管理
- 基础权限和兼容性处理

### 8.2 第二阶段 (2-3天)
- 替换DisplayNaviManager中的P2P组件
- 更新协议处理逻辑
- 实现连接状态管理

### 8.3 第三阶段 (2-3天)
- 错误处理和重连机制
- 完善日志和调试功能
- 单元测试和集成测试

### 8.4 第四阶段 (2-3天)
- 与仪表端联调
- 性能优化和bug修复
- 文档更新

## 9. 附录

### 9.1 相关Android API
- `WifiManager.LocalOnlyHotspotCallback`
- `WifiManager.setWifiApEnabled()` (反射)
- `NetworkInterface.getNetworkInterfaces()`
- `ConnectivityManager.getActiveNetworkInfo()`

### 9.2 参考资料
- Android WiFi AP开发指南
- WiFi热点兼容性最佳实践
- TCP/IP网络编程指南 