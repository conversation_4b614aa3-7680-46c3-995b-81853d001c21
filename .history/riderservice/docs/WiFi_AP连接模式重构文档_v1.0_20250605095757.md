# WiFi AP连接模式重构技术文档

## 1. 概述

本文档描述了将RiderService SDK的WiFi连接模式从WiFi P2P（点对点直连）改为WiFi AP（接入点）模式的技术重构方案。

### 1.1 背景
当前系统使用WiFi P2P模式进行设备间的直接连接，但由于兼容性和稳定性问题，需要改为WiFi AP模式。

### 1.2 架构说明
**新架构模式**：
- **仪表端（车机）**：作为WiFi热点（AP）+ TCP客户端
- **Android手机端**：作为WiFi客户端，连接到仪表热点 + TCP服务器
- **连接流程**：BLE获取热点信息 → 手机连接热点 → 手机通过BLE发送IP给仪表 → 仪表作为TCP客户端连接手机

### 1.3 目标
- 替换WiFi P2P连接机制为WiFi AP客户端模式
- 保持现有TCP服务器架构不变
- 通过BLE传递手机IP地址给仪表端
- 确保连接的稳定性和兼容性
- 支持Android 6.0-15(API 23-35)的最新实践
- 保持向后兼容性，支持P2P模式回退机制
- 增强并发安全性和资源管理

## 2. 现状分析

### 2.1 当前架构
- **核心类**: `SoftP2pManager.kt` - 负责WiFi P2P连接管理
- **协议支持**: `WifiMode.WIFI_P2P` 和 `WifiMode.WIFI_AP`
- **传输层**: `TcpConnection.kt` - TCP服务器监听30512端口
- **连接管理**: `DisplayNaviManager.kt` - 显示和导航管理
- **状态管理**: 复杂的P2P连接状态机（11个状态）

### 2.2 现有WiFi P2P流程
1. 通过BLE获取仪表WiFi信息
2. 启动WiFi P2P设备发现
3. 连接到指定MAC地址的P2P设备
4. 建立TCP连接（端口30512）
5. 开始数据传输

### 2.3 现有状态机分析
当前`SoftP2pManager`使用复杂的11状态自动连接状态机：
- `AUTOLINK_STATION_WIFI_START` (0) - 进入app时，未获取到其他状态
- `AUTOLINK_STATION_NO_CONNECT` (1) - wifi p2p未连接
- `AUTOLINK_STATION_ALREADY_CONNECTED` (2) - wifi p2p已经连接
- `AUTOLINK_STATION_SEARCH_WIFI` (3) - 搜索wifi，持续10s
- `AUTOLINK_STATION_SEARCH_FAILED` (4) - 搜索wifi重试失败
- `AUTOLINK_STATION_SEARCH_SUCCESS` (5) - wifi搜索成功
- `AUTOLINK_STATION_CONNECT_SUCCESS` (6) - wifi连接成功
- `AUTOLINK_STATION_CONNECT_FAILED` (7) - wifi连接失败，重试3次
- `AUTOLINK_STATION_RECONNECT_FAILED` (8) - wifi连接重试失败
- `AUTOLINK_STATION_AUTOLINK_RESTART` (9) - 已连接状态下的Autolink重启
- `AUTOLINK_STATION_P2P_DEVICE_ERROR` (10) - p2p对等设备错误
- `AUTOLINK_STATION_WIFI_DISABLE` (11) - wifi关闭

新架构需要保持类似的状态管理复杂度以确保连接稳定性。

## 3. 详细架构设计

### 2.3.1 整体系统架构

```mermaid
graph TB
    subgraph "仪表端 (车机)"
        I1[WiFi热点AP]
        I2[TCP客户端]
        I3[BLE服务端]
        I4[数据处理层]
        
        I3 --> I1
        I1 --> I2
        I2 --> I4
    end
    
    subgraph "Android手机端"
        A1[BLE客户端]
        A2[WiFi客户端管理器]
        A3[TCP服务器:30512]
        A4[数据传输层]
        A5[RiderService API]
        
        A1 --> A2
        A2 --> A3
        A3 --> A4
        A4 --> A5
    end
    
    I3 -.BLE连接.-> A1
    I1 -.WiFi连接.-> A2
    I2 -.TCP连接.-> A3
    
    style I1 fill:#e1f5fe
    style A2 fill:#f3e5f5
    style I2 fill:#fff3e0
    style A3 fill:#e8f5e8
```

### 2.3.2 核心组件架构

```mermaid
graph LR
    subgraph "连接管理层"
        CM[ConnectionManager]
        DNM[DisplayNaviManager]
    end
    
    subgraph "网络层"
        WCM[WiFiClientManager]
        TCS[TcpServerConnection]
        NPH[NetworkPermissionHelper]
    end
    
    subgraph "协议层"
        RS[RiderService]
        MP[MessageProtocol]
    end
    
    subgraph "传输层"
        TT[TcpTransport]
        DC[DataConnection]
    end
    
    CM --> DNM
    DNM --> WCM
    DNM --> TCS
    WCM --> NPH
    TCS --> DC
    DC --> TT
    RS --> CM
    RS --> MP
    
    style WCM fill:#e3f2fd
    style TCS fill:#e8f5e8
    style NPH fill:#fce4ec
```

### 2.3.3 连接建立完整流程

```mermaid
sequenceDiagram
    participant App as Android App
    participant BLE as BLE Manager
    participant Instrument as 仪表端
    participant WiFi as WiFiClientManager
    participant TCP as TcpServerConnection
    
    Note over App,Instrument: 1. BLE连接和WiFi信息获取
    App->>BLE: 扫描并连接BLE设备
    BLE->>Instrument: 建立BLE连接
    App->>Instrument: 请求WiFi信息(WIFI_AP模式)
    Instrument->>App: 返回热点信息(SSID,密码)
    
    Note over App,Instrument: 2. WiFi客户端连接
    App->>WiFi: connectToWifi(ssid, password)
    WiFi->>WiFi: 检查权限和WiFi状态
    WiFi->>WiFi: 扫描网络并找到目标热点
    
    alt Android 10+
        WiFi->>WiFi: 使用WifiNetworkSpecifier
    else Android 6.0-9
        WiFi->>WiFi: 使用WifiConfiguration
    end
    
    WiFi->>Instrument: 连接到WiFi热点
    Instrument->>WiFi: 分配IP地址
    WiFi->>App: onWifiConnected(ssid, ip)
    
    Note over App,Instrument: 3. 启动TCP服务器并通知仪表
    App->>TCP: 启动TCP服务器(端口30512)
    TCP->>App: onServerStarted()
    App->>Instrument: 通过BLE发送手机IP地址
    
    Note over App,Instrument: 4. 仪表端TCP客户端连接
    Instrument->>TCP: 连接到手机TCP服务器
    TCP->>App: onClientConnected(transport)
    
    Note over App,Instrument: 5. 数据传输
    App->>Instrument: 发送控制指令
    Instrument->>App: 返回视频/音频数据
```

### 2.3.4 WiFi客户端状态机

```mermaid
stateDiagram-v2
    [*] --> IDLE: 初始化
    
    IDLE --> SCANNING: 开始扫描网络
    SCANNING --> CONNECTING: 找到目标网络
    SCANNING --> CONNECTION_FAILED: 未找到目标网络
    
    CONNECTING --> CONNECTED: 连接成功
    CONNECTING --> CONNECTION_FAILED: 连接失败
    CONNECTING --> RECONNECTING: 连接超时
    
    CONNECTED --> DISCONNECTED: 网络断开
    CONNECTED --> [*]: 主动断开
    
    DISCONNECTED --> RECONNECTING: 自动重连
    RECONNECTING --> CONNECTING: 重连尝试
    RECONNECTING --> CONNECTION_FAILED: 重连次数耗尽
    
    CONNECTION_FAILED --> IDLE: 重置状态
    CONNECTION_FAILED --> [*]: 彻底失败
    
    note right of RECONNECTING: 指数退避策略
    note right of CONNECTION_FAILED: 最多重试5次
```

### 2.3.5 错误处理和恢复流程

```mermaid
flowchart TD
    A["连接异常"] --> B{"异常类型"}
    
    B -->|WiFi断开| C["启动重连机制"]
    B -->|TCP断开| D["重新建立TCP连接"]
    B -->|权限错误| E["请求用户授权"]
    B -->|网络不可用| F["等待网络恢复"]
    
    C --> G{"重连次数检查"}
    G -->|< 5次| H["指数退避延迟"]
    G -->|≥ 5次| I["重连失败"]
    
    H --> J["重新连接WiFi"]
    J --> K{"连接结果"}
    K -->|成功| L["恢复正常"]
    K -->|失败| C
    
    D --> M["获取仪表IP"]
    M --> N["重新建立TCP连接"]
    N --> O{"TCP连接结果"}
    O -->|成功| L
    O -->|失败| P["报告连接错误"]
    
    E --> Q["显示权限请求"]
    Q --> R{"用户授权"}
    R -->|同意| S["重新开始连接"]
    R -->|拒绝| T["功能不可用"]
    
    F --> U["监听网络状态"]
    U --> V{"网络恢复"}
    V -->|是| S
    V -->|否| F
    
    I --> P
    L --> Z["连接正常"]
    P --> Y["显示错误信息"]
    T --> Y
    Y --> End1["结束"]
    Z --> End2["结束"]
    
    style L fill:#c8e6c9
    style P fill:#ffcdd2
    style T fill:#ffcdd2
```

### 2.3.6 Android版本兼容性架构

```mermaid
graph TD
    A[WiFi连接请求] --> B{Android版本检查}
    
    B -->|API ≥ 29 Android 10+| C[现代化API路径]
    B -->|API 23-28 Android 6.0-9| D[传统API路径]
    B -->|API < 23| E[不支持]
    
    subgraph "现代化API (Android 10+)"
        C --> C1[WifiNetworkSpecifier]
        C1 --> C2[NetworkRequest.Builder]
        C2 --> C3[ConnectivityManager.requestNetwork]
        C3 --> C4[NetworkCallback监听]
    end
    
    subgraph "传统API (Android 6.0-9)"
        D --> D1[权限检查]
        D1 --> D2[WiFi网络扫描]
        D2 --> D3[WifiConfiguration配置]
        D3 --> D4[WifiManager连接]
        D4 --> D5[BroadcastReceiver监听]
    end
    
    subgraph "权限管理"
        F[ACCESS_FINE_LOCATION]
        G[CHANGE_WIFI_STATE]
        H[NEARBY_WIFI_DEVICES API 33+]
    end
    
    C4 --> I[连接成功回调]
    D5 --> I
    E --> J[显示不支持提示]
    I --> K[建立TCP连接]
    
    D1 --> F
    D1 --> G
    C --> H
    
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#ffebee
```

## 3. AP客户端模式重构方案

### 3.1 整体架构设计

基于以上详细分析，我们的重构方案采用分层架构，确保各组件职责清晰，易于维护和扩展。

### 3.2 核心组件重构

#### 3.2.1 新建 `WiFiClientManager.kt`
替换 `SoftP2pManager.kt`，实现WiFi客户端连接：

**主要功能:**
- WiFi网络扫描和连接
- 网络状态监控
- 自动重连机制
- 跨版本兼容性处理

**核心接口:**
```kotlin
interface WiFiClientManagerListener {
    fun onWifiConnecting(ssid: String)
    fun onWifiConnected(ssid: String, ipAddress: String)
    fun onWifiDisconnected()
    fun onWifiConnectionFailed(reason: Int)
    fun onNetworkScanComplete(networks: List<ScanResult>)
    fun requestWifiInfo()
    fun onWifiState(opened: Boolean)
}
```

#### 3.2.2 状态机优化
简化的WiFi客户端连接状态：

```kotlin
enum class WifiClientState {
    IDLE,                    // 空闲状态
    SCANNING,               // 扫描网络中
    CONNECTING,             // 连接中
    CONNECTED,              // 已连接
    DISCONNECTED,           // 已断开
    CONNECTION_FAILED,      // 连接失败
    RECONNECTING           // 重连中
}
```

## 4. 详细实现方案

### 4.1 Android版本兼容性处理

#### 4.1.1 Android 10+ (API 29+) 新方式
使用 `WifiNetworkSpecifier` 和 `NetworkRequest`：

```kotlin
@RequiresApi(Build.VERSION_CODES.Q)
private fun connectToWifiModern(ssid: String, password: String) {
    val wifiNetworkSpecifier = WifiNetworkSpecifier.Builder()
        .setSsid(ssid)
        .setWpa2Passphrase(password)
        .build()

    val networkRequest = NetworkRequest.Builder()
        .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
        .setNetworkSpecifier(wifiNetworkSpecifier)
        .build()

    connectivityManager.requestNetwork(networkRequest, networkCallback)
}
```

#### 4.1.2 Android 6.0-9 (API 23-28) 兼容方式
使用 `WifiConfiguration` 和 `WifiManager`：

```kotlin
@SuppressLint("MissingPermission")
private fun connectToWifiLegacy(ssid: String, password: String) {
    val wifiConfig = WifiConfiguration().apply {
        SSID = "\"$ssid\""
        preSharedKey = "\"$password\""
        allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK)
        allowedProtocols.set(WifiConfiguration.Protocol.RSN)
        allowedGroupCiphers.set(WifiConfiguration.GroupCipher.CCMP)
        allowedPairwiseCiphers.set(WifiConfiguration.PairwiseCipher.CCMP)
    }

    val networkId = wifiManager.addNetwork(wifiConfig)
    if (networkId != -1) {
        wifiManager.disconnect()
        wifiManager.enableNetwork(networkId, true)
        wifiManager.reconnect()
    }
}
```

### 4.2 权限管理

#### 4.2.1 必需权限
```xml
<!-- WiFi基础权限 -->
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />

<!-- 位置权限（WiFi扫描需要） -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

<!-- Android 13+ 的附近WiFi设备权限 -->
<uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" 
    android:usesPermissionFlags="neverForLocation"
    tools:targetApi="s" />
```

#### 4.2.2 动态权限申请
```kotlin
private fun checkAndRequestPermissions(): Boolean {
    val requiredPermissions = mutableListOf<String>()
    
    if (ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) 
        != PackageManager.PERMISSION_GRANTED) {
        requiredPermissions.add(Manifest.permission.ACCESS_FINE_LOCATION)
    }
    
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.NEARBY_WIFI_DEVICES)
            != PackageManager.PERMISSION_GRANTED) {
            requiredPermissions.add(Manifest.permission.NEARBY_WIFI_DEVICES)
        }
    }
    
    return requiredPermissions.isEmpty()
}
```

### 4.3 网络扫描和连接

#### 4.3.1 WiFi网络扫描
```kotlin
@SuppressLint("MissingPermission")
private fun startWifiScan() {
    if (!wifiManager.isWifiEnabled) {
        listener.onWifiState(false)
        return
    }
    
    updateState(WifiClientState.SCANNING)
    
    val scanReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action == WifiManager.SCAN_RESULTS_AVAILABLE_ACTION) {
                val scanResults = wifiManager.scanResults
                listener.onNetworkScanComplete(scanResults)
                context.unregisterReceiver(this)
            }
        }
    }
    
    context.registerReceiver(scanReceiver, IntentFilter(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION))
    wifiManager.startScan()
}
```

#### 4.3.2 目标网络连接
```kotlin
fun connectToTargetNetwork(ssid: String, password: String) {
    Log.d(TAG, "Connecting to target network: $ssid")
    
    if (currentState == WifiClientState.CONNECTED && getCurrentSsid() == ssid) {
        Log.d(TAG, "Already connected to target network")
        return
    }
    
    updateState(WifiClientState.CONNECTING)
    
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        connectToWifiModern(ssid, password)
    } else {
        connectToWifiLegacy(ssid, password)
    }
}
```

### 4.4 网络状态监控

#### 4.4.1 NetworkCallback实现
```kotlin
private val networkCallback = object : ConnectivityManager.NetworkCallback() {
    override fun onAvailable(network: Network) {
        super.onAvailable(network)
        Log.d(TAG, "Network available: $network")
        
        // 绑定应用到此网络（Android 10+）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            connectivityManager.bindProcessToNetwork(network)
        }
        
        val ipAddress = getNetworkIpAddress(network)
        updateState(WifiClientState.CONNECTED)
        listener.onWifiConnected(targetSsid, ipAddress)
    }
    
    override fun onLost(network: Network) {
        super.onLost(network)
        Log.d(TAG, "Network lost: $network")
        updateState(WifiClientState.DISCONNECTED)
        listener.onWifiDisconnected()
        
        // 启动重连
        startReconnection()
    }
    
    override fun onUnavailable() {
        super.onUnavailable()
        Log.d(TAG, "Network unavailable")
        updateState(WifiClientState.CONNECTION_FAILED)
        listener.onWifiConnectionFailed(-1)
    }
}
```

#### 4.4.2 IP地址获取
```kotlin
private fun getNetworkIpAddress(network: Network): String {
    return try {
        val linkProperties = connectivityManager.getLinkProperties(network)
        linkProperties?.linkAddresses?.find { 
            it.address is Inet4Address 
        }?.address?.hostAddress ?: ""
    } catch (e: Exception) {
        Log.e(TAG, "Error getting IP address", e)
        ""
    }
}
```

### 4.5 自动重连机制

#### 4.5.1 重连策略
```kotlin
private fun startReconnection() {
    if (currentState == WifiClientState.RECONNECTING) {
        return
    }
    
    updateState(WifiClientState.RECONNECTING)
    reconnectionAttempts++
    
    val delay = calculateBackoffDelay(reconnectionAttempts)
    
    reconnectionHandler.postDelayed({
        if (reconnectionAttempts <= MAX_RECONNECTION_ATTEMPTS) {
            Log.d(TAG, "Reconnection attempt: $reconnectionAttempts")
            connectToTargetNetwork(targetSsid, targetPassword)
        } else {
            Log.e(TAG, "Max reconnection attempts reached")
            updateState(WifiClientState.CONNECTION_FAILED)
            listener.onWifiConnectionFailed(RECONNECTION_FAILED)
        }
    }, delay)
}

private fun calculateBackoffDelay(attempt: Int): Long {
    // 指数退避：1s, 2s, 4s, 8s, 16s
    return minOf(1000L * (1 shl (attempt - 1)), 16000L)
}
```

## 5. 协议层修改

### 5.1 更新WiFi请求协议
```kotlin
// ConnectionManager.kt
fun requestWifiInfo(isReset: Boolean = true) {
    Log.d(TAG, "requestWifiInfo ${_connectionStatus.value.btStatus}")
    if (_connectionStatus.value.btStatus is BleStatus.DeviceConnected) {
        ModuleInject.messageManager.queueOutgoing(
            RiderProtocol.NaviMessageId.MSG_WIFI_INFO_REQUEST_VALUE,
            RiderProtocol.WifiInfoRequest
                .newBuilder()
                .setWifiMode(RiderProtocol.WifiMode.WIFI_AP) // 改为AP模式
                .setIsResetWifi(isReset)
                .build()
        )
    }
}
```

### 5.2 处理仪表端热点信息
```kotlin
// RiderService.kt  
private fun handleWifiInfo(msg: ByteArray?) {
    Log.d("connect analysis:", "wifi info response (AP client mode)::${TimeUtils.getCurrentTimeStr()}")
    val wifiInfo = RiderProtocol.WifiInfoNotification.parseFrom(msg)
    
    // 连接到仪表端的WiFi热点
    val instrumentSsid = wifiInfo.name ?: ""
    val instrumentPassword = wifiInfo.password ?: ""
    
    if (instrumentSsid.isNotEmpty()) {
        mConnectionManager.connectToInstrumentWifi(
            getApplication().applicationContext,
            instrumentSsid,
            instrumentPassword
        )
    } else {
        Log.e(TAG, "Invalid WiFi info received from instrument")
    }
}
```

## 6. TCP连接调整

### 6.1 TCP服务器模式保持不变
基于用户补充的架构信息，手机端继续作为TCP服务器，仪表端作为TCP客户端连接：

```kotlin
// 保持现有的TCP服务器架构，主要修改连接通知逻辑
class TcpServerConnection : DataConnection() {
    private var serverSocket: ServerSocket? = null
    private var listenPort: Int = 30512
    
    override fun onStart() {
        startTcpServer()
    }
    
    private fun startTcpServer() {
        try {
            serverSocket = ServerSocket(listenPort)
            Log.d(TAG, "TCP server started on port $listenPort")
            
            // 启动服务器后，通过BLE通知仪表端手机的IP地址
            notifyInstrumentWithIpAddress()
            
            AcceptThread("TCP Server").start()
        } catch (e: IOException) {
            Log.e(TAG, "Failed to start TCP server", e)
            mCallback?.onDisconnected()
        }
    }
    
    private fun notifyInstrumentWithIpAddress() {
        val phoneIp = getPhoneIpAddress()
        if (phoneIp.isNotEmpty()) {
            // 通过BLE发送IP地址给仪表端
            sendIpAddressThroughBle(phoneIp, listenPort)
        }
    }
    
    private fun getPhoneIpAddress(): String {
        // 获取手机在WiFi网络中的IP地址
        return WiFiUtils.getWifiIpAddress()
    }
    
    private fun sendIpAddressThroughBle(ipAddress: String, port: Int) {
        // 通过BLE消息管理器发送IP地址给仪表端
        ModuleInject.messageManager.queueOutgoing(
            RiderProtocol.NaviMessageId.MSG_PHONE_IP_NOTIFICATION_VALUE,
            RiderProtocol.PhoneIpNotification
                .newBuilder()
                .setIpAddress(ipAddress)
                .setPort(port)
                .build()
        )
    }
    
    inner class AcceptThread(name: String) : Thread(name) {
        override fun run() {
            try {
                Log.d(TAG, "Waiting for instrument TCP client connection...")
                val clientSocket = serverSocket?.accept()
                
                if (clientSocket != null) {
                    Log.d(TAG, "Instrument connected from: ${clientSocket.remoteSocketAddress}")
                    val transport = TcpTransport(clientSocket)
                    mCallback?.onConnected(transport)
                }
            } catch (e: IOException) {
                Log.e(TAG, "Error accepting TCP connection", e)
                mCallback?.onDisconnected()
            }
        }
    }
}
```

### 6.2 协议层增加IP通知消息
需要在协议定义中增加手机IP地址通知消息：

```protobuf
// 新增消息类型
message PhoneIpNotification {
    string ip_address = 1;    // 手机IP地址
    int32 port = 2;           // TCP服务器端口
}
```

## 7. 具体文件修改清单

### 7.1 新增文件
- `WiFiClientManager.kt` - WiFi客户端连接管理
- `WiFiClientManagerListener.kt` - WiFi客户端事件监听
- `NetworkPermissionHelper.kt` - 网络权限助手

### 7.2 修改文件
- `DisplayNaviManager.kt` - 替换P2P为WiFi客户端
- `ConnectionManager.kt` - 更新连接逻辑
- `RiderService.kt` - 修改WiFi信息处理
- `TcpConnection.kt` - 增加IP地址通知逻辑
- `AndroidManifest.xml` - 添加网络权限
- `协议文件` - 增加PhoneIpNotification消息定义

### 7.3 删除文件
- `SoftP2pManager.kt` - P2P管理器
- `SoftIP2pListener.kt` - P2P监听接口

## 8. 测试方案

### 8.1 单元测试
- WiFi网络扫描功能
- 不同版本的连接兼容性
- 重连机制验证
- 权限处理测试

### 8.2 集成测试
- 完整连接流程测试
- 网络切换场景
- 异常恢复测试
- 性能和稳定性测试

### 8.3 兼容性测试
- Android 6.0-14版本测试
- 不同设备厂商兼容性
- 各种网络环境测试

## 9. 风险评估和缓解

### 9.1 技术风险
- **权限复杂性**: Android版本间权限差异大
  - **缓解**: 完善的权限检查和引导机制
- **网络API变化**: Android 10+网络API变化较大
  - **缓解**: 分版本实现，充分测试
- **自动连接限制**: 某些厂商限制自动WiFi连接
  - **缓解**: 提供手动连接指引

### 9.2 项目风险
- **仪表端稳定性**: 依赖仪表端热点稳定性
  - **缓解**: 强化重连和错误恢复机制
- **用户体验**: 可能需要用户手动授权
  - **缓解**: 优化权限申请流程和用户引导

## 10. 实施计划

### 10.1 第一阶段 (4-5天)
- 创建WiFiClientManager基础框架
- 实现Android版本兼容处理
- 权限管理和动态申请

### 10.2 第二阶段 (3-4天)
- 网络扫描和连接逻辑
- 状态监控和回调处理
- TCP客户端连接实现

### 10.3 第三阶段 (2-3天)
- 自动重连机制
- 错误处理和日志优化
- 单元测试实现

### 10.4 第四阶段 (3-4天)
- 替换现有P2P组件
- 协议层适配
- 集成测试和bug修复

## 11. 附录

### 11.1 相关Android API
- `ConnectivityManager.NetworkCallback` (Android 6.0+)
- `WifiNetworkSpecifier` (Android 10+)
- `NetworkRequest.Builder` (Android 6.0+)
- `WifiConfiguration` (已废弃但仍需兼容)

### 11.2 参考资料
- Android网络连接最佳实践
- WiFi连接跨版本兼容指南
- Android权限管理指南 