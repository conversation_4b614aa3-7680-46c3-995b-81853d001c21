# RiderService SDK Wi-Fi 连接重构设计文档 (P2P 到 AP 模式)

> 版本：0.1 (修订版 - App 作为 TCP 服务端)
> 作者：AI 自动生成（请根据实际情况补充、修订）
> 基于总体设计文档：`设计文档_投屏重构_v0.5_App发起仪表执行.md`

---

## 1. 背景与目标

本文档旨在详细阐述将 RiderService SDK 中的 Wi-Fi 连接方式从当前的 Wi-Fi Direct (P2P) 模式重构为 Wi-Fi AP (Access Point) 模式的技术方案。目标是提高连接的稳定性、简化连接流程，并可能改善在某些环境下的兼容性。

**核心 уточненные предположения:**
1.  **智能仪表创建 AP**: 智能仪表硬件将作为 Wi-Fi AP 热点提供方。
2.  **手机 App (SDK) 连接 AP**: RiderService SDK 将负责扫描、连接到由仪表提供的 AP。
3.  **AP 凭证交换**: 仪表的 Wi-Fi AP的SSID和密码 (PSK) 将在 BLE 连接建立后，通过安全的 BLE 通道从仪表发送给手机 App (SDK)。
4.  **手机 App (SDK) 作为 TCP 服务端**: 连接到仪表 AP 后，SDK 将在指定端口 (30512) 启动 TCP 服务端。
5.  **仪表作为 TCP 客户端**: 仪表将连接到 SDK 在 AP 网络中启动的 TCP 服务。
6.  **SDK IP 地址通知**: SDK 连接到仪表 AP 并获取到自身 IP 地址后，将通过 BLE 将此 IP 地址通知给仪表，以便仪表进行 TCP 连接。

---

## 2. 现有 Wi-Fi P2P 逻辑简述 (待补充)

(此处需要简要描述当前 SDK 中 Wi-Fi P2P 的实现方式、关键类和流程。例如，如何发现对端、如何协商组所有者 GO、如何建立连接、如何进行数据传输等。这有助于理解需要修改的部分。如果信息不足，标记为待调研。)

*   当前 P2P 连接主要由 `ConnectionManager` 和可能的 `DisplayNaviManager` (如果涉及 GO 协商) 处理。
*   涉及 `WifiP2pManager` API 的使用。
*   ...

---

## 3. 提议的 Wi-Fi AP 模式逻辑

### 3.1 总体流程

```mermaid
sequenceDiagram
    participant App as 手机 App
    participant SDK as RiderService SDK
    participant Instrument as 智能仪表

    App->>SDK: connectBle(device)
    SDK->>Instrument: 建立 BLE 连接
    Instrument-->>SDK: BLE 连接成功
    Instrument-->>SDK: 发送 Wi-Fi AP SSID 和密码 (via BLE)
    SDK->>SDK: 存储 AP 凭证
    SDK->>App: onBleConnected()
    SDK->>SDK: (内部逻辑) 开始连接仪表 Wi-Fi AP
    SDK->>Android OS: 使用凭证扫描并连接指定 SSID 的 AP
    Android OS-->>SDK: Wi-Fi 连接成功, SDK 获取自身在 AP 网络的 IP 地址
    SDK->>SDK: 启动 TCP ServerSocket (端口 30512)
    SDK->>Instrument: 发送 SDK 的 IP 地址和端口号 (30512) (via BLE)
    Instrument->>SDK: (仪表) 连接到 SDK 的 TCP ServerSocket (IP:Port)
    SDK->>SDK:接受仪表 TCP 连接
    SDK->>App: onRiderServiceConnected() / onWiFiDataChannelReady() (或类似回调)
    App->>SDK: requestNaviMode(targetMode)
    SDK->>Instrument: 发送 NaviMode (via Wi-Fi 数据通道或 BLE)
    Instrument-->>SDK: 响应 / 状态变化
    SDK->>App: 相关投屏回调
```

### 3.2 AP 凭证获取与存储
*   (同前) 当 BLE 连接成功后，SDK 应期望从仪表接收包含 Wi-Fi AP 的 SSID 和密码 (PSK) 的特定 BLE 消息/特征值。
*   (同前) SDK 内部应安全地存储这些凭证，至少在当前连接会话中有效。

### 3.3 连接到仪表的 AP
*   (同前) `ConnectionManager` 将负责此过程。
*   (同前) SDK 收到 AP 凭证后，使用 Android 的 Wi-Fi API 连接到指定的 Wi-Fi 网络。
*   连接成功后，SDK 必须获取到自身在该 AP 网络下的 IP 地址 (例如，通过 `WifiInfo.getIpAddress()`)。

### 3.4 IP 地址和端口
*   **仪表 IP (作为AP网关)**: 仪表作为 AP 时，其 IP 地址通常是固定的网关地址。SDK 连接此 AP。
*   **SDK (手机) IP**: SDK 连接上仪表 AP 后，会由 DHCP 获取到一个 IP 地址。**此 IP 地址是仪表连接 SDK TCP 服务端的关键。**
*   **SDK 服务端口**: SDK 将在此 IP 地址上监听 **TCP 端口 30512**。
*   **SDK IP 地址通知机制**: SDK 获取到自身 IP 后，必须通过 BLE 将此 IP 地址和端口号 (30512) 发送给仪表。

### 3.5 数据通道建立
*   SDK 成功连接到仪表的 Wi-Fi AP 并获取到自身 IP 地址后：
    1.  `ConnectionManager` (或其委托的组件) 在 SDK 本机 IP 和预定义端口 **30512** 上启动一个 `ServerSocket`。
    2.  `ConnectionManager` 通过 BLE 将 SDK 的 IP 地址和端口 30512 发送给仪表。
    3.  SDK 等待仪表作为 TCP 客户端连接到此 `ServerSocket`。
    4.  一旦仪表连接成功 (`serverSocket.accept()` 返回一个 `Socket`)，数据通道即建立。

### 3.6 错误处理与重连
*   (基本同前) AP 扫描失败, 连接认证失败, 连接后断开。
*   新增错误场景：
    *   SDK 启动 `ServerSocket` 失败 (例如端口被占用)。
    *   通过 BLE 发送 SDK IP 给仪表失败。
    *   仪表连接 SDK 的 `ServerSocket` 超时或失败。
*   `ConnectionManager` 需要实现相应的重试逻辑和状态回调。

---

## 4. 组件职责变更

### 4.1 `ConnectionManager.kt`
*   **主要变更方**。
*   (同前) 移除所有 Wi-Fi P2P 相关逻辑。
*   (同前) 新增通过 BLE 接收和处理 AP 凭证的逻辑。
*   (同前) 新增使用 Android Wi-Fi API 连接到指定 AP 的逻辑。
*   **新增**: 获取本机在仪表 AP 网络中的 IP 地址的逻辑。
*   **新增**: 启动 TCP `ServerSocket` 在指定端口 (30512) 并管理其生命周期的逻辑 (`bind`, `listen`, `accept`, `close`)。
*   **新增**: 通过 BLE 将本机 IP 和服务端口号发送给仪表的逻辑。
*   (同前) 管理 Wi-Fi AP 连接的生命周期：连接、断开、状态监控、错误处理、重连。
*   (同前) 更新其内部状态机以反映新的连接流程。
*   (同前) 继续负责将 `NaviMode` (来自 `RiderService`) 发送给仪表，现在主要通过已建立的 Wi-Fi Socket 通道。

### 4.2 `RiderService.kt`
*   (同前) 对外的 API 可能影响不大。内部调用 `ConnectionManager` 的逻辑调整。

### 4.3 `DisplayNaviManager.kt` (如果存在并处理 P2P)
*   (同前) P2P 相关逻辑将被移除或重构。

### 4.4 `AutolinkControl.kt` (或类似底层协议封装类)
*   如果它直接处理 Socket 通信，现在需要调整为使用由 `ConnectionManager` 的 `ServerSocket.accept()` 返回的 `Socket` 实例。

### 4.5 BLE 通信协议
*   (同前) 需要 BLE 服务/特征，用于仪表向 SDK 传输 Wi-Fi AP 的 SSID 和密码。
*   **新增**: 需要 BLE 服务/特征（或新的消息类型），用于 SDK 向仪表发送其 IP 地址和监听的 TCP 端口号 (30512)。
*   (同前，可选) 新增 BLE 消息，用于 SDK 通知仪表 Wi-Fi 连接状态。

---

## 5. 配置变更

*   (同前) 权限变更。
*   端口号 30512 应作为常量配置。

---

## 6. 关键流程详细描述

### 6.1 初始化与 BLE 连接 (同总体设计)

### 6.2 AP 凭证交换与存储
1.  (同前) SDK 与仪表 BLE 连接成功。
2.  (同前) SDK 监听仪表特定 BLE 特征值。
3.  (同前) 仪表发送 AP 的 SSID 和 PSK。
4.  (同前) `ConnectionManager` 解析并存储凭证。

### 6.3 连接仪表 AP、启动服务端并等待仪表连接
1.  `ConnectionManager` 使用获取到的 SSID 和 PSK 构建并请求连接到仪表 AP。
2.  通过 `ConnectivityManager.NetworkCallback` (或广播) 监听网络连接结果。
3.  **Wi-Fi AP 连接成功**:
    *   `ConnectionManager` 获取本机在当前 Wi-Fi 网络下的 IP 地址。
    *   `ConnectionManager` 在本机 IP 和端口 30512 上启动 TCP `ServerSocket`，并开始监听。
    *   `ConnectionManager` 通过 BLE 将本机的 {IP 地址, 端口 30512} 发送给仪表。
    *   等待仪表连接到此 `ServerSocket`。
    *   当 `serverSocket.accept()` 成功返回一个客户端 `Socket` 时，数据通道建立。
    *   `ConnectionManager` 更新内部状态，通知 `RiderService` 数据通道就绪。
4.  **失败 (AP连接失败/获取IP失败/ServerSocket启动失败/BLE发送IP失败/仪表连接超时等)**:
    *   `ConnectionManager` 记录错误，执行重试策略或回调错误状态给 `RiderService`。

### 6.4 Wi-Fi 断开与重连
1.  (同前) `NetworkCallback` 检测到网络断开 (`onLost`) 或仪表 TCP 连接断开。
2.  (同前) `ConnectionManager` 更新状态，通知 `RiderService` Wi-Fi/数据通道断开。
3.  (同前) 根据重连策略，尝试重新执行 6.3 中的步骤 (可能需要重新通过BLE发送IP，如果仪表也认为连接已断开并需要重新获取)。
4.  (同前) 如果 BLE 也断开，整个流程从 BLE 重连开始。

---

## 7. 风险与兼容性

*   (同前) Android 版本差异, 权限, 后台连接, 仪表 AP 实现的稳定性, 多网络处理。
*   **新增风险**: SDK 通过 BLE 发送 IP 给仪表的可靠性。如果此消息丢失或仪表未正确处理，TCP 连接将无法建立。需要确认 BLE 通信的可靠性机制（如带响应的写入）。
*   **新增风险**: 手机防火墙或安全软件可能阻止入站 TCP 连接到端口 30512。虽然在连接到对方 AP 的局域网内通常不是问题，但仍需注意。

---

## 8. 待讨论与确认的点

*   **Q1: (不变)** 仪表创建 AP 的具体参数是固定的还是动态生成的？
*   **Q2: (修订)** 仪表如何确保在收到 SDK 的 IP 地址 (via BLE) 后，能及时并正确地发起 TCP 连接到该 IP 和端口 30512？是否有超时或重试机制？
*   **Q3: (已确认部分)** 数据传输通过 TCP，端口号 30512。 (还需确认是否有其他控制端口或UDP通道需求)
*   **Q4: (不变)** Wi-Fi 断开或 TCP 连接断开后的具体重连策略？
*   **Q5: (不变)** 是否需要在 BLE 断开后自动断开 Wi-Fi AP 连接 / 停止 TCP 服务端？
*   **Q6: (修订)** `NaviMode` 等控制信令是通过已建立的 Wi-Fi TCP Socket 发送，还是继续通过 BLE 发送？ (既然已建立 TCP，建议优先 TCP)
*   **Q7: (新增)** SDK 向仪表发送其 IP 地址和端口的 BLE 通信机制是怎样的？ (例如，使用哪个具体的 BLE 服务/特征？写入方式是 WriteWithResponse 还是 WriteWithoutResponse?)
*   **Q8: (新增)** 如果 SDK 获取到的自身在 AP 网络中的 IP 地址发生变化（例如 DHCP 租约更新），是否需要重新通过 BLE 通知仪表新的 IP？

---

该文档为初步设计，具体实现细节待进一步调研和用户确认上述问题后进行完善。 